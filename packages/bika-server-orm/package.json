{"name": "@bika/server-orm", "description": "Database, Cloud Configurations", "author": "", "dependencies": {"@aws-sdk/client-s3": "^3.521.0", "@aws-sdk/s3-request-presigner": "^3.521.0", "@bika/contents": "workspace:*", "@bika/dev-content-server": "workspace:*", "@bika/types": "workspace:*", "@mdx-js/esbuild": "^3.0.1", "@prisma/client": "6.0.1", "adm-zip": "^0.5.10", "axios": "^1.9.0", "basenext": "workspace:*", "connection-string": "^4.4.0", "gray-matter": "^4.0.3", "lodash": "^4.17.21", "minio": "7.1.3", "mongoose": "^8.15.1", "nanoid": "^5.0.4", "pluralize": "^8.0.0", "react-dom": "^18.3.1", "react-markdown": "^9.0.1", "redis": "^4.6.15", "redlock": "5.0.0-beta.2", "rehype-highlight": "^7.0.0", "remark-gfm": "^4.0.0", "sharelib": "workspace:*", "stripe": "^15.4.0", "zod": "^3.24.5", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@types/adm-zip": "^0.5.5", "@types/bun": "^1.0.7", "@types/node": "^20.11.24", "@types/react-dom": "^18", "dotenv-cli": "^7.3.0", "eslint": "^9", "prisma": "6.0.1", "prisma-dbml-generator": "^0.10.0", "prisma-docs-generator": "^0.8.0", "prisma-erd-generator": "^1.11.2", "prisma-markdown": "^1.0.9"}, "exports": {".": "./index.ts", "./bikafile": "./bikafile/bikafile.ts", "./managers/*": "./managers/*.ts", "./stripe": "./stripe/index.ts", "./mongo": "./mongo/index.ts", "./mongo/*": "./mongo/*.ts", "./utils": "./utils/index.ts", "./search": "./search/index.ts", "./content/*": "./content/*.ts", "./content": "./content/index.ts"}, "keywords": [], "license": "ISC", "main": "index.ts", "prisma": {"seed-bak": "node --no-warnings --loader ts-node/esm prisma/seed.ts", "seed": "npm run __seed-prisma"}, "private": true, "scripts": {"lint": "eslint", "__seed-prisma": "bun prisma/seed/_seed-bun.ts && tsx prisma/seed/_seed-tsx.ts", "db:gen": "prisma generate", "db:migrate": "dotenv -e ../../apps/web/.env.local -- prisma migrate dev", "db:deploy": "dotenv -e ../../apps/web/.env.local -- prisma migrate deploy", "db:reset": "dotenv -e ../../apps/web/.env.local -- prisma migrate reset", "db:reset:force": "dotenv -e ../../apps/web/.env.local -- prisma migrate reset --force", "db:seed": "dotenv -e ../../apps/web/.env.local -- prisma db seed", "stripe:excel": "dotenv -e ../../apps/web/.env.local -- bun stripe/seed/excel.ts", "stripe": "pnpm run stripe:excel", "postinstall": "prisma generate --generator client", "mmdc": "mmdc", "test": "dotenv -e ../../apps/web/.env.local -- bun test"}, "version": "1.9.1-alpha.1"}