import { z } from 'zod';
import { NodeResourceTypeSchema } from '@bika/types/node/bo';
import { TemplateVisibilityEnumSchema } from '@bika/types/template/bo';
import { FeatureAbilityConfigDetailSchema } from '@bika/types/website/bo';

export const SearchIndexTypes = [
  // 'ACCESS_LOG',
  // 'TRACK_LOG',
  'TEMPLATE_REPO',
  'PRODUCT_FEATURE',
  // URL -> Web Page
  'WEB_PAGE',

  // 节点 + 资源基础信息，toBO获得，等同Bikafile导出，包含所有内容，比如Node + Database + View + Records
  'NODE_RESOURCE',

  'RESOURCE_CONTENT',
] as const;

export const SearchIndexTypeSchema = z.enum(SearchIndexTypes);
export type SearchIndexType = z.infer<typeof SearchIndexTypeSchema>;

export const TemplateRepoIndexSchema = z.object({
  type: z.literal(SearchIndexTypeSchema.enum.TEMPLATE_REPO),
  templateId: z.string(),
  content: z.string(),
  visibility: TemplateVisibilityEnumSchema, // 强制不为空，默认public
});

export type TemplateRepoIndex = z.infer<typeof TemplateRepoIndexSchema>;

export const ProductFeatureSearchIndexSchema = FeatureAbilityConfigDetailSchema.extend({
  type: z.literal(SearchIndexTypeSchema.enum.PRODUCT_FEATURE),
  locale: z.string(),
});

// 索引产品功能，主要将name、description、url的等索引方便搜索
export type ProductFeatureIndex = z.infer<typeof ProductFeatureSearchIndexSchema>;

// 内在的内容Content
const ResourceContent = z.discriminatedUnion('contentType', [
  z.object({
    contentType: z.literal('DATABASE_RECORD'),
    recordId: z.string(),
  }),
  z.object({
    contentType: z.literal('DOCUMENT_MARKDOWN'),
  }),
  z.object({
    contentType: z.literal('ATTACHMENT_FILE'),
    fileType: z.enum(['PDF', 'DOC', 'XLS', 'PPT', 'ZIP', 'OTHER']),
  }),
]);

export const ResourceContentSearchIndexSchema = ResourceContent.and(
  z.object({
    type: z.literal(SearchIndexTypeSchema.enum.RESOURCE_CONTENT),
    nodeId: z.string(),
    data: z.string(),
    // text 的高亮显示
    titleSummary: z.string().optional(),
  }),
);

export const NodeResourceSearchIndexSchema = z.object({
  type: z.literal(SearchIndexTypeSchema.enum.NODE_RESOURCE),
  id: z.string(), // nodeId
  parents: z.string(), // node parents example {id: fold, name: {'en': 'test}}[]
  nodeResource: z.string(), // NodeResourceSchema,

  // 汇总的内容，通常用于搜索结果的的纯文字“高亮显示”，不同的节点有不同的内容
  // 比如，文档节点，这里就是放markdown的内容，搜索结果就能对文档的内容进行高亮
  contentSummary: z.string().optional(),
  // text 的高亮显示
  titleSummary: z.string().optional(),
  resourceType: NodeResourceTypeSchema.optional(),
});

export const WebPageSearchIndexSchema = z.object({
  type: z.literal(SearchIndexTypeSchema.enum.WEB_PAGE),
  url: z.string(),
  title: z.string().optional(),
  content: z.string(),
});

export const SearchIndexSchema = z.union([
  NodeResourceSearchIndexSchema,
  ProductFeatureSearchIndexSchema,
  TemplateRepoIndexSchema,
  ResourceContentSearchIndexSchema,
  WebPageSearchIndexSchema,
]);
export type SearchIndex = z.infer<typeof SearchIndexSchema>;

/**
 * 支持的 Search Index 类型，具体的data 馅肉
 */
// export type SearchIndexDataMapping = {
//   // @deprecated 改到db.log
//   ACCESS_LOG: AuditLog;
//   // @deprecated 改到db.log
//   TRACK_LOG: TrackLog;
//   TEMPLATE_REPO: TemplateRepoIndex; // 不放字段，全文搜索
//   PRODUCT_FEATURE: ProductFeatureIndex;
//   // NODE_RESOURCE: NodeResourceIndex;
// };
