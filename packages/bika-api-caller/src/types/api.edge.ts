import { z } from 'zod';

export const APIEdgeCommandTypeSchema = z.union([
  z.literal('health-check'),
  z.literal('telegram:message'),
  z.literal('other'),
]);

// Params
export const APIEdgeTelegramMessageCommandSchema = z.object({
  command: z.literal('telegram:message'),
  message: z.object({
    text: z.string().optional(),
    voice: z.any().optional(),
  }),
  chatId: z.string(),
  // Telegram User ID
  userId: z.string(),
});

export const APIEdgeHealthCheckCommandSchema = z.object({
  command: z.literal('health-check'),
});

export const APIEdgeCommandSchema = z.discriminatedUnion('command', [
  APIEdgeTelegramMessageCommandSchema,
  APIEdgeHealthCheckCommandSchema,
]);

export type IAPIEdgeCommand = z.infer<typeof APIEdgeCommandSchema>;

// Response
export const APIEdgeCommandTelegramMessageResponseSchema = z.object({
  message: z.string(),
});

export type IEdgeCommandTelegramMessageResponse = z.infer<typeof APIEdgeCommandTelegramMessageResponseSchema>;

export const APIEdgeCommandResponseSchema = z.union([
  APIEdgeCommandTelegramMessageResponseSchema,
  APIEdgeHealthCheckCommandSchema,
]);
export type IEdgeCommandResponse = z.infer<typeof APIEdgeCommandResponseSchema>;

// /api/edge

export type APIEdgeCommandType = z.infer<typeof APIEdgeCommandTypeSchema>;

export const APIEdgeRequestSchema = z.object({
  edgeToken: z.string(),
  data: APIEdgeCommandSchema,
});

export const APIEdgeResponseSchema = z.object({
  code: z.number(),
  data: APIEdgeCommandResponseSchema,
});
