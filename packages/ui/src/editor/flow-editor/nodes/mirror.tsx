'use client';

import { Handle, Position } from '@xyflow/react';
import React from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import type { Database } from '@bika/types/database/bo';
import type { FormBO } from '@bika/types/form/bo';
import type { EditorScreenProps } from '@bika/types/template/type';
import { FieldIconMapFieldType } from '@bika/ui/database/record-detail';
import { Stack } from '@bika/ui/layouts';
import { NodeCard } from '../card';
import { NodeHeader } from '../header';
import { NodePanel } from '../node-panel';

interface Props {
  data: FormBO & {
    onClick?: (data: EditorScreenProps) => void;
    fields?: Database['fields'];
  };
}

export function MirrorNode(props: Props) {
  const { data } = props;
  const { i } = useLocale();
  return (
    <NodePanel
      onClick={
        props.data.onClick
          ? () => {
              props.data.onClick?.({
                screenType: 'NODE_RESOURCE',
                resourceType: 'MIRROR',
                nodeId: data.id || (data.templateId as string),
              });
            }
          : undefined
      }
    >
      <Handle type="source" position={Position.Right} isConnectable={false} />
      <Handle type="target" position={Position.Left} isConnectable={false} />
      <NodeHeader type="MIRROR" title={i(data.name)} description={i(data.description)} />
      <Stack>
        {(data.fields || []).map((field, index) => {
          const Icon = FieldIconMapFieldType[field.type];
          return (
            <NodeCard
              onClick={() => {
                props.data.onClick?.({
                  screenType: 'DATABASE_FIELD',
                  databaseId: data.templateId,
                  fieldId: field.templateId as string,
                });
              }}
              key={index}
              space={8}
              icon={
                <Stack
                  alignItems={'center'}
                  justifyContent={'center'}
                  sx={{
                    width: 24,
                    height: 24,
                    borderRadius: 4,
                    backgroundColor: 'var(--bg-controls)',
                  }}
                >
                  {Icon}
                </Stack>
              }
              name={i(field.name)}
            />
          );
        })}
      </Stack>
    </NodePanel>
  );
}
