import React, { ReactNode } from 'react';
import { Stack, Box } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/texts';

interface Props {
  icon: ReactNode;
  name: string;
  description?: string;
  line?: boolean;
  space?: number;
  children?: ReactNode;
  onClick?: () => void;
}
export function NodeCard(props: Props) {
  return (
    <Stack
      onClick={(e) => {
        if (props.onClick) {
          e.stopPropagation();
          props.onClick();
        }
      }}
      px={2}
      py={1}
      spacing={0.5}
      direction="column"
      sx={{
        backgroundColor: 'var(--bg-popup)',
        borderRadius: 4,
        position: 'relative',
        marginBottom: `${props.space || 24}px`,
        '::after': {
          content: '""',
          position: 'absolute',
          // 一根竖线
          width: '1px',
          height: 24,
          bottom: -24,
          marginLeft: '-0.5px',
          left: '50%',
          backgroundColor: 'var(--bg-popup)',
          display: props.line ? 'block' : 'none',
        },
        ':last-child': {
          marginBottom: 0,
        },
        ':last-child::after': {
          display: 'none',
        },

        ':hover': {
          backgroundColor: props.onClick && 'var(--hover)',
          cursor: props.onClick && 'pointer',
        },
      }}
    >
      <Stack direction="row" alignItems={'center'}>
        <Box
          sx={{
            width: 24,
            height: 24,
            position: 'relative',
            flexShrink: 0,
            borderRadius: 4,
            overflow: 'hidden',
          }}
          mr={1}
        >
          {props.icon}
        </Box>
        {/* 单行 省略号 */}
        <Typography
          title={props.name}
          sx={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
          textColor={props.description ? 'var(--text-secondary)' : 'var(--text-primary)'}
          level="b3"
        >
          {props.name}
        </Typography>
      </Stack>

      {props.description && (
        <Typography
          title={props.description}
          // sx={{ mt: 1 }}
          // sx={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
          textColor="var(--text-primary)"
          level="b4"
        >
          {props.description}
        </Typography>
      )}
      {props.children}
    </Stack>
  );
}
