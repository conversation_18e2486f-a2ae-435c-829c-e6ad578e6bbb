import { NodeResourceType } from '@bika/types/node/bo';
import { Stack } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/texts';
import type { FlowNodeTypeDef } from './flow-editor';
import { NodeIcon } from '../../node/icon';

const items: Record<FlowNodeTypeDef, { name: string }> = {
  AI: {
    name: 'AI Agent',
  },
  AUTOMATION: {
    name: 'Automation',
  },
  DATABASE: {
    name: 'Database',
  },
  // VIEW: {
  //   name: 'View',
  // },
  INTEGRATION: {
    name: 'Integration',
  },
  MISSION: {
    name: 'Mission',
  },
  REPORT: {
    name: 'Report',
  },
  FORM: {
    name: 'Form',
  },
  MIRROR: {
    name: 'Mirror',
  },
  FOLDER: {
    name: 'Folder',
  },
  DASHBOARD: {
    name: 'Dashboard',
  },
  ROOT: {
    name: 'Root',
  },
  TEMPLATE: {
    name: 'Template',
  },
  PAGE: {
    name: '<PERSON>',
  },
  EMBED: {
    name: 'Embed',
  },
  DATAPAGE: {
    name: 'Data Page',
  },
  CANVAS: {
    name: 'Can<PERSON>',
  },
  ALIAS: {
    name: 'Alias',
  },
  DOCUMENT: {
    name: 'Document',
  },
  FILE: {
    name: 'File',
  },
  REPORT_TEMPLATE: {
    name: 'Report Template',
  },
  UNIT: {
    name: 'Unit',
  },
};

interface Props {
  type: FlowNodeTypeDef; // 'ai' | 'automation' | 'database' | 'view' | 'integration' | 'mission' | 'report' | 'form' | 'mirror' | 'folder';
  title: string;
  description?: string;
}
export function NodeHeader(props: Props) {
  const { type, title, description } = props;
  const item = items[type];
  return (
    <>
      <Stack
        sx={
          {
            // backgroundColor: 'var(--bg-popup)',
          }
        }
        direction="row"
      >
        {type !== 'INTEGRATION' && type !== 'MISSION' && type !== 'REPORT' && (
          <NodeIcon value={{ kind: 'node-resource', nodeType: type?.toUpperCase() as NodeResourceType }} size={40} />
        )}
        <Stack ml={1} mb={1} direction="column">
          <Typography textColor="var(--text-secondary)" level="b4">
            {item.name}
          </Typography>
          <Typography textColor="var(--text-primary)" level="b3">
            {title}
          </Typography>
        </Stack>
      </Stack>

      <Typography
        title={description}
        sx={{
          mb: 1,
          whiteSpace: 'normal',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          display: '-webkit-box',
          WebkitLineClamp: 2,
          wordBreak: 'break-all',
          WebkitBoxOrient: 'vertical',
        }}
        textColor="var(--text-secondary)"
        level="b4"
      >
        {description}
      </Typography>
    </>
  );
}
