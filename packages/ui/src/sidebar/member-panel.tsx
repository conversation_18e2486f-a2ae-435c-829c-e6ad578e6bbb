import { type ReactElement, type ReactNode } from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import type { TeamVO, RoleVO } from '@bika/types/unit/vo';
import { Button } from '@bika/ui/button-component';
import { AvatarImg, AvatarSize } from '@bika/ui/components/avatar/index';
import type { OrgChartUnit } from '@bika/ui/editor/flow-editor/nodes/unit';
import CommentOutlined from '@bika/ui/icons/components/comment_outlined';
import UserGroupOutlined from '@bika/ui/icons/components/user_group_outlined';
import { Box, Divider, Stack } from '@bika/ui/layouts';
import { NodeIcon } from '@bika/ui/node/icon';
import { EllipsisText } from '@bika/ui/text/ellipsis';
import { Typography } from '@bika/ui/texts';

interface Props {
  type: 'Member' | 'Organization';
  unit?: OrgChartUnit | null;
  skills?: ReactNode;
  onClick?: () => void;
  nodeEditor?: ReactElement | null;
}

const CONTAINER_STYLES = {
  height: '600px', // 固定高度为600px
  borderRadius: '16px',
  border: 'var(--border-default) 1px solid',
  background: 'var(--bg-blur)',
  boxShadow: 'var(--shadow-default)',
  overflowY: 'auto', // 内容超出时显示垂直滚动条
  overflowX: 'hidden', // 隐藏水平滚动条
};

const ORGANIZATION_STYLES = {
  ...CONTAINER_STYLES,
  height: '156px',
  position: 'relative',
  borderRadius: '8px',
  width: '144px',
  padding: '24px 16px',
  minWidth: 'unset',
};

export const MemberPanel = (props: Props) => {
  const { unit, type, onClick, nodeEditor } = props;
  const { t } = useLocale();

  const isAI = unit?.type === 'Member' && unit.relationType === 'AI';
  const isOrganization = type === 'Organization';
  const isMember = unit?.type !== 'Team';
  const name = unit?.name;
  const email = isMember ? unit?.email : '';
  const roles = isMember ? unit?.roles : [];
  const teams = isMember ? unit?.teams : [];
  const nickName = isMember ? unit?.nickName : '';
  const avatar = unit?.avatar;
  const memberCount = unit?.type === 'Team' ? unit?.memberCount : 0;

  const InfoRow = ({
    label,
    value,
    endDecorator,
  }: {
    label: string;
    value?: string | TeamVO[] | RoleVO[] | null;
    endDecorator?: ReactElement;
  }) => {
    if (!value) return null;

    const renderValue = () => {
      if (typeof value === 'string') {
        return (
          <EllipsisText>
            <Typography textColor="var(--text-primary)" level="b2" textAlign="start">
              {value}
            </Typography>
          </EllipsisText>
        );
      }

      if (Array.isArray(value) && value.length > 0) {
        const textContent = value.map((item) => item.name).join(', ');
        return (
          <Box width="calc(100% - 40%)" textAlign="start">
            <Typography textColor="var(--text-primary)" level="b2">
              {textContent}
            </Typography>
          </Box>
        );
      }

      return null;
    };

    const renderedValue = renderValue();
    if (!renderedValue) return null;

    return (
      <Stack width="100%" direction="row" alignItems="flex-start" justifyContent="flex-start" gap={0.5}>
        <Typography textColor="var(--text-secondary)" width="50%" flexShrink={0} level="b2">
          {label}
        </Typography>
        {renderedValue}
        {endDecorator}
      </Stack>
    );
  };

  const renderAvatar = (size: number = 80) => {
    if (!avatar && isAI) {
      return <NodeIcon value={{ kind: 'node-resource', nodeType: 'AI' }} color="var(--static)" size={size} />;
    }

    return (
      <AvatarImg
        alt={name}
        avatar={avatar}
        name={name}
        defaultIcon={!isMember ? <UserGroupOutlined color="var(--static)" /> : null}
        customSize={size}
        shape="SQUARE"
      />
    );
  };

  // Default member/agent details content
  let content = (
    <Stack
      width={isAI ? '400px' : '496px'}
      alignItems="center"
      textAlign="center"
      p="56px 88px"
      spacing={5}
      position="relative"
    >
      {isAI && (
        <div
          className="w-[20px] h-[18px] flex absolute right-[40px] top-[40px] justify-center items-center rounded-[4px] border border-solid border-[var(--border-default)]
  bg-[linear-gradient(90deg,#6681E5_-13.4%,#9852D7_100.33%)]"
        >
          <Typography level="b4" textColor="var(--static)">
            AI
          </Typography>
        </div>
      )}
      <Stack alignItems="center" spacing={2} width="100%">
        {renderAvatar(80)}
        <EllipsisText>
          <Typography
            textColor="var(--text-primary)"
            textOverflow="ellipsis"
            whiteSpace="nowrap"
            overflow="hidden"
            width="100%"
            level={!isOrganization ? 'h4' : 'b3'}
          >
            {name}
          </Typography>
        </EllipsisText>
      </Stack>
      {isOrganization && (
        <EllipsisText>
          <Typography
            textColor={'var(--text-secondary)'}
            textOverflow={'ellipsis'}
            whiteSpace={'nowrap'}
            overflow={'hidden'}
            width="100%"
            level="b4"
          >
            {isMember ? email : memberCount}
          </Typography>
        </EllipsisText>
      )}
      {!isOrganization && (
        <>
          <Stack width="100%" p={'12px 16px'} spacing={3}>
            <InfoRow label={t.user.nickname} value={nickName} />
            <InfoRow label={t.user.email} value={email} />
            <InfoRow label={t.team.team} value={teams} />
            <InfoRow label={t.role.role} value={roles} />
          </Stack>
          <Stack spacing={2}>
            <Button
              onClick={onClick}
              startDecorator={<CommentOutlined currentColor />}
              sx={{
                height: '40px',
                minWidth: '96px',
                padding: '9px 16px',
                borderRadius: '20px',
                fontWeight: 'normal',
                background: 'linear-gradient(90deg, #6681E5 -13.4%, #9852D7 100.33%), var(--brand, #907FF0)',
                color: 'var(--textStaticPrimary)',
                '&:hover': {
                  background: 'var(--brand-hover)',
                },
                '&:active': {
                  background: 'var(--brand-active)',
                },
              }}
            >
              {isAI ? t.ai.start_chat : t.email.send_email}
            </Button>
            {isAI && nodeEditor}
          </Stack>
        </>
      )}
    </Stack>
  );

  if (!unit) {
    return <div>no unit details</div>;
  }

  if (isOrganization) {
    content = (
      <Stack alignItems="center" justifyContent="center" textAlign="center">
        {isAI && (
          <div
            className="h-[18px] min-w-[24px] flex justify-center items-center gap-[2px] absolute right-[8px] top-[8px] rounded-[4px] border border-solid border-[var(--border-default)]
  bg-[linear-gradient(90deg,#6681E5_-13.4%,#9852D7_100.33%)]"
          >
            <Typography level="b4" textColor="var(--textStaticPrimary)">
              AI
            </Typography>
          </div>
        )}
        {renderAvatar(AvatarSize.Size32)}
        <Box mt={2} mb={0.5} width="100%">
          <EllipsisText>
            <Typography
              textColor="var(--text-primary)"
              textOverflow="ellipsis"
              whiteSpace="nowrap"
              overflow="hidden"
              level="b3"
            >
              {name}
            </Typography>
          </EllipsisText>
        </Box>
        {isAI && props.skills}
        <EllipsisText>
          <Typography
            textColor="var(--text-secondary)"
            textOverflow="ellipsis"
            whiteSpace="nowrap"
            overflow="hidden"
            width="100%"
            level="b4"
            mb={0.5}
          >
            {isMember ? email : memberCount}
          </Typography>
        </EllipsisText>
      </Stack>
    );
  }

  if (!isOrganization && isAI) {
    content = (
      <Stack flex={1} direction="row" overflow="hidden">
        {content}
        <Divider orientation="vertical" sx={{ bgcolor: 'var(--borderCommonDefault)' }} />
        {props.skills}
      </Stack>
    );
  }

  return <Stack sx={isOrganization ? ORGANIZATION_STYLES : CONTAINER_STYLES}>{content}</Stack>;
};
