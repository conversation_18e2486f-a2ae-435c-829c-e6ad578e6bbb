import assert from 'assert';
import type { ILocaleContext } from '@bika/contents/i18n/context';
import { FormBO, FormMetadata } from '@bika/types/form/bo';
import type { INodeResourceApi } from '@bika/types/node/context';
import { NodeResourceSelect } from './node-resource-select';
import { DatabaseViewSelect } from '../../database/types-form/database-view-select';

interface Props {
  value: FormBO;
  onChange(bo: FormBO): void;
  locale: ILocaleContext;

  api: INodeResourceApi;
}

export function FormBOInput(props: Props) {
  const { t } = props.locale;
  const formBO = props.value;
  assert(formBO.formType === 'DATABASE' || formBO.formType === undefined);
  const formDatabaseId = formBO.databaseId;
  const metadata: FormMetadata = formBO.metadata || { type: 'VIEW' };
  assert(metadata.type === 'VIEW', 'TODO,  only support view type currently');
  const viewId = metadata.viewId;
  return (
    <>
      <NodeResourceSelect
        resourceType="DATABASE"
        locale={props.locale}
        resourceId={formDatabaseId}
        setResourceId={(newId) => {
          if (newId) {
            formBO.databaseId = newId;
            props.onChange({ ...formBO });
          }
        }}
        label={t.resource.title_database_id}
      />
      <DatabaseViewSelect
        locale={props.locale}
        api={props.api}
        databaseId={formDatabaseId}
        viewId={viewId}
        setViewId={(newViewId) => {
          if (newViewId) {
            metadata.viewId = newViewId;
            props.onChange({ ...formBO, metadata });
          }
        }}
        label={t.resource.title_view_id}
      />
    </>
  );
}
