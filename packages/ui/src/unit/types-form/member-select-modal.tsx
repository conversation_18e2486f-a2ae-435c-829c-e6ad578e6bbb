/* eslint-disable max-lines */
import _ from 'lodash';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import type { ILocaleContext } from '@bika/contents/i18n';
import type { INodeResourceApi } from '@bika/types/node/context';
import { useSpaceContextForce } from '@bika/types/space/context';
import type { To } from '@bika/types/unit/bo';
import type { RoleVO, TeamSubListPageItemVO, TeamVO } from '@bika/types/unit/vo';
import { Breadcrumbs } from '@bika/ui/breadcrumbs';
import { Button } from '@bika/ui/button';
import { Checkbox, Radio, Grid, Input } from '@bika/ui/forms';
import ChevronRightOutlined from '@bika/ui/icons/components/chevron_right_outlined';
import SearchOutlined from '@bika/ui/icons/components/search_outlined';
import UserAddOutlined from '@bika/ui/icons/components/user_add_outlined';
import { Stack, Box } from '@bika/ui/layouts';
import { List, ListItem, ListItemButton } from '@bika/ui/list';
import { MUIModal, ModalDialog, ModalClose } from '@bika/ui/modal';
import { Skeleton } from '@bika/ui/skeleton';
import { Tab, TabList, Tabs, TabPanel } from '@bika/ui/tabs';
import { Typography } from '@bika/ui/texts';
import { UnitItem, UnitTag, type IUserInfo } from '../../user/unit-picker-component';

type UnitInfo = Omit<IUserInfo, 'label'>;

const { debounce, sortBy } = _;

export type SelectType = 'Role' | 'Member' | 'Guest' | 'Team';

// 添加枚举用于管理 Tab
enum TabType {
  Member = 0,
  Role = 1,
  Guest = 2,
  Other = 3,
}

export const getToVo = (items: TeamVO[]) => {
  const assignees: To[] = [];
  const newValues = items.map((item) => item.id);
  const memberIds = newValues.filter((txt) => txt.startsWith('meb'));
  const roles = newValues.filter((txt) => txt.startsWith('rol'));
  const team = newValues.filter((txt) => txt.startsWith('tem'));
  const root = newValues.filter((txt) => txt.startsWith('root'));

  if (memberIds.length) {
    // TO INPUT 不支持 UNIT_MEMBER
    // assignees.push({
    //   type: 'UNIT_MEMBER',
    //   memberId: memberIds,
    // });
    assignees.push({
      type: 'SPECIFY_UNITS',
      unitIds: memberIds.concat(root),
    });
  }

  if (roles.length) {
    assignees.push({
      type: 'UNIT_ROLE',
      roleId: roles,
    });
  }
  if (team.length) {
    assignees.push({
      type: 'UNIT_TEAM',
      teamId: team,
    });
  }
  return assignees;
};

// TODO refactor to editor
type MemberSelectModalProps = {
  selectedUnits: UnitInfo[];
  multiple?: boolean;
  target?: SelectType[];
  hideInviteButton?: boolean;
  onChange: (_teams: UnitInfo[]) => void;
  setIsEdit: (_edit: boolean) => void;
  onCancel?: () => void;
  locale: ILocaleContext;
  api: Pick<INodeResourceApi['unit'], 'useRoles' | 'getSearchUnits' | 'getTeamSubList'>;
};

interface Path {
  id: string | undefined;
  name: string;
}

const DEFAULT_PATH = [
  {
    id: undefined,
    name: 'Root',
  },
];

export const MemberSelect: React.FC<MemberSelectModalProps> = (props) => {
  const {
    selectedUnits,
    target,
    onChange,
    setIsEdit,
    onCancel,
    multiple = false,
    locale,
    api,
    hideInviteButton = false,
  } = props;
  const spaceContext = useSpaceContextForce();
  const { t } = locale;
  const [tab, setTab] = useState(0);
  const rootTeam = tab === 2 ? spaceContext.guestRootTeam : spaceContext.rootTeam;

  let tabs: SelectType[] = target ?? ['Member', 'Role', 'Guest'];
  if (tabs.length === 0) {
    tabs = ['Member', 'Role'];
  }

  // TODO add more type
  tabs = sortBy(tabs, (_tab) => {
    switch (_tab) {
      case 'Member':
        return TabType.Member;
      case 'Role':
        return TabType.Role;
      case 'Guest':
        return TabType.Guest;
      default:
        return TabType.Other;
    }
  });

  const { data: roleData, isLoading: roleLoading } = api.useRoles();

  const [cacheTeamsOrMember, setCacheTeamOrMembers] = React.useState<TeamSubListPageItemVO[]>([spaceContext.rootTeam]);
  const [cacheSelectedUnits, setCacheSelectedUnits] = React.useState<UnitInfo[]>(selectedUnits);
  const [paths, setPaths] = React.useState<Path[]>(DEFAULT_PATH);
  const [loading, setLoading] = React.useState(false);
  const [filterText, setFilterText] = useState('');

  useEffect(() => {
    if (tab === TabType.Guest) {
      setCacheTeamOrMembers([spaceContext.guestRootTeam]);
    }
    if (tab === TabType.Member) {
      setCacheTeamOrMembers([spaceContext.rootTeam]);
    }
    setPaths(DEFAULT_PATH);
  }, [tab]);

  const filterRoles: RoleVO[] | undefined = useMemo(() => {
    if (!tab) return undefined;
    if (!filterText) return roleData?.data;

    return roleData?.data.filter((role) => role.name.toLowerCase().includes(filterText.toLowerCase()));
  }, [tab, filterText, roleData?.data]);

  const searchRequest = useMemo(
    () =>
      debounce(async (value: string) => {
        const { data } = await api.getSearchUnits({ name: value });

        const teams = data.filter(Boolean) as TeamVO[];

        setCacheTeamOrMembers(teams);
        setLoading(false);
      }, 1000),
    [],
  );

  useEffect(() => {
    if (tab) return;
    if (!filterText) return;
    setLoading(true);
    searchRequest(filterText);
  }, [tab]);

  const handleSearch = (value: string) => {
    setFilterText(value);

    if (tab === 1) return;
    if (!value) {
      setCacheTeamOrMembers([spaceContext.rootTeam]);
      return;
    }
    setLoading(true);

    searchRequest(value);
  };

  const handleSelectTeam = (team: TeamVO | UnitInfo) => {
    setCacheSelectedUnits((prevTeams) => {
      let result: UnitInfo[] = [];
      if (prevTeams.some((prevTeam) => prevTeam.id === team.id)) {
        result = prevTeams.filter((prevTeam) => prevTeam.id !== team.id);
      } else {
        result = [...prevTeams, team];
      }
      if (multiple) {
        return result;
      }
      return result.slice(result.length - 1);
    });
  };

  const isTargetEnabled = useCallback(
    (type: SelectType) => {
      if (!target) {
        return true;
      }
      return target.includes(type);
    },
    [target],
  );

  const getChildrenTeams = async (teamId: string | undefined, teamName: string, team?: TeamSubListPageItemVO) => {
    if (!teamId) {
      setPaths(DEFAULT_PATH);
      setCacheTeamOrMembers([spaceContext.rootTeam]);
      return;
    }

    if (team && !teamId.startsWith('tem') && teamId !== rootTeam.id) {
      handleSelectTeam(team);
      return;
    }
    setLoading(true);
    const data = await api.getTeamSubList(teamId);
    setPaths((prevPaths) => {
      const index = prevPaths.findIndex((path) => path.id === teamId);
      if (index === -1) {
        return [...prevPaths, { id: teamId, name: teamName }];
      }
      return prevPaths.slice(0, index + 1);
    });

    const filteredData = data?.data.filter((item) => !(item.type === 'Member' && item.relationType === 'AI'));
    setCacheTeamOrMembers(filteredData ?? []);
    setLoading(false);
  };

  const getTabName = (_tab: SelectType) => {
    if (_tab === 'Member') {
      return t.unit_selected_modal.organization;
    }
    if (_tab === 'Role') {
      return t.unit_selected_modal.role;
    }
    if (_tab === 'Guest') {
      return t.unit_selected_modal.guest;
    }
    return '';
  };

  if (tabs.length === 0) {
    return null;
  }
  return (
    <>
      <Grid container spacing={2} sx={{ flex: '1 1 auto', height: '100%', overflowY: 'auto' }}>
        <Grid item xs={8} sx={{ p: 1, height: '100%', overflowY: 'hidden' }}>
          <Input
            value={filterText}
            startDecorator={<SearchOutlined />}
            placeholder={t.action.search}
            onChange={(e) => {
              handleSearch(e.target.value);
            }}
            sx={{ mb: 1 }}
          />

          <Box
            sx={{
              overflowY: 'auto',
              height: '100%',
              flex: '1 1 auto',
            }}
          >
            <Tabs
              value={tab}
              sx={{
                height: '100%',
                overflowY: 'hidden',
              }}
              onChange={(_e, value) => {
                setTab(value as number);
              }}
            >
              <TabList>
                {tabs.map((_tab) => (
                  <Tab key={_tab} variant="plain" color="neutral">
                    {getTabName(_tab)}
                  </Tab>
                ))}
              </TabList>

              <TabPanel value={0}>
                <Box
                  sx={{
                    maxHeight: '432px',
                    overflowY: 'hidden',
                    display: 'flex',
                    flexDirection: 'column',
                  }}
                >
                  <Breadcrumbs
                    sx={{
                      flex: '0 0',
                    }}
                  >
                    {paths.map((path, index) => (
                      <Typography
                        key={path.id}
                        level="body-md"
                        onClick={() => {
                          if (index === paths.length - 1) return;
                          getChildrenTeams(path.id, path.name);
                        }}
                      >
                        {path.name}
                      </Typography>
                    ))}
                  </Breadcrumbs>
                  <Stack spacing={1} direction="column" flex={'1 1 auto'} sx={{ mt: 1.5, overflowY: 'auto' }}>
                    {loading && (
                      <Box>
                        <Skeleton pos="MEMBER_SELECT" />
                      </Box>
                    )}
                    {!loading && cacheTeamsOrMember.length === 0 && (
                      <Typography level="body-md">{t.unit_selected_modal.empty_data}</Typography>
                    )}
                    {!loading && cacheTeamsOrMember.length > 0 && (
                      <List
                        sx={{
                          overflowY: 'auto',
                        }}
                      >
                        {cacheTeamsOrMember?.map((team) => (
                          <ListItem key={team.id}>
                            <ListItemButton
                              sx={{
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                height: '40px',
                                '&:hover': {
                                  backgroundColor: 'var(--hover) !important',
                                },
                              }}
                              onClick={() => {
                                getChildrenTeams(team.id, team.name, team);
                              }}
                            >
                              <Stack direction="row" alignItems="center" spacing={2} sx={{ overflow: 'hidden' }}>
                                {multiple ? (
                                  <Checkbox
                                    onChange={() => {
                                      handleSelectTeam(team);
                                    }}
                                    disabled={isTargetEnabled(team.type as SelectType) === false}
                                    onClick={(e) => e.stopPropagation()}
                                    checked={cacheSelectedUnits.some((selectedTeam) => selectedTeam.id === team.id)}
                                  />
                                ) : (
                                  <Radio
                                    onChange={() => {
                                      handleSelectTeam(team);
                                    }}
                                    disabled={isTargetEnabled(team.type as SelectType) === false}
                                    onClick={(e) => e.stopPropagation()}
                                    checked={cacheSelectedUnits.some((selectedTeam) => selectedTeam.id === team.id)}
                                  />
                                )}
                                <UnitItem data={team} />
                              </Stack>
                              {(team.type === 'Team' || team.id === rootTeam.id) && (
                                <Box sx={{ width: 24, flexShrink: 0 }}>
                                  <ChevronRightOutlined />
                                </Box>
                              )}
                            </ListItemButton>
                          </ListItem>
                        ))}
                      </List>
                    )}
                  </Stack>
                </Box>
              </TabPanel>
              <TabPanel
                value={1}
                sx={{
                  maxHeight: '432px',
                  overflowY: 'hidden',
                }}
              >
                {roleLoading && (
                  <Box>
                    <Skeleton pos="MEMBER_SELECT" />
                  </Box>
                )}
                {!roleLoading && filterRoles?.length === 0 && (
                  <Typography level="body-md">{t.space.no_data}</Typography>
                )}
                <List
                  sx={{
                    height: '100%',
                    flex: '1 1 auto',
                    overflowY: 'auto',
                  }}
                >
                  {filterRoles?.map((team: RoleVO) => (
                    <ListItem key={team.id}>
                      <ListItemButton
                        sx={{
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          height: '40px',
                        }}
                      >
                        <Stack direction="row" alignItems="center" spacing={2} sx={{ overflow: 'hidden' }}>
                          {multiple ? (
                            <Checkbox
                              onChange={() => {
                                handleSelectTeam(team);
                              }}
                              disabled={isTargetEnabled(team.type) === false}
                              onClick={(e) => e.stopPropagation()}
                              checked={cacheSelectedUnits.some((selectedTeam) => selectedTeam.id === team.id)}
                            />
                          ) : (
                            <Radio
                              onChange={() => {
                                handleSelectTeam(team);
                              }}
                              disabled={isTargetEnabled(team.type) === false}
                              onClick={(e) => e.stopPropagation()}
                              checked={cacheSelectedUnits.some((selectedTeam) => selectedTeam.id === team.id)}
                            />
                          )}
                          <UnitItem data={team} />
                        </Stack>
                      </ListItemButton>
                    </ListItem>
                  ))}
                </List>
              </TabPanel>

              <TabPanel
                value={2}
                sx={{
                  maxHeight: '432px',
                  overflowY: 'auto',
                }}
              >
                <>
                  <Breadcrumbs
                    sx={{
                      flex: '0 0 none',
                    }}
                  >
                    {paths.map((path, index) => (
                      <Typography
                        key={path.id}
                        level="body-md"
                        onClick={() => {
                          if (index === paths.length - 1) return;
                          getChildrenTeams(path.id, path.name);
                        }}
                      >
                        {path.name}
                      </Typography>
                    ))}
                  </Breadcrumbs>
                  <Stack spacing={1} direction="column" sx={{ mt: 1.5 }}>
                    {loading && (
                      <Box>
                        <Skeleton pos="MEMBER_SELECT" />
                      </Box>
                    )}
                    {!loading && cacheTeamsOrMember.length === 0 && (
                      <Typography level="body-md">{t.unit_selected_modal.empty_data}</Typography>
                    )}
                    {!loading && cacheTeamsOrMember.length > 0 && (
                      <List
                        sx={{
                          overflowY: 'auto',
                        }}
                      >
                        {cacheTeamsOrMember?.map((team) => (
                          <ListItem key={team.id}>
                            <ListItemButton
                              sx={{
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                height: '40px',
                              }}
                              onClick={() => {
                                getChildrenTeams(team.id, team.name, team);
                              }}
                            >
                              <Stack direction="row" alignItems="center" spacing={2} sx={{ overflow: 'hidden' }}>
                                {multiple ? (
                                  <Checkbox
                                    onChange={() => {
                                      handleSelectTeam(team);
                                    }}
                                    onClick={(e) => e.stopPropagation()}
                                    checked={cacheSelectedUnits.some((selectedTeam) => selectedTeam.id === team.id)}
                                  />
                                ) : (
                                  <Radio
                                    onChange={() => {
                                      handleSelectTeam(team);
                                    }}
                                    onClick={(e) => e.stopPropagation()}
                                    checked={cacheSelectedUnits.some((selectedTeam) => selectedTeam.id === team.id)}
                                  />
                                )}
                                <UnitItem data={team} />
                              </Stack>
                              {(team.type === 'Team' || team.id === rootTeam.id) && (
                                <Box sx={{ width: 24, flexShrink: 0 }}>
                                  <ChevronRightOutlined />
                                </Box>
                              )}
                            </ListItemButton>
                          </ListItem>
                        ))}
                      </List>
                    )}
                  </Stack>
                </>
              </TabPanel>
            </Tabs>
          </Box>
        </Grid>
        <Grid item xs={4} borderLeft="2px solid var(--rowSelectedBg)">
          <Typography level="h4">{t.unit_selected_modal.selected_team}</Typography>
          <Stack spacing={1} direction="row" useFlexGap flexWrap="wrap">
            {cacheSelectedUnits?.map((unit) => (
              <UnitTag
                key={unit.id}
                data={unit}
                onClose={() => {
                  handleSelectTeam(unit);
                }}
              />
            ))}
          </Stack>
        </Grid>
      </Grid>
      <Stack direction="row" justifyContent="space-between">
        <div>
          {!hideInviteButton && (
            <Button
              variant="plain"
              startDecorator={<UserAddOutlined currentColor />}
              onClick={() => {
                spaceContext.showUIModal({
                  type: 'space-settings',
                  first: 'SPACE',
                  tab: { type: 'SPACE_INVITE_MEMBER' },
                });
              }}
            >
              {t.space.home.invite_members}
            </Button>
          )}
        </div>
        <Stack direction="row" spacing={1}>
          <Button
            color="neutral"
            variant="plain"
            onClick={() => {
              // setIsEdit(false);
              onCancel?.();
            }}
          >
            {t.cancel}
          </Button>
          <Button
            variant="solid"
            onClick={() => {
              onChange(cacheSelectedUnits);
              setIsEdit(false);
            }}
          >
            {t.ok}
          </Button>
        </Stack>
      </Stack>
    </>
  );
};

export const MemberSelectModal = (props: MemberSelectModalProps) => {
  const { onCancel, locale } = props;
  const { t } = locale;

  return (
    <MUIModal open onClose={() => onCancel?.()}>
      <ModalDialog
        component="div"
        sx={{
          height: 640,
          width: 600,
          maxWidth: 600,
        }}
      >
        <ModalClose />
        <Typography level="h4">{t.record.select_member}</Typography>
        <MemberSelect {...props} />
      </ModalDialog>
    </MUIModal>
  );
};
