{"name": "sharelib", "private": true, "version": "1.9.1-alpha.1", "description": "Shared and basic utils library for Vika, Bika.ai, and ToolSDK.ai. No index.ts main entry, for tree shaking.", "exports": {"./*": "./*.ts", "./search": "./search/index.ts", "./search/*": "./search/*.ts"}, "scripts": {}, "dependencies": {"@elastic/elasticsearch": "^8.13.0", "axios": "^1.9.0", "basenext": "workspace:^", "connection-string": "^4.4.0", "dayjs": "1.11.10", "lodash": "^4.17.21", "mime-types": "^2.1.35", "minio": "7.1.3", "mongoose": "^8.15.1", "nanoid": "^5.0.9", "negotiator": "^0.6.3", "react": "18.3.1", "react-dom": "18.3.1"}, "keywords": [], "author": "", "license": "ISC"}