---
title: 如何使用 ToolSDK.ai，搭建一个OpenAI 的表单式AI Action？
slug: toolsdk.ai/help/zh-CN/tutorials/_build-openai-form-action
sidebar_position: 2
---


# 如何使用 ToolSDK.ai，搭建一个OpenAI 的表单式AI Action？

在日常的使用中，要调用OpenAI，还需要懂点代码才行能调用。

有没有办法，制作一个简单的表单，就可以调用OpenAI啦？

有！借助 [FormApp.ai](http://FormApp.ai) 这一强大工具，可轻松实现与各类第三方工具的无缝连接。

作为 AI 自动化调度体系的关键构成部分，它能够将多种功能与技术整合，通过高效的算法和智能的流程编排，精心打包出一个功能完备、适应性强的商业 AI 智能体。

这一智能体不仅具备强大的API和AI执行力，还能依据不同的业务场景和需求，灵活地执行任务，为企业在复杂多变的商业环境中提供智能化、个性化的解决方案 ，助力企业提升运营效率、降低成本，增强市场竞争力。

## 功能简介

我们先来看看效果，（打开表单，填入token、message，点击提交）....文本生成成功了！

那么，如何利用FormApp.ai，打造一个属于你自己的表单式AI Agent？

## 操作流程

### 一、新建 OpenAI 集成

#### <b>进入FormApp.ai</b>

点击导航栏中的 <b>Submit</b>，切换到 <b>New Integration</b>页面。

先填写基础信息

<b>Key：</b> 此集成在FormApp中的唯一标识

<b>名称和描述：</b> 填写集成名称和简要说明，并可以切换多语言进行填写

<b>认证类型：</b> 选择 <b>Custom</b>，兼容 OpenAI 的认证模式

最后点击底部 Submit 提交

<img src="https://book.bika.ai/bika-content/assets/UQFIbxPv8oem3xxjU1tlKDjkgpc.png" src-width="1440" src-height="778" align="center"/>

#### 集成配置

##### 配置表单

通过 OpenAI <u>官方 API 文档</u> 提供的信息，我们需要使用 API Key 来集成 Open AI 的账户，所以需要创建该字段

<img src="https://book.bika.ai/bika-content/assets/L1dxbNTSUoiYlnxvGKGluBiGgLf.png" src-width="1440" src-height="778" align="center"/>

<img src="https://book.bika.ai/bika-content/assets/P4IIb8mUiosEi5x0WnQlOBUVgRd.png" src-width="1440" src-height="778" align="center"/>

##### 添加测试 API

可以添加以下链接作为测试 API， 然后点击底部的 Update 进行保存

```bash
// 测试运行后可以获取模型列表，代表集成搭建成功
GET https://api.openai.com/v1/models
```

##### 获取API key

<div class="flex gap-3 columns-2" column-size="2">
<div class="w-[50%]" width-ratio="50">
<img src="https://book.bika.ai/bika-content/assets/NsIbbU9Vhon59xxnwallZz9dgQq.png" src-width="1440" src-height="778" align="center"/>
</div>
<div class="w-[50%]" width-ratio="50">
<p>进入 <a href="https://platform.openai.com/api-keys">OpenAI API key</a> ，这里管理你的 API key 列表，点击右上角的 “Create new secret key” 按钮创建一个新的 Key，根据你的需要输入和选择权限，保存后复制 API key 备用。</p>
</div>
</div>

<img src="https://book.bika.ai/bika-content/assets/KgnsbhkfIoKhe6xzGN5l26ZngFb.png" src-width="1440" src-height="778" align="center"/>

<img src="https://book.bika.ai/bika-content/assets/ViBcbhlhGoJGAkxxH3flD8j8gpb.png" src-width="1440" src-height="778" align="center"/>

##### 连接账号

回到 Formapp.ai，在右侧的预览区域，点击 Add Credential 来链接OpenAI 账户。

输入刚刚创建的 Key，并点击按钮链接。成功后会生成一条新的 Credential，在下拉菜单中可以选择

<img src="https://book.bika.ai/bika-content/assets/XIGbbNIhGo8Mz3xAwLOlE0uDgjb.png" src-width="1440" src-height="778" align="center"/>

### 二、新建 Open AI 文本生成Action

<img src="https://book.bika.ai/bika-content/assets/RsNNbfn19ojnCJxLpUFloIWcgIe.png" src-width="1440" src-height="778" align="center"/>

<img src="https://book.bika.ai/bika-content/assets/AKX8bZ02pos7HcxgP9dlvEY0gJh.png" src-width="1440" src-height="778" align="center"/>

##### 配置表单

基于 [OpenAI 文本生成 API](https://platform.openai.com/docs/guides/text-generation?example=completions) 官方文档提供的信息，文本生成的请求如下，其中进行文本生成至少需要两个字段：

- model
- messages

```bash
curl "https://api.openai.com/v1/chat/completions" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $OPENAI_API_KEY" \
    -d '{
        "model": "gpt-4o",
        "messages": [
            {
                "role": "developer",
                "content": "You are a helpful assistant."
            },
            {
                "role": "user",
                "content": "Write a haiku about recursion in programming."
            }
        ]
    }'
```

而此 Action 所需的其实是 Model 和 messages中的用户输入的 content

所以需要在 Formapp Inputs 中创建这两个字段

除此之外，

<img src="https://book.bika.ai/bika-content/assets/H2zobK1USo4VzKxF4lMlNExBgqb.png" src-width="1440" src-height="778" align="center"/>

其中 Model 字段可以使用 Select 类型，并添加所需的值，添加允许使用的模型名称即可

<img src="https://book.bika.ai/bika-content/assets/UbsRb3PNkovaGvxnQaxl7X28g5c.png" src-width="1440" src-height="778" align="center"/>

##### 配置 API 

- <b>请求头</b>

```bash
curl "https://api.openai.com/v1/chat/completions" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $OPENAI_API_KEY" \
```

基于官方文档提供的信息，请求头需要 Content-Type: application/json 和 Authorization: Bearer $OPENAI_API_KEY

其中 API key 这部分已经通过集成输入了，所以在此使用 {{bundle.authData.Integration.Key}} 的方式绑定即可

<img src="https://book.bika.ai/bika-content/assets/Xva0bzDN7o9IzAxRDxPlITXig5e.png" src-width="1440" src-height="778" align="center"/>

- <b>请求体</b>

请求头遵循官方文档提供的格式书写，必须包含 model 和 message。

- Model 绑定用户选择的模型
- Message 中 developer 的 content 可以自定义你希望完成何种类型文本生成工作的 Prompt
- Message 中 user 的 content 是用户使用时输入的内容，绑定用户输入

```json
{
        "model": "{{bundle.inputData.model}}",
        "messages": [
            {
                "role": "developer",
                "content": "You can customize the Prompt here"
            },
            {
                "role": "user",
                "content": "{{bundle.inputData.user_input}}",
            }
        ]
    }
```

<img src="https://book.bika.ai/bika-content/assets/LgMQbAVANo3zkqxd2u8lQJLOgYg.png" src-width="2880" src-height="1800" align="center"/>

#### 测试 Action 

OpenAI Text Grenade Action 就完成了，接下来可以在右侧预览区域之间运行测试。

首先选择集成，如果你已经连接过 OpenAI ，可以直接在下拉菜单中选择，或者点击按钮新建连接

在下拉菜单中选择语言模型，然后输入需要生成文本的 Prompt，点击 Test 按钮测试运行

<img src="https://book.bika.ai/bika-content/assets/Y7dKbzsrAoUbvNxGYD4lYVClg3b.png" src-width="1440" src-height="778" align="center"/>

随后将会在当前窗口弹出返回结果，代表测试运行成功。

<img src="https://book.bika.ai/bika-content/assets/ClgWby0baorZQ2xSAD1l2XxQg1f.png" src-width="1440" src-height="778" align="center"/>

# 
