---
title: How to Submit a MCP Server to ToolSDK.ai
slug: toolsdk.ai/help/en/tutorials/how-to-submit-a-mcp-server-to-toolsdk-ai
sidebar_position: 1
sidebar_label: How to Submit a MCP Server to ToolSDK.ai
---


# How to Submit a MCP Server to ToolSDK.ai

`Awesome Mcp Registry` (https://github.com/toolsdk-ai/awesome-mcp-registry) is an open, high-quality, well-structured and developer-friendly list of thousands MCP servers.

Unlike other MCP projects, this `Awesome MCP Registry` leverages structured JSON configs to generate `README.md`, <u>npm package</u> and <u>packages-list.json</u>

ToolSDK.ai use `Awesome MCP Registry` to build your own great MCP projects and hosting sites, acting as the database for MCP servers and packages.

> <b>TL;DR</b>: 
Fork the repository ➝ Add your JSON config ➝ Submit a Pull Request ➝ Get reviewed ➝ Published to ToolSDK.ai!

Anyone can contribute MCP server configurations formatted as JSON via Pull Requests. 

Once merged, your submission will be automatically published as an npm package, with README, documentation, website content, and API interfaces generated.

---

## 📌 Prefer a video guide?  

Watch this video for a step-by-step walkthrough:

[Awesome MCP Registry: How to submit a MCP server in JSON file?](https://www.youtube.com/watch?v=J_oaDtCoVVo)

<iframe src="https://www.youtube.com/embed/J_oaDtCoVVo"/>

---

## Detailed Submission Guide

### 1. ✅ Fork the Original Repository

1. Open the original project repository page (`https://github.com/toolsdk-ai/awesome-mcp-registry`).
2. Click the <b>Fork</b> button at the top right to create a copy under your account.
3. Clone your forked repository to your local machine.

```bash
git clone https://github.com/toolsdk-ai/awesome-mcp-registry.git
```

### 2. ✅ Navigate to the Target Directory

Go to the root directory of your local repository and locate the `packages/uncategorized` folder. 

This is where you should place your new MCP server configuration file. <em>(You just need to put it here; AI will help categorize it later.)</em>

### 3. ✅ Create a New JSON File

Create a new file named, for example, `my-mcp-server.json`inside the `packages/uncategorized` directory. 

Fill it with the following structure:

```json
{
  "type": "mcp-server",
  "packageName": "@yourname/your-mcp-server",
  "description": "Describe your MCP server’s functionality",
  "url": "https://github.com/yourname/your-mcp-server",
  "runtime": "node",
  "license": "MIT",
  "env": {
    "GITHUB_PERSONAL_ACCESS_TOKEN": {
      "description": "Personal access token for GitHub API access",
      "required": true
    }
  }
}
```

📌 <b>Required Fields</b>

<table>
<colgroup>
<col width="200"/>
<col width="200"/>
<col width="200"/>
</colgroup>
<tbody>
<tr><td><p>Field Name</p></td><td><p>Required</p></td><td><p>Description</p></td></tr>
<tr><td><p><code>type</code></p></td><td><p>✅ Yes</p></td><td><p>Fixed value <code>&quot;mcp-server&quot;</code></p></td></tr>
<tr><td><p><code>packageName</code></p></td><td><p>✅ Yes</p></td><td><p>NPM package name or unique identifier</p></td></tr>
<tr><td><p><code>description</code></p></td><td><p>✅ Yes</p></td><td><p>Describe what this MCP server does</p></td></tr>
<tr><td><p><code>runtime</code></p></td><td><p>✅ Yes</p></td><td><p>Supports <code>&quot;node&quot;</code>, <code>&quot;python&quot;</code>, <code>&quot;java&quot;</code>, <code>&quot;go&quot;</code></p></td></tr>
</tbody>
</table>

Optional fields such as `license`, `url`, `env`, `name` etc., can be added as needed.

### 4. ✅ Commit Changes to Your Forked Repo

Add the file and commit:

```bash
git add packages/uncategorized/my-mcp-server.json
git commit -m "Add new MCP server: my-mcp-server"
git push origin main
```

### ✅ Submit a Pull Request

Go to your fork on GitHub and click <b>New Pull Request</b> to propose your changes to the original repo.

### ✅Awaiting Review and Automation

After your PR is merged, we will review your submission. Once the review is approved, the system will automatically perform the following actions. You can check the deployment status of your MCP server at the following locations:

- Publish your MCP server as an npm package;
- Generate a README documentation file;
- Update the `packages-list.json` file;
- Build the online API documentation;
- Display your service on the ToolSDK.ai website.

### ✅ Verify Your Submission

If uncertain about the JSON structure, refer to examples or validate with Zod Schema.

- Don't forget to write a clear [description](file:///home/<USER>/bika/bika/scripts/toolsdk/crawl-mcp-get.ts#L16-L16) — it helps users understand your service.
- If there are environment variables required, make sure to clearly document each field and whether it’s mandatory.

---

## <b>🚀 Tips</b>

- If you're unsure whether the JSON is correctly structured, refer to existing examples or use a Zod Schema to validate it.
- Don't forget to provide a clear description — it helps users understand your service.
- If your service requires environment variables, be sure to explain each field and indicate whether it is required.

