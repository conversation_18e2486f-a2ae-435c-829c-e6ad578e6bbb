import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import type { ToolInvocation } from '@ai-sdk/ui-utils';
import { FileArtifact } from '@bika/domains/ai-artifacts';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { FC } from 'react';

// Mock data for SkillsetSelectDTO
const mockSkillsets: SkillsetSelectDTO[] = [
  { kind: 'preset', key: 'default' },
  { kind: 'preset', key: 'bika-ai-page' },
  { kind: 'preset', key: 'bika-database' },
];

// Mock data for ToolInvocation
const mockToolInvocation: ToolInvocation = {
  toolName: 'generate_file',
  toolCallId: 'file-artifact-call-id',
  state: 'result',
  args: {
    prompt: 'Generate a file',
  },
  result: {
    filePath: '/path/to/sample-file.pdf',
    content: 'Sample file content',
  },
};

// Sample file paths for different stories
const sampleImagePath = 'https://via.placeholder.com/800x600/4CAF50/FFFFFF?text=Sample+Image';
const samplePdfPath = 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';
const sampleDocPath = '/path/to/sample-document.docx';

const FileArtifactStory: FC<{
  filePath?: string;
  content?: string;
  skillsets?: SkillsetSelectDTO[];
  tool?: ToolInvocation;
  expandable?: boolean;
}> = ({ 
  filePath, 
  content, 
  skillsets = mockSkillsets, 
  tool = mockToolInvocation, 
  expandable = true 
}) => {
  return (
    <div className="p-4 w-full h-[600px]">
      <FileArtifact
        filePath={filePath}
        content={content}
        skillsets={skillsets}
        tool={tool}
        expandable={expandable}
      />
    </div>
  );
};

export default {
  title: '@bika/ai/FileArtifact',
  component: FileArtifactStory,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  args: {
    filePath: sampleImagePath,
    content: 'Sample file content',
    skillsets: mockSkillsets,
    tool: mockToolInvocation,
    expandable: true,
  },
} satisfies Meta<typeof FileArtifactStory>;

type Story = StoryObj<typeof FileArtifactStory>;

export const Default: Story = {};

export const ImageFile: Story = {
  args: {
    filePath: sampleImagePath,
    content: 'Generated image file',
    tool: {
      ...mockToolInvocation,
      toolName: 'generate_image',
      args: {
        prompt: 'Generate a sample image',
      },
    },
  },
};

export const PdfFile: Story = {
  args: {
    filePath: samplePdfPath,
    content: 'Generated PDF document',
    tool: {
      ...mockToolInvocation,
      toolName: 'generate_pdf',
      args: {
        prompt: 'Generate a PDF document',
      },
    },
  },
};

export const DocumentFile: Story = {
  args: {
    filePath: sampleDocPath,
    content: 'Generated document file',
    tool: {
      ...mockToolInvocation,
      toolName: 'generate_document',
      args: {
        prompt: 'Generate a document file',
      },
    },
  },
};

export const NoFilePath: Story = {
  args: {
    filePath: undefined,
    content: 'File content without path',
    tool: {
      ...mockToolInvocation,
      toolName: 'generate_content_only',
      args: {
        prompt: 'Generate content without file path',
      },
    },
  },
};

export const NoContent: Story = {
  args: {
    filePath: sampleImagePath,
    content: undefined,
    tool: {
      ...mockToolInvocation,
      toolName: 'generate_file_only',
      args: {
        prompt: 'Generate file path only',
      },
    },
  },
};

export const NotExpandable: Story = {
  args: {
    filePath: sampleImagePath,
    content: 'Sample file content',
    expandable: false,
  },
};

export const MinimalSkillsets: Story = {
  args: {
    filePath: sampleImagePath,
    content: 'Sample file content',
    skillsets: [{ kind: 'preset', key: 'default' }],
  },
};

export const CallState: Story = {
  args: {
    filePath: sampleImagePath,
    content: 'Sample file content',
    tool: {
      ...mockToolInvocation,
      state: 'call',
    },
  },
};

export const EmptyState: Story = {
  args: {
    filePath: undefined,
    content: undefined,
    tool: {
      ...mockToolInvocation,
      toolName: 'generate_empty_file',
      args: {
        prompt: 'Generate empty file',
      },
    },
  },
};
