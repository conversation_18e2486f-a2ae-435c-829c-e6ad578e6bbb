import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import type { ToolInvocation } from '@ai-sdk/ui-utils';
import { RecordArtifact } from '@bika/domains/ai-artifacts';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { FieldVO, RecordVO } from '@bika/types/database/vo';
import { FC } from 'react';

// Mock data for SkillsetSelectDTO
const mockSkillsets: SkillsetSelectDTO[] = [
  { kind: 'preset', key: 'default' },
  { kind: 'preset', key: 'bika-ai-page' },
  { kind: 'preset', key: 'bika-database' },
];

// Mock data for ToolInvocation
const mockToolInvocation: ToolInvocation = {
  toolName: 'generate_records',
  toolCallId: 'record-artifact-call-id',
  state: 'result',
  args: {
    prompt: 'Generate database records',
  },
  result: {
    records: [],
    fields: [],
  },
};

// Mock field data
const mockFields: FieldVO[] = [
  {
    id: 'field1',
    name: 'Name',
    type: 'SingleLineText',
    property: {},
    isPrimary: true,
  },
  {
    id: 'field2',
    name: 'Email',
    type: 'Email',
    property: {},
    isPrimary: false,
  },
  {
    id: 'field3',
    name: 'Age',
    type: 'Number',
    property: {},
    isPrimary: false,
  },
  {
    id: 'field4',
    name: 'Department',
    type: 'SingleSelect',
    property: {
      options: [
        { id: 'opt1', name: 'Engineering', color: 'blue' },
        { id: 'opt2', name: 'Marketing', color: 'green' },
        { id: 'opt3', name: 'Sales', color: 'red' },
      ],
    },
    isPrimary: false,
  },
];

// Mock record data
const mockRecords: RecordVO[] = [
  {
    id: 'rec1',
    databaseId: 'db1',
    fields: {
      field1: 'John Doe',
      field2: '<EMAIL>',
      field3: 30,
      field4: 'opt1',
    },
    createdTime: new Date().toISOString(),
    lastModifiedTime: new Date().toISOString(),
  },
  {
    id: 'rec2',
    databaseId: 'db1',
    fields: {
      field1: 'Jane Smith',
      field2: '<EMAIL>',
      field3: 28,
      field4: 'opt2',
    },
    createdTime: new Date().toISOString(),
    lastModifiedTime: new Date().toISOString(),
  },
  {
    id: 'rec3',
    databaseId: 'db1',
    fields: {
      field1: 'Bob Johnson',
      field2: '<EMAIL>',
      field3: 35,
      field4: 'opt3',
    },
    createdTime: new Date().toISOString(),
    lastModifiedTime: new Date().toISOString(),
  },
];

const RecordArtifactStory: FC<{
  data?: RecordVO[];
  fields?: FieldVO[];
  skillsets?: SkillsetSelectDTO[];
  tool?: ToolInvocation;
}> = ({ 
  data = mockRecords, 
  fields = mockFields, 
  skillsets = mockSkillsets, 
  tool = mockToolInvocation 
}) => {
  return (
    <div className="p-4 w-full h-[600px]">
      <RecordArtifact
        data={data}
        fields={fields}
        skillsets={skillsets}
        tool={tool}
      />
    </div>
  );
};

export default {
  title: '@bika/ai/RecordArtifact',
  component: RecordArtifactStory,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  args: {
    data: mockRecords,
    fields: mockFields,
    skillsets: mockSkillsets,
    tool: mockToolInvocation,
  },
} satisfies Meta<typeof RecordArtifactStory>;

type Story = StoryObj<typeof RecordArtifactStory>;

export const Default: Story = {};

export const SingleRecord: Story = {
  args: {
    data: [mockRecords[0]],
    fields: mockFields,
    tool: {
      ...mockToolInvocation,
      toolName: 'generate_single_record',
      args: {
        prompt: 'Generate a single database record',
      },
    },
  },
};

export const EmptyRecords: Story = {
  args: {
    data: [],
    fields: mockFields,
    tool: {
      ...mockToolInvocation,
      toolName: 'generate_empty_records',
      args: {
        prompt: 'Generate empty record set',
      },
    },
  },
};

export const MinimalFields: Story = {
  args: {
    data: mockRecords,
    fields: [mockFields[0], mockFields[1]], // Only name and email
    tool: {
      ...mockToolInvocation,
      toolName: 'generate_minimal_records',
      args: {
        prompt: 'Generate records with minimal fields',
      },
    },
  },
};

export const CallState: Story = {
  args: {
    data: mockRecords,
    fields: mockFields,
    tool: {
      ...mockToolInvocation,
      state: 'call',
    },
  },
};

export const MinimalSkillsets: Story = {
  args: {
    data: mockRecords,
    fields: mockFields,
    skillsets: [{ kind: 'preset', key: 'default' }],
  },
};
