import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import type { ToolInvocation } from '@ai-sdk/ui-utils';
import { 
  FileArtifact, 
  HtmlArtifact, 
  MarkdownArtifact, 
  RecordArtifact, 
  NodeResourcesArtifact ,
  SlidesArtifact
} from '@bika/domains/ai-artifacts';
// import { SlidesArtifact } from '@bika/domains/ai-artifacts/slides-server-artifact/slides-artifact';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import type { NodeResource } from '@bika/types/node/bo';
import { FieldVO, RecordVO } from '@bika/types/database/vo';
import { Slides, SlidesOutline } from '@bika/types/ai/vo';
import { FC } from 'react';
import { Box } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/text-components';

// Mock data for SkillsetSelectDTO
const mockSkillsets: SkillsetSelectDTO[] = [
  { kind: 'preset', key: 'default' },
  { kind: 'preset', key: 'bika-ai-page' },
  { kind: 'preset', key: 'bika-database' },
];

// Mock data for ToolInvocation
const mockToolInvocation: ToolInvocation = {
  toolName: 'generate_artifact',
  toolCallId: 'artifact-call-id',
  state: 'result',
  args: {
    prompt: 'Generate artifact',
  },
  result: {},
};

// Mock data for each artifact type
const mockHtmlContent = `
<div style="padding: 20px; font-family: Arial, sans-serif; background: #f8f9fa; border-radius: 8px;">
  <h1 style="color: #2c3e50;">Sample HTML Artifact</h1>
  <p>This is a sample HTML artifact demonstrating rich content rendering.</p>
  <button style="background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 4px;">
    Interactive Button
  </button>
</div>
`;

const mockMarkdownContent = `# Sample Markdown Artifact

This is a **sample markdown** artifact with various formatting:

## Features
- **Bold text** and *italic text*
- Code snippets
- Lists and more

\`\`\`javascript
console.log('Hello, World!');
\`\`\`

> This is a blockquote example.
`;

const mockFields: FieldVO[] = [
  {
    id: 'field1',
    name: 'Name',
    type: 'SingleLineText',
    property: {},
    isPrimary: true,
  },
  {
    id: 'field2',
    name: 'Email',
    type: 'Email',
    property: {},
    isPrimary: false,
  },
];

const mockRecords: RecordVO[] = [
  {
    id: 'rec1',
    databaseId: 'db1',
    fields: {
      field1: 'John Doe',
      field2: '<EMAIL>',
    },
    createdTime: new Date().toISOString(),
    lastModifiedTime: new Date().toISOString(),
  },
];

const mockSlides: Slides = [
  {
    slide_number: 1,
    title: 'Sample Slide',
    type: 'title',
    html_content: `
      <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center;">
        <h1 style="font-size: 2.5rem; margin-bottom: 20px;">Sample Presentation</h1>
        <p style="font-size: 1.2rem;">Generated by AI</p>
      </div>
    `,
  },
];

const mockSlidesOutline: SlidesOutline = {
  outline: {
    title: 'Sample Presentation',
    slides: [
      {
        slide_number: 1,
        slide_title: 'Sample Slide',
        slide_type: 'title',
        slide_data: {
          subtitle: 'Generated by AI',
          author: 'AI System',
          date: 'December 2023',
        },
        description: 'Title slide for sample presentation',
      },
    ],
  },
};

const mockNodeResources: NodeResource[] = [
  {
    resourceType: 'DATABASE',
    id: 'db-1',
    name: 'Sample Database',
    description: 'A sample database resource',
    databaseType: 'DATUM',
    fields: [
      {
        name: 'Title',
        type: 'SingleLineText',
        id: 'field-1',
      },
    ],
  },
];

const AllArtifactsStory: FC = () => {
  return (
    <Box sx={{ p: 4, display: 'flex', flexDirection: 'column', gap: 6 }}>
      <Typography level="h1" sx={{ textAlign: 'center', mb: 4 }}>
        All AI Artifacts Showcase
      </Typography>

      {/* File Artifact */}
      <Box>
        <Typography level="h2" sx={{ mb: 2 }}>
          File Artifact
        </Typography>
        <Box sx={{ height: '400px', border: '1px solid #ddd', borderRadius: '8px', overflow: 'hidden' }}>
          <FileArtifact
            filePath="https://via.placeholder.com/600x400/4CAF50/FFFFFF?text=Sample+File"
            content="Sample file content"
            skillsets={mockSkillsets}
            tool={{
              ...mockToolInvocation,
              toolName: 'generate_file',
            }}
          />
        </Box>
      </Box>

      {/* HTML Artifact */}
      <Box>
        <Typography level="h2" sx={{ mb: 2 }}>
          HTML Artifact
        </Typography>
        <Box sx={{ height: '400px', border: '1px solid #ddd', borderRadius: '8px', overflow: 'hidden' }}>
          <HtmlArtifact
            content={mockHtmlContent}
            skillsets={mockSkillsets}
            tool={{
              ...mockToolInvocation,
              toolName: 'generate_html',
            }}
            data={{ html: mockHtmlContent }}
          />
        </Box>
      </Box>

      {/* Markdown Artifact */}
      <Box>
        <Typography level="h2" sx={{ mb: 2 }}>
          Markdown Artifact
        </Typography>
        <Box sx={{ height: '400px', border: '1px solid #ddd', borderRadius: '8px', overflow: 'hidden' }}>
          <MarkdownArtifact
            skillsets={mockSkillsets}
            tool={{
              ...mockToolInvocation,
              toolName: 'generate_markdown',
            }}
            value={{
              content: mockMarkdownContent,
              title: 'Sample Markdown',
            }}
          />
        </Box>
      </Box>

      {/* Record Artifact */}
      <Box>
        <Typography level="h2" sx={{ mb: 2 }}>
          Record Artifact
        </Typography>
        <Box sx={{ height: '400px', border: '1px solid #ddd', borderRadius: '8px', overflow: 'hidden' }}>
          <RecordArtifact
            data={mockRecords}
            fields={mockFields}
            skillsets={mockSkillsets}
            tool={{
              ...mockToolInvocation,
              toolName: 'generate_records',
            }}
          />
        </Box>
      </Box>

      {/* Slides Artifact */}
      <Box>
        <Typography level="h2" sx={{ mb: 2 }}>
          Slides Artifact
        </Typography>
        <Box sx={{ height: '500px', border: '1px solid #ddd', borderRadius: '8px', overflow: 'hidden' }}>
          <SlidesArtifact
            slides={mockSlides}
            outline={mockSlidesOutline}
          />
        </Box>
      </Box>

      {/* Node Resources Artifact */}
      <Box>
        <Typography level="h2" sx={{ mb: 2 }}>
          Node Resources Artifact
        </Typography>
        <Box sx={{ height: '400px', border: '1px solid #ddd', borderRadius: '8px', overflow: 'hidden' }}>
          <NodeResourcesArtifact
            resources={mockNodeResources}
            skillsets={mockSkillsets}
            tool={{
              ...mockToolInvocation,
              toolName: 'generate_node_resources',
            }}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default {
  title: '@bika/ai/_AllArtifacts',
  component: AllArtifactsStory,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof AllArtifactsStory>;

type Story = StoryObj<typeof AllArtifactsStory>;

export const AllArtifacts: Story = {};
