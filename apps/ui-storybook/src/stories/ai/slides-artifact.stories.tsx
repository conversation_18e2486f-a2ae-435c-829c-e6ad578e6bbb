import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { SlidesArtifact } from '@bika/domains/ai-artifacts/slides-server-artifact/slides-artifact';
import { Slides, SlidesOutline } from '@bika/types/ai/vo';
import { FC } from 'react';

// Mock slides data
const mockSlides: Slides = [
  {
    slide_number: 1,
    title: 'Introduction to AI',
    type: 'title',
    html_content: `
      <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center; padding: 40px;">
        <h1 style="font-size: 3rem; margin-bottom: 20px; font-weight: bold;">Introduction to AI</h1>
        <h2 style="font-size: 1.5rem; margin-bottom: 30px; opacity: 0.9;">Understanding Artificial Intelligence</h2>
        <p style="font-size: 1.2rem; opacity: 0.8;">Presented by: AI Research Team</p>
        <p style="font-size: 1rem; opacity: 0.7; margin-top: 20px;">December 2023</p>
      </div>
    `,
  },
  {
    slide_number: 2,
    title: 'What is AI?',
    type: 'content',
    html_content: `
      <div style="padding: 60px; background: #f8f9fa; height: 100vh; display: flex; flex-direction: column;">
        <h1 style="font-size: 2.5rem; color: #2c3e50; margin-bottom: 40px; border-bottom: 3px solid #3498db; padding-bottom: 15px;">What is AI?</h1>
        <div style="font-size: 1.3rem; line-height: 1.8; color: #34495e;">
          <p style="margin-bottom: 25px;"><strong>Artificial Intelligence (AI)</strong> refers to the simulation of human intelligence in machines that are programmed to think and learn like humans.</p>
          <h3 style="color: #e74c3c; margin: 30px 0 20px 0;">Key Characteristics:</h3>
          <ul style="margin-left: 30px;">
            <li style="margin-bottom: 15px;">Learning from experience</li>
            <li style="margin-bottom: 15px;">Adapting to new inputs</li>
            <li style="margin-bottom: 15px;">Performing human-like tasks</li>
          </ul>
        </div>
      </div>
    `,
  },
  {
    slide_number: 3,
    title: 'AI Applications',
    type: 'list',
    html_content: `
      <div style="padding: 60px; background: #ffffff; height: 100vh; display: flex; flex-direction: column;">
        <h1 style="font-size: 2.5rem; color: #2c3e50; margin-bottom: 40px; border-bottom: 3px solid #e67e22; padding-bottom: 15px;">AI Applications</h1>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; flex: 1;">
          <div>
            <h3 style="color: #27ae60; margin-bottom: 20px; font-size: 1.5rem;">Current Applications</h3>
            <ul style="font-size: 1.2rem; line-height: 2; color: #2c3e50;">
              <li>🚗 Autonomous Vehicles</li>
              <li>🏥 Medical Diagnosis</li>
              <li>💬 Virtual Assistants</li>
              <li>🛒 Recommendation Systems</li>
            </ul>
          </div>
          <div>
            <h3 style="color: #8e44ad; margin-bottom: 20px; font-size: 1.5rem;">Future Possibilities</h3>
            <ul style="font-size: 1.2rem; line-height: 2; color: #2c3e50;">
              <li>🤖 General AI</li>
              <li>🧬 Drug Discovery</li>
              <li>🌍 Climate Modeling</li>
              <li>🚀 Space Exploration</li>
            </ul>
          </div>
        </div>
      </div>
    `,
  },
];

// Mock outline data
const mockOutline: SlidesOutline = {
  outline: {
    title: 'Introduction to AI',
    slides: [
      {
        slide_number: 1,
        slide_title: 'Introduction to AI',
        slide_type: 'title',
        slide_data: {
          subtitle: 'Understanding Artificial Intelligence',
          author: 'AI Research Team',
          date: 'December 2023',
        },
        description: 'Title slide introducing the AI presentation',
      },
      {
        slide_number: 2,
        slide_title: 'What is AI?',
        slide_type: 'content',
        slide_data: {
          sections: [
            {
              heading: 'Definition',
              text: 'Artificial Intelligence (AI) refers to the simulation of human intelligence in machines.',
            },
            {
              heading: 'Key Characteristics',
              text: 'Learning from experience, adapting to new inputs, performing human-like tasks.',
            },
          ],
        },
        description: 'Defines AI and its key characteristics',
      },
      {
        slide_number: 3,
        slide_title: 'AI Applications',
        slide_type: 'list',
        slide_data: {
          list_type: 'bullet',
          items: [
            { text: 'Autonomous Vehicles' },
            { text: 'Medical Diagnosis' },
            { text: 'Virtual Assistants' },
            { text: 'Recommendation Systems' },
          ],
        },
        description: 'Lists current and future AI applications',
      },
    ],
  },
};

const SlidesArtifactStory: FC<{
  slides?: Slides;
  outline?: SlidesOutline;
}> = ({ slides = mockSlides, outline = mockOutline }) => {
  return (
    <div className="w-full h-[800px]">
      <SlidesArtifact slides={slides} outline={outline} />
    </div>
  );
};

export default {
  title: '@bika/ai/SlidesArtifact',
  component: SlidesArtifactStory,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  args: {
    slides: mockSlides,
    outline: mockOutline,
  },
} satisfies Meta<typeof SlidesArtifactStory>;

type Story = StoryObj<typeof SlidesArtifactStory>;

export const Default: Story = {};

export const SingleSlide: Story = {
  args: {
    slides: [mockSlides[0]],
    outline: {
      outline: {
        title: 'Single Slide Presentation',
        slides: [mockOutline.outline.slides[0]],
      },
    },
  },
};

export const EmptySlides: Story = {
  args: {
    slides: [],
    outline: {
      outline: {
        title: 'Empty Presentation',
        slides: [],
      },
    },
  },
};

export const TechnicalPresentation: Story = {
  args: {
    slides: [
      {
        slide_number: 1,
        title: 'Code Example',
        type: 'code',
        html_content: `
          <div style="padding: 60px; background: #1e1e1e; color: #d4d4d4; height: 100vh; display: flex; flex-direction: column;">
            <h1 style="color: #569cd6; margin-bottom: 30px; font-size: 2.5rem;">JavaScript Example</h1>
            <pre style="background: #2d2d30; padding: 30px; border-radius: 8px; font-size: 1.1rem; line-height: 1.6; overflow: auto; flex: 1;">
<code style="color: #d4d4d4;">
<span style="color: #569cd6;">function</span> <span style="color: #dcdcaa;">fibonacci</span>(<span style="color: #9cdcfe;">n</span>) {
  <span style="color: #c586c0;">if</span> (<span style="color: #9cdcfe;">n</span> <= <span style="color: #b5cea8;">1</span>) {
    <span style="color: #c586c0;">return</span> <span style="color: #9cdcfe;">n</span>;
  }
  <span style="color: #c586c0;">return</span> <span style="color: #dcdcaa;">fibonacci</span>(<span style="color: #9cdcfe;">n</span> - <span style="color: #b5cea8;">1</span>) + <span style="color: #dcdcaa;">fibonacci</span>(<span style="color: #9cdcfe;">n</span> - <span style="color: #b5cea8;">2</span>);
}

<span style="color: #6a9955;">// Calculate the 10th Fibonacci number</span>
<span style="color: #9cdcfe;">console</span>.<span style="color: #dcdcaa;">log</span>(<span style="color: #dcdcaa;">fibonacci</span>(<span style="color: #b5cea8;">10</span>));
</code>
            </pre>
            <p style="margin-top: 20px; font-size: 1.2rem; color: #ce9178;">This function calculates Fibonacci numbers recursively.</p>
          </div>
        `,
      },
    ],
    outline: {
      outline: {
        title: 'Technical Presentation',
        slides: [
          {
            slide_number: 1,
            slide_title: 'Code Example',
            slide_type: 'code',
            slide_data: {
              language: 'javascript',
              code_snippet: 'function fibonacci(n) { ... }',
              explanation: 'Recursive Fibonacci implementation',
            },
            description: 'Shows a JavaScript code example',
          },
        ],
      },
    },
  },
};

export const DataVisualization: Story = {
  args: {
    slides: [
      {
        slide_number: 1,
        title: 'Sales Data',
        type: 'chart',
        html_content: `
          <div style="padding: 60px; background: #ffffff; height: 100vh; display: flex; flex-direction: column;">
            <h1 style="color: #2c3e50; margin-bottom: 40px; font-size: 2.5rem; text-align: center;">Sales Performance Q4 2023</h1>
            <div style="display: flex; justify-content: space-around; align-items: center; flex: 1;">
              <div style="text-align: center;">
                <div style="width: 150px; height: 150px; border-radius: 50%; background: conic-gradient(#3498db 0deg 144deg, #e74c3c 144deg 216deg, #2ecc71 216deg 360deg); display: flex; align-items: center; justify-content: center; margin: 0 auto 20px;">
                  <div style="width: 100px; height: 100px; border-radius: 50%; background: white; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; font-weight: bold;">100%</div>
                </div>
                <h3 style="color: #34495e;">Revenue Distribution</h3>
              </div>
              <div style="display: flex; flex-direction: column; gap: 20px;">
                <div style="display: flex; align-items: center; gap: 15px;">
                  <div style="width: 20px; height: 20px; background: #3498db; border-radius: 3px;"></div>
                  <span style="font-size: 1.2rem;">Product A: 40%</span>
                </div>
                <div style="display: flex; align-items: center; gap: 15px;">
                  <div style="width: 20px; height: 20px; background: #e74c3c; border-radius: 3px;"></div>
                  <span style="font-size: 1.2rem;">Product B: 20%</span>
                </div>
                <div style="display: flex; align-items: center; gap: 15px;">
                  <div style="width: 20px; height: 20px; background: #2ecc71; border-radius: 3px;"></div>
                  <span style="font-size: 1.2rem;">Product C: 40%</span>
                </div>
              </div>
            </div>
          </div>
        `,
      },
    ],
    outline: {
      outline: {
        title: 'Data Visualization',
        slides: [
          {
            slide_number: 1,
            slide_title: 'Sales Data',
            slide_type: 'chart',
            slide_data: {
              chart_type: 'pie',
              data_description: 'Q4 2023 sales performance by product',
              chart_title: 'Revenue Distribution',
            },
            description: 'Pie chart showing sales distribution',
          },
        ],
      },
    },
  },
};
