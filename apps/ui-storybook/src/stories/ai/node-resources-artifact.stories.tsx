import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import type { ToolInvocation } from '@ai-sdk/ui-utils';
import { NodeResourcesArtifact } from '@bika/domains/ai-artifacts';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import type { NodeResource } from '@bika/types/node/bo';
import { FC } from 'react';

// Mock data for SkillsetSelectDTO
const mockSkillsets: SkillsetSelectDTO[] = [
  { kind: 'preset', key: 'default' },
  { kind: 'preset', key: 'bika-ai-page' },
  { kind: 'preset', key: 'bika-database' },
];

// Mock data for ToolInvocation
const mockToolInvocation: ToolInvocation = {
  toolName: 'generate_node_resources',
  toolCallId: 'node-resources-artifact-call-id',
  state: 'result',
  args: {
    prompt: 'Generate node resources',
  },
  result: {
    resources: [],
  },
};

// Mock node resources data
const mockNodeResources: NodeResource[] = [
  {
    resourceType: 'FOLDER',
    id: 'folder-1',
    name: 'Project Resources',
    description: 'Main folder containing all project resources',
    children: [
      {
        resourceType: 'DATABASE',
        id: 'database-1',
        name: 'Customer Database',
        description: 'Database containing customer information',
        databaseType: 'DATUM',
        fields: [
          {
            name: 'Customer Name',
            type: 'SingleLineText',
            id: 'field-1',
          },
          {
            name: 'Email',
            type: 'Email',
            id: 'field-2',
          },
          {
            name: 'Phone',
            type: 'Phone',
            id: 'field-3',
          },
        ],
      },
      {
        resourceType: 'AUTOMATION',
        id: 'automation-1',
        name: 'Customer Onboarding',
        description: 'Automated workflow for customer onboarding process',
        triggers: [
          {
            type: 'RECORD_CREATED',
            databaseId: 'database-1',
          },
        ],
        actions: [
          {
            type: 'SEND_EMAIL',
            template: 'Welcome Email',
          },
        ],
      },
    ],
  },
  {
    resourceType: 'AI',
    id: 'ai-agent-1',
    name: 'Customer Support Agent',
    description: 'AI agent for handling customer support queries',
    agentType: 'CHAT',
    model: 'gpt-4',
    systemPrompt: 'You are a helpful customer support agent.',
  },
  {
    resourceType: 'DASHBOARD',
    id: 'dashboard-1',
    name: 'Sales Dashboard',
    description: 'Dashboard showing sales metrics and KPIs',
    widgets: [
      {
        type: 'CHART',
        title: 'Monthly Sales',
        chartType: 'line',
      },
      {
        type: 'METRIC',
        title: 'Total Revenue',
        value: '$125,000',
      },
    ],
  },
];

const simpleNodeResources: NodeResource[] = [
  {
    resourceType: 'DATABASE',
    id: 'simple-db',
    name: 'Simple Database',
    description: 'A basic database example',
    databaseType: 'DATUM',
    fields: [
      {
        name: 'Title',
        type: 'SingleLineText',
        id: 'field-title',
      },
      {
        name: 'Status',
        type: 'SingleSelect',
        id: 'field-status',
      },
    ],
  },
];

const NodeResourcesArtifactStory: FC<{
  resources?: NodeResource[];
  skillsets?: SkillsetSelectDTO[];
  tool?: ToolInvocation;
  expandable?: boolean;
}> = ({ 
  resources = mockNodeResources, 
  skillsets = mockSkillsets, 
  tool = mockToolInvocation, 
  expandable = true 
}) => {
  return (
    <div className="p-4 w-full h-[600px]">
      <NodeResourcesArtifact
        resources={resources}
        skillsets={skillsets}
        tool={tool}
        expandable={expandable}
      />
    </div>
  );
};

export default {
  title: '@bika/ai/NodeResourcesArtifact',
  component: NodeResourcesArtifactStory,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  args: {
    resources: mockNodeResources,
    skillsets: mockSkillsets,
    tool: mockToolInvocation,
    expandable: true,
  },
} satisfies Meta<typeof NodeResourcesArtifactStory>;

type Story = StoryObj<typeof NodeResourcesArtifactStory>;

export const Default: Story = {};

export const SimpleResources: Story = {
  args: {
    resources: simpleNodeResources,
    tool: {
      ...mockToolInvocation,
      toolName: 'generate_simple_resources',
      args: {
        prompt: 'Generate simple node resources',
      },
    },
  },
};

export const EmptyResources: Story = {
  args: {
    resources: [],
    tool: {
      ...mockToolInvocation,
      toolName: 'generate_empty_resources',
      args: {
        prompt: 'Generate empty resource set',
      },
    },
  },
};

export const DatabaseOnly: Story = {
  args: {
    resources: [mockNodeResources[0].children![0]],
    tool: {
      ...mockToolInvocation,
      toolName: 'generate_database_resource',
      args: {
        prompt: 'Generate database resource only',
      },
    },
  },
};

export const AIAgentOnly: Story = {
  args: {
    resources: [mockNodeResources[1]],
    tool: {
      ...mockToolInvocation,
      toolName: 'generate_ai_agent',
      args: {
        prompt: 'Generate AI agent resource',
      },
    },
  },
};

export const NotExpandable: Story = {
  args: {
    resources: simpleNodeResources,
    expandable: false,
  },
};

export const MinimalSkillsets: Story = {
  args: {
    resources: simpleNodeResources,
    skillsets: [{ kind: 'preset', key: 'default' }],
  },
};

export const CallState: Story = {
  args: {
    resources: simpleNodeResources,
    tool: {
      ...mockToolInvocation,
      state: 'call',
    },
  },
};
