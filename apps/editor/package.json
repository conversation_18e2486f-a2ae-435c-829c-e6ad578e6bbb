{"name": "@bika/editor-next", "version": "1.9.1-alpha.1", "private": true, "scripts": {"dev": "dotenv -e ../web/.env.local -- next dev -p 4000", "dev:turbo": "dotenv -e ../web/.env.local -- next dev --turbo -p 4000", "build": "next build --experimental-build-mode compile", "start": "next start", "lint": "next lint"}, "dependencies": {"@bika/api-caller": "workspace:*", "@bika/contents": "workspace:*", "@bika/domains": "workspace:*", "@bika/server": "workspace:*", "@bika/types": "workspace:*", "@bika/ui": "workspace:*", "@mdx-js/loader": "^3.0.0", "@mdx-js/react": "^3.0.0", "@next/mdx": "15.2.3", "@types/mdx": "^2.0.10", "next": "15.3.3", "react": "18.3.1", "react-dom": "18.3.1", "sharelib": "workspace:^"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.2.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5.7.2"}}