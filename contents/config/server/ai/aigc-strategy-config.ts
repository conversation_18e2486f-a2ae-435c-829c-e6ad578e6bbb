/**
 * 配置AIGC策略
 */
import type { IAigcStrategy } from './aigc-strategy-types';
/**
 * 除了「生成内容」以为，我们还有“基于模板，组装网页”，包括：
 *
 * - 模板中心静态页, /template/{XXX}
 * - 模板落地页，/solution/{XXX}
 *
 * - TODO: 模板独立Blog文章，把README抽出来，/blog/template/{XXX}，塞进去生成的图片：Screenshot、TEMPLATE_USE_CASES_PHOTO、架构图
 * - TODO: 可下载的生成解决方案PPT
 *
 */
export const AIGCStrategyConfigs: IAigcStrategy[] = [
  {
    // 每周发一张小红书封面图，针对模板，对其personas和usecases生成图片
    strategy: 'TEMPLATE_PHOTO',
    output: 'redbook',
    photoFile: './template-red-book.tpl.svg',
    locales: ['zh-CN'],
  },
  {
    // 针对模板，对其personas和usecases生成英文版的图片
    strategy: 'TEMPLATE_PHOTO',
    output: 'use-case',
    photoFile: './template-use-cases-cover_en.svg',
    locales: ['en', 'ja', 'zh-TW'],
  },
  {
    // 针对模板，对其personas和usecases生成英文的横版图片
    strategy: 'TEMPLATE_PHOTO',
    output: 'banner',
    photoFile: './template-banner-en.svg',
    locales: ['en'],
  },
  {
    // 针对模板，对其personas和usecases生成日语的横版图片
    strategy: 'TEMPLATE_PHOTO',
    output: 'banner',
    photoFile: './template-banner-ja.svg',
    locales: ['ja'],
  },
  {
    // 针对模板，对其personas和usecases生成繁体中文的横版图片
    strategy: 'TEMPLATE_PHOTO',
    output: 'banner',
    photoFile: './template-banner-zh-TW.svg',
    locales: ['zh-TW'],
  },
  {
    // 针对模板，对其personas和usecases生成简体中文的横版图片
    strategy: 'TEMPLATE_PHOTO',
    output: 'banner',
    photoFile: './template-banner-zh-CN.svg',
    locales: ['zh-CN'],
  },
  {
    // 每个template生成一篇包含关键词 presentation-software 的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'presentation-software', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-presentation-software.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
    {
    // 每个template生成一篇包含关键词 ai content detector 的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'ai-content-detector', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-ai-content-detector.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个template生成一篇包含关键词 marketing automation tools 的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'marketing-automation-tools', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-marketing-automation-tools.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个template生成一篇包含关键词 my apps 的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'my-apps', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-my-apps.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个template生成一篇包含关键词 rss reader 的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'rss-reader', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-rss-reader.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个template生成一篇包含关键词 outlook email 的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'outlook-email', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-outlook-email.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
    {
    // 每个template生成一篇包含关键词 chatgpt 的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'chatgpt', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-chatgpt.tpl.md',
    locales: ['en','zh-CN'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个template生成一篇包含关键词 Agent Swarm 的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'agent-swarm', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-agent-swarm.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个template生成一篇包含关键词 best email client for mac 的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'best-email-client-for-mac', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-best-email-client-for-mac.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个template生成一篇包含关键词 habit tracker 的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'habit-tracker', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-habit-tracker.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个template生成一篇包含关键词 ai calendar 的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'ai-calendar', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-ai-calendar.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个usecases生成一篇包含关键词 ai data automation 的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'ai-data-automation', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-ai-data-automation-use-cases.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个模板生成一篇包含关键词 ai data automation 的文章
    strategy: 'TEMPLATE_AI',
    output: 'ai-data-automation', // 生成路径在/blog/productivity/use-case-{pmo}-youtube-sharing.....
    promptFile: 'template-ai-data-automation.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个persona生成一篇包含关键词 ai data automation 的文章
    strategy: 'TEMPLATE_AI',
    loopPersonas: true,
    output: 'ai-data-automation', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-ai-data-automation-persona.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个UseCase生成一篇包含关键词 data automation 的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'data-automation', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-data-automation-use-cases.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个模板生成一篇包含关键词 data automation 的文章
    strategy: 'TEMPLATE_AI',
    output: 'data-automation', // 生成路径在/blog/productivity/use-case-{pmo}-youtube-sharing.....
    promptFile: 'template-data-automation.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个persona生成一篇包含关键词 data automation 的文章
    strategy: 'TEMPLATE_AI',
    loopPersonas: true,
    output: 'data-automation', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-data-automation-persona.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个useCases生成一篇包含关键词 zapier pricing 的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'zapier-pricing', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-zapier-pricing-use-cases.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个useCases生成一篇包含关键词 Airtable pricing 的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'airtable-pricing', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-airtable-pricing-use-cases.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个模板生成一篇包含关键词 Airtable pricing 的文章
    strategy: 'TEMPLATE_AI',
    output: 'airtable-pricing', // 生成路径在/blog/productivity/use-case-{pmo}-youtube-sharing.....
    promptFile: 'template-airtable-pricing.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个persona生成一篇包含关键词 Airtable pricing 的文章
    strategy: 'TEMPLATE_AI',
    loopPersonas: true,
    output: 'airtable-pricing', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-airtable-pricing-persona.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个persona生成一篇包含关键词 Zapier pricing 的文章
    strategy: 'TEMPLATE_AI',
    loopPersonas: true,
    output: 'zapier-pricing', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-zapier-pricing-persona.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个模板生成一篇包含关键词 Zapier pricing 的文章
    strategy: 'TEMPLATE_AI',
    output: 'zapier-pricing', // 生成路径在/blog/productivity/use-case-{pmo}-youtube-sharing.....
    promptFile: 'template-zapier-pricing.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个useCase 生成一篇包含关键词 Airtable Alternative的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'airtable-alternative', // 生成路径在/blog/productivity/use-case-{pmo}-youtube-sharing.....
    promptFile: 'template-airtable-alternative-use-cases.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个useCase 生成一篇包含关键词 Zapier Alternative的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'zapier-alternative', // 生成路径在/blog/productivity/use-case-{pmo}-youtube-sharing.....
    promptFile: 'template-zapier-alternative-use-cases.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个useCase 生成一篇包含关键词 vs Zapier的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'vs-zapier', // 生成路径在/blog/productivity/use-case-{pmo}-youtube-sharing.....
    promptFile: 'template-vs-zapier-use-cases.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个模板生成一篇包含关键词 vs Zapier的文章
    strategy: 'TEMPLATE_AI',
    output: 'vs-zapier', // 生成路径在/blog/productivity/use-case-{pmo}-youtube-sharing.....
    promptFile: 'template-vs-zapier.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个模板生成一篇包含关键词 vs Airtable 的文章
    strategy: 'TEMPLATE_AI',
    output: 'vs-airtable', // 生成路径在/blog/productivity/use-case-{pmo}-youtube-sharing.....
    promptFile: 'template-vs-airtable.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个useCase 生成一篇包含关键词 vs Airtable的文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'vs-airtable', // 生成路径在/blog/productivity/use-case-{pmo}-youtube-sharing.....
    promptFile: 'template-vs-airtable-use-cases.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个persona生成一篇包含关键词 Airtable Alternative的小语种文章
    strategy: 'TEMPLATE_AI',
    loopPersonas: true,
    output: 'airtable-alternative', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-airtable-alternative-persona-extendlocale.tpl.md',
    locales: ['fr', 'de', 'es', 'ru', 'it', 'vi'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个模板生成一篇包含关键词 Airtable Alternative的小语种文章
    strategy: 'TEMPLATE_AI',
    output: 'airtable-alternative', // 生成路径在/blog/productivity/use-case-{pmo}-youtube-sharing.....
    promptFile: './template-airtable-alternative-extendlocale.tpl.md',
    locales: ['fr', 'de', 'es', 'ru', 'it', 'vi'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个persona生成一篇包含关键词 Zapier Alternative的小语种文章
    strategy: 'TEMPLATE_AI',
    loopPersonas: true,
    output: 'zapier-alternative', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-zapier-alternative-persona-extendlocale.tpl.md',
    locales: ['fr', 'de', 'es', 'ru', 'it', 'vi'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个模板生成一篇包含关键词 Zapier Alternative的文章
    strategy: 'TEMPLATE_AI',
    output: 'zapier-alternative', // 生成路径在/blog/productivity/use-case-{pmo}-youtube-sharing.....
    promptFile: 'template-zapier-alternative-extendlocale.tpl.md',
    locales: ['fr', 'de', 'es', 'ru', 'it', 'vi'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个persona生成一篇包含关键词 Zapier Alternative的文章
    strategy: 'TEMPLATE_AI',
    loopPersonas: true,
    output: 'zapier-alternative', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-zapier-alternative-persona.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个模板生成一篇包含关键词 Zapier Alternative的文章
    strategy: 'TEMPLATE_AI',
    output: 'zapier-alternative', // 生成路径在/blog/productivity/use-case-{pmo}-youtube-sharing.....
    promptFile: 'template-zapier-alternative.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个模板生成一篇包含关键词Airtable Alternative的文章
    strategy: 'TEMPLATE_AI',
    output: 'airtable-alternative', // 生成路径在/blog/productivity/use-case-{pmo}-youtube-sharing.....
    promptFile: 'template-airtable-alternative.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个persona生成一篇包含关键词Airtable Alternative的文章
    strategy: 'TEMPLATE_AI',
    loopPersonas: true,
    output: 'airtable-alternative', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-airtable-alternative-persona.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // 每个useCase生成一篇文章
    strategy: 'TEMPLATE_AI',
    loopUseCases: true,
    output: 'productivity', // 生成路径在/blog/productivity/use-case-{pmo}-youtube-sharing.....
    promptFile: 'use-cases-templates.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // AI生成固定文章:  如果生成过，我就不生成；这种适合生成完之后，进行本地修改
    // 每个persona生成一篇文章
    strategy: 'TEMPLATE_AI',
    loopPersonas: true,
    output: 'productivity', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './persona-template.tpl.md',
    locales: ['en', 'zh-TW', 'zh-CN', 'ja'],
    model: 'doubao-pro-256k',
  },
  {
    // AI生成固定文章:  如果生成过，我就不生成；这种适合生成完之后，进行本地修改
    // 每个模板生成一篇中文文章，用于知乎
    strategy: 'TEMPLATE_AI',
    output: 'productivity', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-blog-use-cases-zh-cn.tpl.md',
    locales: ['zh-CN'],
    model: 'doubao-pro-256k',
  },
  {
    // AI生成固定文章:  如果生成过，我就不生成；这种适合生成完之后，进行本地修改
    // 每个模板生成一篇blog文章,用于Medium
    strategy: 'TEMPLATE_AI',
    output: 'productivity', // 生成路径在/blog/productivity/persona-{pmo}-youtube-sharing.....
    promptFile: './template-blog-use-cases.tpl.md',
    locales: ['en', 'ja', 'zh-TW'],
    model: 'doubao-pro-256k',
  },
  {
    // 针对模板，生成小红书内容(短文)
    strategy: 'TEMPLATE_AI',
    output: 'short', // 生成路径在/blog/short/persona-{pmo}-youtube-sharing.....
    promptFile: './template-red-book-use-cases.prompt.md',
    locales: ['zh-CN'],
    model: 'doubao-pro-256k',
  },
  {
    // 针对模板，生成tweet&LinkedIn动态
    strategy: 'TEMPLATE_AI',
    output: 'short', // 生成路径在/blog/short/persona-{pmo}-youtube-sharing.....
    promptFile: './tweet-template.tpl.md',
    locales: ['en'],
    model: 'doubao-pro-256k',
  },
  {
    // AI生成固定文章:  如果生成过，我就不生成；这种适合生成完之后，进行本地修改
    // 每个模板生成一篇中文文章，用于知乎
    strategy: 'TEMPLATE_AI',
    dynamic: true,
    output: 'short', // bika-content独立工程
    promptFile: './template-blog-use-cases-zh-cn.tpl.md',
    locales: ['zh-CN'],
    model: 'doubao-pro-256k',
  },
];
