---
sidebar_label: 'Video: Quick Start Bika.ai？ - AI Organizer for Building Agentic AI Teams'
title: 'Video: Quick Start Bika.ai？ - AI Organizer for Building Agentic AI Teams'
date: '2024-09-18'
author: <PERSON>
description: 'Bika.ai, the first AI organizer for vibe working. Build your own agentic AI team combines AI agents, automation, databases, dashboards, and documents. Just chat, build, manage agentic AI teams like a messenger app across sales, marketing, research, design, engineering, and more.'
cover: '/assets/blog/what-is-bika-ai/blog-cover.zh-CN.png'
---
import { BlogVideo } from '../../../components/blog-video';


<BlogVideo locale={'en'} videoType={'ONBOARDING'} />

<br/><br/>
Let's quickly get started with Bika.ai and see how you can learn new AI automation skills in just 3 minutes, freeing up your time for the future.

1. Sign up at [Bika.ai](https://bika.ai/signup).
2. Go to "Template Center" on the left.
3. Install the "Slack Channel Scheduled Notifications" template.
4. Create a Slack app to obtain a Webhook URL.
5. Enter the Webhook URL into the Webhook Source in the template.
6. Test the automation by clicking "Run now."
7. Adjust the content and timing of the auto-sending message.
8. Confirm settings with another "Run now."
9. Enable automation by switching to ON.

Below is a brief introduction to the principles.