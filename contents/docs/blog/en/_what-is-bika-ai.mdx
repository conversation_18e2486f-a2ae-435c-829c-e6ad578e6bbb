

import { QA } from '../../components/qa';
import What from './_what.mdx'
import Tutorial from './_tutorial.mdx'
import Concept from './_concept.mdx'
import UseCases from './_use_cases.mdx'
import Features from './_features.mdx'
import { CTA } from '../../components/cta';
import { Templates } from '../../components/templates';



> This article is a beginner's guide to the AI Organizer platform [Bika.ai](https://bika.ai)
>
> [Bika.ai](https://bika.ai) is the first AI Organizer platform that can help you build your own agentic AI team combines AI agents, automation, databases, dashboards, and documents. 
>
> Vibe working with your agentic AI employees every day, they will proactively help you get jobs done. Build your own one-person AI company is not a dream any more. Just chat, build, manage agentic AI teams like a messenger app.

<What />
<Tutorial />
<UseCases />
<Concept />
<Features />

## Completely Free Trial

<CTA locale={'en'} />

<br />

Currently, Bika.ai provides a free plan. You can refer to the free specifications for details, see our [pricing page](https://bika.ai/pricing).

If you have ideas and want to suggest some features, please join our community to give feedback and make suggestions: https://community.bika.ai

If you have further needs such as private deployment or feature scheduling, please contact our [sales manager](?m=eyJuYW1lIjoiQ09OVEFDVF9TRVJWSUNFIn0=).


## Frequently Asked Questions: What is Bika.ai?

<QA locale={'en'} />

<br />


import { toUSVString } from 'util';

import { SloganKeywords } from '../../components/slogan-keywords';

<SloganKeywords locale={'en'} />