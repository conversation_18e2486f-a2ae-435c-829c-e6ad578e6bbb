import { z } from 'zod';
import { BaseCreateNodeResourceBOSchema, NodeResourceTypeSchema } from './base';
import { MirrorCreateClientBOSchema } from './bo-mirror';
import { AIAgentNodeCreateDTOSchema, AIPageNodeCreateDTOSchema } from '../ai/dto';
import { AutomationCreateBOSchema } from '../automation/dto-automation';
import { DashboardCreateBOSchema } from '../dashboard/dto-dashboard';
import { DatabaseCreateBOSchema } from '../database/dto-database';
import { FileCreateDTOSchema, DocumentCreateDTOSchema } from '../document/dto';
import { FormTypeSchema } from '../form/bo-form';
import { iStringSchema } from '../i18n/bo';
import { AvatarLogoSchema } from '../system/avatar';

// Folder creation schema
export const FolderCreateBOSchema = BaseCreateNodeResourceBOSchema.extend({
  resourceType: z.literal(NodeResourceTypeSchema.enum.FOLDER),
  readme: iStringSchema.optional(),
  icon: AvatarLogoSchema.optional(),
});

export type FolderCreateBO = z.infer<typeof FolderCreateBOSchema>;

// UI display is optional, used for UI rendering
// export const ViewNodeCreateClientBOSchema = BaseCreateNodeResourceBOSchema.extend({
//   resourceType: ViewNodeType,
//   databaseId: z.string().optional(),
//   viewId: z.string().optional(),
//   // viewType: ViewTypeSchema.default('TABLE'),
// });

// Server side cannot be optional
// export const ViewNodeCreateServerBOSchema = ViewNodeCreateClientBOSchema.refine(
//   (value) => value.databaseId !== undefined && value.viewId !== undefined,
// );

// export type ViewNodeCreateBO = z.infer<typeof ViewNodeCreateClientBOSchema>;
// export type ViewNodeCreateServerBO = z.output<typeof ViewNodeCreateServerBOSchema>;

// Form creation schema for server
export const FormCreateServerBOSchema = BaseCreateNodeResourceBOSchema.extend({
  resourceType: z.literal('FORM'),
  databaseId: z.string(),
  viewId: z.string(),
  cover: AvatarLogoSchema.optional(),
  brandLogo: AvatarLogoSchema.optional(),
  formType: FormTypeSchema.default('DATABASE'),
});

// Form creation schema for client (databaseId and viewId are optional)
export const FormCreateClientBOSchema = FormCreateServerBOSchema.partial({
  databaseId: true,
  viewId: true,
});

export type FormCreateBO = z.infer<typeof FormCreateClientBOSchema>;
export type FormCreateServerBO = z.infer<typeof FormCreateClientBOSchema>;

// Mirror creation schema
export const MirrorCreateBOSchema = BaseCreateNodeResourceBOSchema.extend({
  resourceType: z.literal('MIRROR'),
});
export type MirrorCreateBO = z.infer<typeof MirrorCreateBOSchema>;

// Not directly for API, wrapped by DTO, so it's BO
export const ResourceCreateDTOSchema = z
  .discriminatedUnion('resourceType', [
    FolderCreateBOSchema,
    DatabaseCreateBOSchema,
    AutomationCreateBOSchema,
    // ViewNodeCreateClientBOSchema,
    FormCreateClientBOSchema,
    DashboardCreateBOSchema,
    MirrorCreateClientBOSchema,
    DocumentCreateDTOSchema,
    FileCreateDTOSchema,
    AIAgentNodeCreateDTOSchema,
    AIPageNodeCreateDTOSchema,
  ])
  .default({
    resourceType: 'DATABASE',
    name: 'new resource',
  });
/**
 * Create Resource BO
 */
export type ResourceCreateDTO = z.infer<typeof ResourceCreateDTOSchema>;

export const NodeCreateDTOSchema = z.object({
  spaceId: z.string(),
  parentId: z.string().describe('Parent node ID'),
  data: ResourceCreateDTOSchema.describe('Created resource BO'),
});

export type NodeCreateDTO = z.infer<typeof NodeCreateDTOSchema>;

export const NodeCreateDTOSchemaWithoutSpaceId = NodeCreateDTOSchema.omit({ spaceId: true });
export type NodeCreateDTOWithoutSpaceId = Omit<NodeCreateDTO, 'spaceId'>;
