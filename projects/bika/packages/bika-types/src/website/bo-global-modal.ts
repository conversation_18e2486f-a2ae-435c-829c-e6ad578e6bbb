import { z } from 'zod';
import { HelpVideoTypeSchema } from './bo-help-video';
import { AIIntentParamsSchema } from '../ai/bo-intent-params';
import { UserVOSchema } from '../user/vo-user';
// const GuideIntentUI = dynamic(() => import('@bika/domains/ai/client').then((res) => res.GuideIntentUI));

export const GlobalModalNames = [
  'AUTH',
  'USER_PROFILE',
  'CONTACT_SERVICE',
  'COMING_SOON',
  'USER_EMAIL_SETTING',
  'AI_WIZARD',
  'INSTALL_TEMPLATE',
  'HELP_VIDEO',
  'AI_ARTIFACT',
] as const;

export const GlobalModalNameSchema = z.enum(GlobalModalNames);
export type GlobalModalName = z.infer<typeof GlobalModalNameSchema>;

export const GlobalModalInstallTemplateActionTypeSchema = z.enum([
  'space_install',
  'update',
  'web_install',
  'template_feedback',
]);
export type GlobalModalInstallTemplateActionType = z.infer<typeof GlobalModalInstallTemplateActionTypeSchema>;

export const GlobalModalInstallTemplateSchema = z.object({
  name: z.literal(GlobalModalNameSchema.enum.INSTALL_TEMPLATE),

  type: GlobalModalInstallTemplateActionTypeSchema,
  templateId: z.string(),
  nodeId: z.string().or(z.array(z.string())).optional(),
  /**
   * When this parameter exists, don't show the space selection prompt
   */
  spaceId: z.string().optional(),
  user: UserVOSchema.optional(),
});
export type GlobalModalInstallTemplateConfig = z.infer<typeof GlobalModalInstallTemplateSchema>;

export const GlobalModalConfigSchema = z.discriminatedUnion('name', [
  z.object({
    name: z.literal(GlobalModalNameSchema.enum.AUTH),
    redirect: z.string().optional(),
    onLoginAction: z.enum(['CLOSE_MODAL']),
  }),
  z.object({
    name: z.literal(GlobalModalNameSchema.enum.USER_PROFILE),
  }),
  z.object({
    name: z.literal(GlobalModalNameSchema.enum.CONTACT_SERVICE),
    defaultTab: z.number().optional(),
  }),
  z.object({
    name: z.literal(GlobalModalNameSchema.enum.COMING_SOON),
    defaultTab: z.number().optional(),
  }),
  GlobalModalInstallTemplateSchema,
  z.object({
    name: z.literal(GlobalModalNameSchema.enum.USER_EMAIL_SETTING),
  }),

  z.object({
    name: z.literal(GlobalModalNameSchema.enum.AI_WIZARD),
    // If spaceId is provided, run as member, otherwise run as user
    initIntent: AIIntentParamsSchema.optional(),
    initWizardId: z.string().optional(),
    initUserMessage: z.string().optional(),
  }),
  z.object({
    name: z.literal(GlobalModalNameSchema.enum.HELP_VIDEO),
    videoType: HelpVideoTypeSchema,
  }),
  z.object({
    name: z.literal(GlobalModalNameSchema.enum.AI_ARTIFACT),
  }),
]);
export type GlobalModalConfig = z.infer<typeof GlobalModalConfigSchema>;
