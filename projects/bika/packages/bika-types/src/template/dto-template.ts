import { z } from 'zod';
import { TemplateAuthorDisplaySchema, TemplateCategorySchema, TemplateVisibilityEnumSchema } from './bo-template-base';
import { iStringSchema } from '../i18n/bo';
import { MissionSchema } from '../mission/missions';
import { AvatarLogoSchema } from '../system/avatar';

export const StoreTemplateUpdateDTOSchema = z.object({
  name: iStringSchema.optional(),
  description: iStringSchema.optional(),
  cover: z.union([AvatarLogoSchema, z.string()]).optional(),
  category: TemplateCategorySchema.optional(),
  readme: iStringSchema.optional(),
  visibility: TemplateVisibilityEnumSchema.optional(),
  detach: z.boolean().optional(),
  authorDisplay: TemplateAuthorDisplaySchema.optional(),
  templateId: z.string().optional(),
  useCases: iStringSchema.optional(),
  keywords: iStringSchema.optional(),
});

export type StoreTemplateUpdateDTO = z.infer<typeof StoreTemplateUpdateDTOSchema>;

export const StoreTemplateCreateDTOSchema = z.object({
  templateId: z.string().optional(),
  category: TemplateCategorySchema,
  version: z.string(),
  visibility: TemplateVisibilityEnumSchema,
  detach: z.boolean(),
  description: iStringSchema.optional(),
  authorDisplay: TemplateAuthorDisplaySchema.optional(),
  useCases: iStringSchema.optional(),
  keywords: iStringSchema.optional(),
  initMissions: z.array(MissionSchema).optional(),
  withRecords: z.boolean().optional(),
});

export type StoreTemplateCreateDTO = z.infer<typeof StoreTemplateCreateDTOSchema>;

export const TemplatePublishTypeSchema = z.enum(['TEMPLATE_CENTER', 'LOCAL']);
export const TemplatePublishType = TemplatePublishTypeSchema.Enum;

export const TemplatePublishDTOSchema = z.discriminatedUnion('type', [
  z.object({ type: z.literal(TemplatePublishType.TEMPLATE_CENTER), data: StoreTemplateCreateDTOSchema }),
  z.object({ type: z.literal(TemplatePublishType.LOCAL) }),
]);

export type TemplatePublishDTO = z.infer<typeof TemplatePublishDTOSchema>;
