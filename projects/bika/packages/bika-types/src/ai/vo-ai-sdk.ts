// Simulated AI SDK types, define schema
// Separated into a file due to circular dependency issues

import { z } from 'zod';
import { AttachmentVOSchema } from '../attachment/vo-attachment';
import { NodeTreeVOSchema } from '../node/vo-node';

export const AISourceSchema = z.object({
  id: z.string(),
  sourceType: z.literal('url'),
  url: z.string(),
  title: z.string().optional(),
});

export type AISource = z.infer<typeof AISourceSchema>;

export const AIChatOptionSchema = z.object({
  label: z.string(),
  value: z.string(),
  tips: z.string().optional(),
  disabled: z.boolean().optional(),
});
export type AIChatOption = z.infer<typeof AIChatOptionSchema>;

export const AIChatContextVOSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('node'),
    node: NodeTreeVOSchema,
  }),
  z.object({
    type: z.literal('attachment'),
    attachment: AttachmentVOSchema,
  }),
]);

// Why it is VO?  Use by Chat View, fetch API and pass Value to UI Renderer
export type AIChatContextVO = z.infer<typeof AIChatContextVOSchema>;

export const AIChatModelOptionSchema = z.object({
  label: z.string(),
  value: z.string(),
});
export type AIChatModelOption = z.infer<typeof AIChatModelOptionSchema>;

export const AIToolExecutionErrorSchema = z.object({
  name: z.literal('AI_ToolExecutionError'),
  toolCallId: z.string(),
  message: z.string(),
  errors: z.record(z.string()),
});
export type AIToolExecutionError = z.infer<typeof AIToolExecutionErrorSchema>;
