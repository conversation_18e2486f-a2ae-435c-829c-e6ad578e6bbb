import { z } from 'zod';
import { AIUsageSchema } from './bo-ai';
import { AIArtifactErrorSchema } from './bo-ai-artifact';
import { AINodeBOSchema } from './bo-ai-node';
import { SlidesArtifactSchema } from './vo-slides';
import { AutomationSchema } from '../automation/bo-automation';
import { RecordVOSchema } from '../database/vo-record';
import { NodeResourceSchema } from '../node/bo';
import { NodeDetailVOSchema } from '../node/vo-node';

export const ArtifactTypes = [
  'html',
  'code',
  'text',
  'file',
  'node-resources',
  'database',
  'automation',
  'record',
  'slides',
  // AI Agent Node
  'ai-agent',
  'image-text',
] as const;
export const ArtifactTypeSchema = z.enum(ArtifactTypes);

export type ArtifactType = z.infer<typeof ArtifactTypeSchema>;

export const ArtifactAINodeAgentSchema = AINodeBOSchema.pick({
  name: true,
  description: true,
  skillsets: true,
});
export type ArtifactAINodeAgent = z.infer<typeof ArtifactAINodeAgentSchema>;

export const ArtifactVOSchema = z.discriminatedUnion('type', [
  z.object({
    id: z.string(), // node id & document id
    type: z.literal(ArtifactTypeSchema.enum.html),
    data: z
      .object({
        html: z.string(),
      })
      .partial()
      .optional(),
    usage: AIUsageSchema.optional(),
    error: AIArtifactErrorSchema.optional(),
  }),
  z.object({
    id: z.string(), // node id & document id
    type: z.literal(ArtifactTypeSchema.enum.code),
    data: z
      .object({
        code: z.string(),
        language: z.string(),
      })
      .partial()
      .optional(),
    error: AIArtifactErrorSchema.optional(),
  }),
  z.object({
    id: z.string(),
    type: z.literal(ArtifactTypeSchema.enum.text),
    data: z.string(),
    usage: AIUsageSchema.optional(),
    error: AIArtifactErrorSchema.optional(),
  }),
  z.object({
    id: z.string(),
    type: z.literal(ArtifactTypeSchema.enum.file),
    data: z
      .object({
        filePath: z.string(),
        content: z.string(),
      })
      .partial()
      .optional(),
    usage: AIUsageSchema.optional(),
  }),
  z.object({
    id: z.string(),
    type: z.literal(ArtifactTypeSchema.enum['node-resources']),
    data: z.array(NodeResourceSchema),
    error: AIArtifactErrorSchema.optional(),
  }),
  z.object({
    id: z.string(),
    type: z.literal(ArtifactTypeSchema.enum.database),
    data: z.array(NodeDetailVOSchema), // .or(z.any())),
    error: AIArtifactErrorSchema.optional(),
  }),
  z.object({
    id: z.string(),
    type: z.literal(ArtifactTypeSchema.enum.automation),
    data: AutomationSchema,
    error: AIArtifactErrorSchema.optional(),
  }),
  z.object({
    id: z.string(),
    type: z.literal(ArtifactTypeSchema.enum.record),
    data: z.array(RecordVOSchema),
    error: AIArtifactErrorSchema.optional(),
  }),
  z.object({
    id: z.string(),
    type: z.literal(ArtifactTypeSchema.enum.slides),
    data: SlidesArtifactSchema,
    usage: AIUsageSchema.optional(),
    error: AIArtifactErrorSchema.optional(),
  }),
  z.object({
    id: z.string(),
    type: z.literal(ArtifactTypeSchema.enum['ai-agent']),
    data: AINodeBOSchema,
    usage: AIUsageSchema.optional(),
    error: AIArtifactErrorSchema.optional(),
  }),
  z.object({
    id: z.string(),
    type: z.literal(ArtifactTypeSchema.enum['image-text']),
    data: z.object({
      imageUrl: z.string(),
      text: z.string(),
    }),
    usage: AIUsageSchema.optional(),
    error: AIArtifactErrorSchema.optional(),
  }),
]);

export type ArtifactVO = z.infer<typeof ArtifactVOSchema>;
