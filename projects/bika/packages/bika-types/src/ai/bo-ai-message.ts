import { z } from 'zod';
import { AISourceSchema } from './vo-ai-sdk';
import { AIIntentUIVOSchemas } from './vo-intent-ui';
import { iStringSchema } from '../i18n/bo';
import { SkillsetSelectDTOSchema } from '../skill/dto';
import { TalkDetailVOSchema } from '../space/vo-talk';
import { UserVOSchema } from '../user/vo-user';

export const AIWizardMessageTypes = ['assistant', 'user', 'system', 'data'] as const;

// In AI interaction, there is also a SYSTEM Message, which is only used for AI LLM interaction and is not visible in AIWizard Message
export type AIWizardMessageType = z.infer<typeof AIWizardMessageTypeSchema>;

export const AIWizardMessageTypeSchema = z.enum(AIWizardMessageTypes);

// export const AIMessageToolSchema = z.object({
//   toolName: z.string(),
//   args: z.any(),
//   result: z.any().optional(),
// });
// export type AIMessageTool = z.infer<typeof AIMessageToolSchema>;

export const AIMessageVoiceBOSchema = z.object({
  attachmentId: z.string(),
  time: z.number(),
});

export type AIMessageVoiceBO = z.infer<typeof AIMessageVoiceBOSchema>;

// 由于 z.any()，会默认 optional()，因此构造一个 JSONVaulue，来避免 optional()
export const JSONValueSchema = z.string().or(z.number()).or(z.boolean()).or(z.array(z.any())).or(z.record(z.any()));
export type JSONValue = z.infer<typeof JSONValueSchema>;

export const AIMessagePartToolInvocationSchema = z.discriminatedUnion('state', [
  z.object({
    state: z.literal('partial-call'),
    step: z.number().optional(),
    toolCallId: z.string(), // `call_${id}`,
    toolName: z.string(), // msgTool.toolName,
    // any 是 option，但原 AI SDK Message 的 args 是 required,  但是由于 mongodb 持久化会 把{} 删成 undefined，这里还是 any() 以适应
    args: JSONValueSchema.default({}), /// JSONValueSchema, // z.custom<Required<any>>((x) => x !== undefined),
  }),
  z.object({
    state: z.literal('call'),
    step: z.number().optional(),
    toolCallId: z.string(), // `call_${id}`,
    toolName: z.string(), // msgTool.toolName,
    args: JSONValueSchema.default({}), // JSONValueSchema, // z.custom<Required<any>>((x) => x !== undefined),
  }),
  z.object({
    state: z.literal('result'),
    step: z.number().optional(),
    toolCallId: z.string(), // `call_${id}`,
    toolName: z.string(), // msgTool.toolName,
    args: JSONValueSchema.default({}), // z.custom<Required<any>>((x) => x !== undefined),
    result: JSONValueSchema.default({}), // z.custom<Required<any>>((x) => x !== undefined),
  }),
]);
export type AIMessagePartToolInvocation = z.infer<typeof AIMessagePartToolInvocationSchema>;

export const AIMessagePartSourceSchema = z.object({
  type: z.literal('source'),
  source: AISourceSchema,
});
export type AIMessagePartSource = z.infer<typeof AIMessagePartSourceSchema>;

export const AIMessagePartSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('text'),
    text: z.string(),
  }),
  z.object({
    type: z.literal('reasoning'),
    reasoning: z.string(),
    details: z.array(
      z.discriminatedUnion('type', [
        z.object({ type: z.literal('text'), text: z.string(), signature: z.string().optional() }),
        z.object({
          type: z.literal('redacted'),
          data: z.string(),
        }),
      ]),
    ), // you can set []
  }),
  z.object({
    type: z.literal('tool-invocation'),
    toolInvocation: AIMessagePartToolInvocationSchema,
  }),
  AIMessagePartSourceSchema,
  z.object({
    type: z.literal('file'),
    mimeType: z.string(),
    data: z.string(),
  }),
  z.object({
    type: z.literal('step-start'),
  }),
]);

export const AIMessageAnnotationSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('creator'),
    creator: UserVOSchema,
  }),
  z.object({
    type: z.literal('talk'),
    source: TalkDetailVOSchema, // TalkVO
  }),
  z.object({
    type: z.literal('prompts'),
    prompts: z.array(z.string()),
  }),
  z.object({
    type: z.literal('skillsets'),
    skillsets: z.array(SkillsetSelectDTOSchema),
  }),
  z.object({
    type: z.literal('artifact'),
    artifact: z.any(), // data
  }),
  // z
  //   .object({
  //     type: z.string(),
  //   })
  //   .catchall(z.any()),
]);
export type AIMessageAnnotation = z.infer<typeof AIMessageAnnotationSchema>;

/**
 * 100% Compatbile with AI SDK Message
 */
export const AISDKMessageSchema = z.object({
  id: z.string(), // .optional(),
  role: AIWizardMessageTypeSchema,

  // sources: z.array(AISourceSchema).optional(),
  // reasoning: iStringSchema.optional(),

  // content: z.string(),
  parts: z.array(AIMessagePartSchema).optional(),

  experimental_attachments: z
    .array(
      z.object({
        name: z.string().optional(),
        contentType: z.string().optional(),
        url: z.string(),
      }),
    )
    .optional(),

  // skillsets 集合
  // @deprecated Use annotations instead
  skillsets: z.array(SkillsetSelectDTOSchema).optional(),

  // skillsets?: SkillsetSelectDTO[],

  // Tips Prompts
  prompts: z.array(z.string()).optional(),

  // ----------------------------------------------------

  /**
   * @deprecated Use parts instead
   */
  text: iStringSchema.optional(),

  // Corresponds to AI SDK tools invocations
  /**
   * @deprecated Use parts instead
   */
  // tools: z.array(AIMessageToolSchema).optional(),

  // Last message
  /**
   * @deprecated Use parts instead
   */
  final: z.boolean().nullable().optional(),
  /**
   * @deprecated Use parts instead
   */
  loading: z.boolean().optional(),
  /**
   * @deprecated Use parts instead
   */
  showProgress: z.boolean().optional(),
  /**
   * @deprecated Use parts instead
   */
  time: z.number().optional(),
  /**
   * @deprecated Use parts instead
   */
  voice: z.union([AIMessageVoiceBOSchema, iStringSchema]).nullish(), // Can be a URL

  /**
   * Old WizardUI, gradually being replaced by tools
   *
   * @deprecated
   */
  ui: AIIntentUIVOSchemas.optional(),

  /**
   * 为了兼容 AI SDK 和老AI Wizard的 Message，可以放入任何字
   *
   * @deprecated
   */
  content: z.string(), // .optional(),

  // CreatedBy User ID, message and wizard creators are different
  createdBy: z.string().optional(),
});

export type AISDKMessage = z.infer<typeof AISDKMessageSchema>;

export const AISDKUIMessageSchema = AISDKMessageSchema.extend({
  parts: z.array(AIMessagePartSchema),
});
export type AISDKUIMessage = z.infer<typeof AISDKUIMessageSchema>;

export const AIMessageBOSchema = AISDKMessageSchema.extend({
  // 这个是为了兼容老的 AI Wizard Message
  // 现在的 AI SDK Message 都是 parts
  parts: z.array(AIMessagePartSchema),
  annotations: z.array(AIMessageAnnotationSchema).optional(),
  content: z.string().optional(), // .default(''), // .optional(),
});

//  对齐 CoreMessage，服务端持久化专用！https://ai-sdk.dev/docs/reference/ai-sdk-core/core-message
// 千万不要对齐 UIMessage！(parts 制)
export type AIMessageBO = z.infer<typeof AIMessageBOSchema>;

export function toAIMessageBO(aiSDKMessage: AISDKMessage): AIMessageBO {
  return {
    parts: aiSDKMessage.parts || [],
    ...aiSDKMessage,
    // annotations: aiSDKMessage.annotations || [],
  };
}
export function toAIMessageBOs(aiSDKMessage: AISDKMessage[]): AIMessageBO[] {
  return aiSDKMessage.map((msg) => toAIMessageBO(msg));
}
export function toAISDKMessage(aiMsgBO: AIMessageBO): AISDKMessage {
  return {
    content: aiMsgBO.content || '',
    ...aiMsgBO,
  };
}

export function toAISDKMessages(aiMsgBO: AIMessageBO[]): AISDKMessage[] {
  return aiMsgBO.map((msg) => toAISDKMessage(msg));
}

export function toUIMessages(aiMsgBO: AIMessageBO[]): AISDKUIMessage[] {
  return aiMsgBO.map((msg) => ({
    ...msg,
    parts: msg.parts || [],
    content: msg.content || '',
  }));
}
