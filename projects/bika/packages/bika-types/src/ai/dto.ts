import { z } from 'zod';
import { AINodeAgentSchema } from './bo-ai-node';
import { AiPageNodeBOSchema } from './bo-ai-page';
import { NodeUpdateBOSchema, BaseCreateNodeResourceBOSchema, NodeResourceTypeSchema } from '../node/base';
import { PaginationSchema } from '../shared/pagination';

export * from './dto-intent-ui-resolve';

// AI Page Node
export const AIPageNodeCreateDTOSchema = BaseCreateNodeResourceBOSchema.extend({
  resourceType: z.literal(NodeResourceTypeSchema.enum.PAGE),
  bo: AiPageNodeBOSchema.omit({
    name: true,
    description: true,
    templateId: true,
    resourceType: true,
  }).optional(),
});

export type AIPageNodeCreateDTO = z.infer<typeof AIPageNodeCreateDTOSchema>;

export const AIPageNodeUpdateDTOSchema = NodeUpdateBOSchema.extend({
  resourceType: z.literal(NodeResourceTypeSchema.enum.PAGE),
  // name: iStringSchema,
  bo: AiPageNodeBOSchema.omit({
    name: true,
    description: true,
    templateId: true,
    resourceType: true,
  }).optional(),
});

export type AIPageNodeUpdateDTO = z.infer<typeof AIPageNodeUpdateDTOSchema>;

// AI Agent Node
export const AIAgentNodeCreateDTOSchema = BaseCreateNodeResourceBOSchema.extend({
  resourceType: z.literal(NodeResourceTypeSchema.enum.AI),
  bo: AINodeAgentSchema.optional(),
});

export type AINodeCreateDTO = z.infer<typeof AIAgentNodeCreateDTOSchema>;

export const AINodeUpdateDTOSchema = NodeUpdateBOSchema.extend({
  resourceType: z.literal(NodeResourceTypeSchema.enum.AI),
  // name: iStringSchema,
  bo: AINodeAgentSchema.optional(),
});

export type AINodeUpdateDTO = z.infer<typeof AINodeUpdateDTOSchema>;

export const AIWizardDTOSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('AI_COPILOT'),
    spaceId: z.string().optional(),
    nodeId: z.string().optional(),
  }),
  z.object({
    type: z.literal('AI_BUILDER'),
    spaceId: z.string().optional(),
  }),
  z.object({
    type: z.literal('AI_NODE'),
    nodeId: z.string().optional(),
    spaceId: z.string().optional(),
  }),
  z.object({
    type: z.literal('AI_SUPERVISOR'),
    spaceId: z.string().optional(),
  }),
]);

export type AIWizardDTO = z.infer<typeof AIWizardDTOSchema>;

export const ListWizardDTOSchema = AIWizardDTOSchema.and(PaginationSchema);

export type ListWizardDTO = z.infer<typeof ListWizardDTOSchema>;
