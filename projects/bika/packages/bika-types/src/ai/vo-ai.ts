import { z } from 'zod';
import { AIMessageBOSchema, AIMessagePartToolInvocationSchema } from './bo-ai-message';
import { AIIntentParamsSchema } from './bo-intent-params';
import { AIIntentUIResolveDTOSchema } from './dto';
import { AIChatOptionSchema } from './vo-ai-sdk';
import { iStringSchema } from '../i18n/bo';
import { BasePaginationInfoSchema } from '../shared/pagination';
import { SkillsetSelectDTOSchema } from '../skill/dto';
import { UserVOSchema } from '../user/vo-user';
/**
 * Intent Resolution Status
 */
export const IntentResolutionStatuses = [
  // No attempt to resolve yet, no communication between parties
  // Sometimes the client receives this message, then automatically initiates chat
  // In certain situations, it feels natural, AI might send several messages showing thought process
  'NOT_STARTED',

  // Dialog has started! One party has initiated conversation
  // Sometimes the bot starts, sometimes the user starts, depends on the situation
  'DIALOG',

  // Needs further clarification from user
  // Usually when parameters are not completely filled
  'NEEDS_DISAMBIGUATION',

  // Needs user confirmation
  // Confirm what? Confirm if the parameters are correct
  // Usually returns UI operation
  // Typically when parameters are just filled and final confirmation is needed
  'NEEDS_CONFIRMATION',

  // Intent is clear, proceed to "complete" (directly enter next intent resolver or execute AI)
  'SUCCESS',
] as const;

export const IntentResolutionStatusSchema = z.enum(IntentResolutionStatuses);

export type IntentResolutionStatus = z.infer<typeof IntentResolutionStatusSchema>;

// Currently AIMessageVO = AIMessageBO
export const AIMessageVOSchema = AIMessageBOSchema;

// .extend({
// The message's creator is different from the wizard's creator
// creator: UserVOSchema.optional(),
// });

export type AIMessageVO = z.infer<typeof AIMessageVOSchema>;

export const AIWizardVOSchema = z.object({
  id: z.string(),
  title: iStringSchema,
  resolutionStatus: IntentResolutionStatusSchema,
  messages: z.array(AIMessageVOSchema),
  skillsets: z.array(SkillsetSelectDTOSchema).optional(),
  intent: AIIntentParamsSchema,
  options: z.array(AIChatOptionSchema).optional(),
});
/**
 * Dialog VO
 */
export type AIWizardVO = z.infer<typeof AIWizardVOSchema>;

export const AIWizardSimpleVOSchema = AIWizardVOSchema.pick({
  id: true,
  title: true,
  resolutionStatus: true,
}).extend({
  description: z.string().optional(),
  createdAt: z.string(),
  creator: UserVOSchema.optional(),
});
export type AIWizardSimpleVO = z.infer<typeof AIWizardSimpleVOSchema>;

export const AIWizardPaginationVOSchema = BasePaginationInfoSchema.extend({
  data: z.array(AIWizardSimpleVOSchema),
});
export type AIWizardPaginationVO = z.infer<typeof AIWizardPaginationVOSchema>;

export const AIResolveType = z.enum(['UI', 'MESSAGE', 'TOOL']);
export const AIResolveUIVOSchema = z.object({
  type: z.literal(AIResolveType.enum.UI),
  uiResolve: AIIntentUIResolveDTOSchema,
  message: z.string().optional(),
});
export type AIResolveUIVO = z.infer<typeof AIResolveUIVOSchema>;
export const AIResolveMessageVOSchema = z.object({
  type: z.literal(AIResolveType.enum.MESSAGE),
  message: z.string(),
  // user option
  option: z.string().optional(),
  attachments: z
    .array(
      z.object({
        name: z.string().optional(),
        contentType: z.string().optional(),
        url: z.string(),
      }),
    )
    .optional(),
});
export type AIResolveMessageVO = z.infer<typeof AIResolveMessageVOSchema>;

export const AIResolveToolDTOSchema = z.object({
  type: z.literal(AIResolveType.enum.TOOL),
  toolInvocation: AIMessagePartToolInvocationSchema,
  // 这是前端tool执行过程的message,直接将整个记录传回去，将最后一条message更新，根据messageId
  // message: AIMessageBOSchema, // z.any(), // 对应 AI SDK 的 Core Message (不是 AI SDK 的 UIMessage 和 Message！！！ https://ai-sdk.dev/docs/reference/ai-sdk-core/core-message)
  // message: z.string(),
  // uiMessage: AIMessageBOSchema, // z.any(), // 对应 AI SDK 的 Core Message (不是 AI SDK 的 UIMessage 和 Message！！！ https://ai-sdk.dev/docs/reference/ai-sdk-core/core-message)
  option: z.string().optional(),
  // message: z.string(),
  // toolCallId: z.string(),
});
export type AIResolveToolDTO = z.infer<typeof AIResolveMessageVOSchema>;

export const AIResolveVOSchema = z.discriminatedUnion('type', [
  AIResolveUIVOSchema,
  AIResolveMessageVOSchema,
  AIResolveToolDTOSchema,
]);
export type AIResolveVO = z.infer<typeof AIResolveVOSchema>;

// Client response: AIResolveVO -> AIResolveUIVO -> AIIntentUIResolveAIO -> XXXAIIntentUIResolveAIO
//                            -> AIResolveMessageVO
