import { z } from 'zod';
import { ChoicesAIIntentUIResolveDTOSchema } from './intent-ui/dto-ai-intent-ui-choices';
import { ConfirmAIIntentUIResolveDTOSchema } from './intent-ui/dto-ai-intent-ui-confirm';
import { FlowAIIntentUIResolveDTOSchema } from './intent-ui/dto-ai-intent-ui-flow-resolve';
import { InputAIIntentUIResolveDTOSchema } from './intent-ui/dto-ai-intent-ui-input';
import { LauncherCommandAIIntentUIResolveDTOSchema } from './intent-ui/dto-ai-intent-ui-launcher-commands';
import { PromptAIIntentUIResolveDTOSchema } from './intent-ui/dto-ai-intent-ui-prompt';
import { BillingAIIntentUIResolveDTOSchema } from './intent-ui/onboarding/vo-ai-intent-ui-billing';
import { GuideAIIntentUIResolveDTOSchema } from './intent-ui/onboarding/vo-ai-intent-ui-guide';
import { AuthAIIntentUIResolveDTOSchema } from './intent-ui/onboarding/vo-ai-intent-ui-onboarding';
import { QuestionaireAIIntentUIResolveDTOSchema } from './intent-ui/onboarding/vo-ai-intent-ui-questionaire';

export * from './intent-ui/dto-ai-intent-ui-confirm';
export * from './intent-ui/dto-ai-intent-ui-choices';
export * from './intent-ui/dto-ai-intent-ui-input';
export * from './intent-ui/dto-ai-intent-ui-prompt';
export * from './intent-ui/dto-ai-intent-ui-flow-resolve';
export * from './intent-ui/dto-ai-intent-ui-launcher-commands';

export const AIIntentUIResolveDTOSchema = z.discriminatedUnion('type', [
  FlowAIIntentUIResolveDTOSchema,
  ConfirmAIIntentUIResolveDTOSchema,
  ChoicesAIIntentUIResolveDTOSchema,
  PromptAIIntentUIResolveDTOSchema,
  InputAIIntentUIResolveDTOSchema,
  AuthAIIntentUIResolveDTOSchema,
  GuideAIIntentUIResolveDTOSchema,
  BillingAIIntentUIResolveDTOSchema,
  QuestionaireAIIntentUIResolveDTOSchema,
  LauncherCommandAIIntentUIResolveDTOSchema,
]);

/**
 * Handle UI control clicks, sent from client to server
 */
export type AIIntentUIResolveDTO = z.infer<typeof AIIntentUIResolveDTOSchema>;
