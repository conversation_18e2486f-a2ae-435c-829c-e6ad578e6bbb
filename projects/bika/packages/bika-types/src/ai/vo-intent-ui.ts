import { z } from 'zod';
import { BillingAIIntentUIVOSchema } from './intent-ui/onboarding/vo-ai-intent-ui-billing';
import { GuideAIIntentUIVOSchema } from './intent-ui/onboarding/vo-ai-intent-ui-guide';
import { AuthAIIntentUIVOSchema } from './intent-ui/onboarding/vo-ai-intent-ui-onboarding';
import { QuestionaireAIIntentUIVOSchema } from './intent-ui/onboarding/vo-ai-intent-ui-questionaire';
import { ChoicesAIIntentUIVOSchema } from './intent-ui/vo-ai-intent-ui-choices';
import { ConfirmAIIntentUIVOSchema } from './intent-ui/vo-ai-intent-ui-confirm';
import { FlowAIIntentUIVOSchema } from './intent-ui/vo-ai-intent-ui-flow';
import { InputAIIntentUIVOSchema } from './intent-ui/vo-ai-intent-ui-input';
import { LauncherCommandAIIntentUIVOSchema } from './intent-ui/vo-ai-intent-ui-launcher-commands';
import { PromptAIIntentUIVOSchema } from './intent-ui/vo-ai-intent-ui-prompt';
import { TemplatesAIIntentUIVOSchema } from './intent-ui/vo-intent-ui-template';

export * from './intent-ui/vo-ai-intent-ui-choices';
export * from './intent-ui/vo-ai-intent-ui-confirm';
export * from './intent-ui/vo-ai-intent-ui-input';
export * from './intent-ui/vo-ai-intent-ui-prompt';
export * from './intent-ui/vo-intent-ui-template';
export * from './intent-ui/vo-ai-intent-ui-flow';
export * from './intent-ui/vo-ai-intent-ui-launcher-commands';

export const AIIntentUIVOSchemas = z.discriminatedUnion('type', [
  ConfirmAIIntentUIVOSchema,
  ChoicesAIIntentUIVOSchema,
  PromptAIIntentUIVOSchema,
  InputAIIntentUIVOSchema,
  AuthAIIntentUIVOSchema,
  GuideAIIntentUIVOSchema,
  BillingAIIntentUIVOSchema,
  QuestionaireAIIntentUIVOSchema,
  TemplatesAIIntentUIVOSchema,
  FlowAIIntentUIVOSchema,
  LauncherCommandAIIntentUIVOSchema,
]);

export type AIIntentUIVO = z.infer<typeof AIIntentUIVOSchemas>;
