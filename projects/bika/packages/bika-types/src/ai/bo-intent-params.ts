import { z } from 'zod';
import { ChatIntentParamsBOSchema } from './intents/bo-ai-intent-chat';
import { SuperAssistantIntentParamsBOSchema } from './intents/bo-ai-intent-super-assistant';
import { AdminIntentParamsSchema } from './intents/bo-intent-params-admin';
import { AINodeIntentParamsSchema } from './intents/bo-intent-params-ai-node';
import { AIPageIntentParamsSchema } from './intents/bo-intent-params-ai-page';
import { BuildAppIntentParamsSchema } from './intents/bo-intent-params-build-app';
import { CopilotIntentParamsSchema } from './intents/bo-intent-params-copilot';
import {
  ImportVikaDataSourceIntentParamsBOSchema,
  CreateNodeResourceAIIntentParamSchema,
} from './intents/bo-intent-params-create-node';
import { CreateRecordAIIntentParamsSchema } from './intents/bo-intent-params-database';
import { CreateReminderIntentParamsSchema } from './intents/bo-intent-params-reminder';
import { AISearchIntentParamsSchema } from './intents/bo-intent-params-search';
import { SuperAgentIntentParamsSchema } from './intents/bo-intent-params-super-agent';
import { StepWizardIntentParamsAIOSchema } from './step-wizard/step-wizard-intent-params';

export * from './intents/bo-intent-params-admin';

export const AIIntentParamsSchema = z.discriminatedUnion('type', [
  AdminIntentParamsSchema,
  SuperAssistantIntentParamsBOSchema,
  ChatIntentParamsBOSchema,
  CreateReminderIntentParamsSchema,
  AISearchIntentParamsSchema,
  CreateRecordAIIntentParamsSchema,
  // OnboardingInitAIIntentParamsSchema,
  // OnboardingUIAIIntentParamsSchema,
  // OnboardingAuthAIIntentParamsSchema,
  // OnboardingTrialAIIntentParamsSchema,
  ImportVikaDataSourceIntentParamsBOSchema,
  CreateNodeResourceAIIntentParamSchema,
  // CreateSpaceAIOSchema,
  StepWizardIntentParamsAIOSchema,
  BuildAppIntentParamsSchema,
  CopilotIntentParamsSchema,
  AINodeIntentParamsSchema,
  AIPageIntentParamsSchema,
  SuperAgentIntentParamsSchema,
]);

/**
 * Intent fill in Object
 */
export type AIIntentParams = z.infer<typeof AIIntentParamsSchema>;
