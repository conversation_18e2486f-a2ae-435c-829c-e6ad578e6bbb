/* eslint-disable @typescript-eslint/no-use-before-define */
import { z } from 'zod';
import { ActionStateSchema, RandomActionStateSchema, RoundRobinActionStateSchema } from './bo-action-state';
// eslint-disable-next-line import/no-cycle
import {
  AIPromptReportInputSchema,
  AISummaryInputSchema,
  CallAgentInputSchema,
  RecordBodyInputSchema,
  DashboardInputSchema,
  DatabaseInputSchema,
  DatabaseViewInputSchema,
  IMWebhookInputSchema,
  LoopActionInputSchema,
  MarkdownReportInputSchema,
  MemberInputSchema,
  MissionBodyInputSchema,
  MissionInputSchema,
  PrevActionInputSchema,
  ScriptInputSchema,
  ServiceEmailInputSchema,
  SmtpInputSchema,
  SmtpIntegrationInputSchema,
  TemplateReportInputSchema,
  UpdateRecordsActionInputSchema,
  UserInputSchema,
  WebhookInputSchema,
  WidgetInputSchema,
  TelegramSendMessageInputSchema,
  XCreateTweetInputSchema,
  TwitterUploadMediaInputSchema,
  DelayInputSchema,
  type MissionBodyInputZodInput,
  type MissionBodyInputZodOutput,
  type MissionBodyInput,
  WecomWebhookActionInputSchema,
  DingtalkWebhookActionInputSchema,
  FeishuWebhookActionInputSchema,
  SlackWebhookActionInputSchema,
  OpenAICreateTextInputSchema,
  CreateDocumentInputSchema,
  CreateNodeResourceInputSchema,
  ReplaceFileActionInputSchema,
  FilterActionInputSchema,
  FindRecordsActionInputSchema,
  FormAppAIActionInputSchema,
  ToolSDKAIActionInputSchema,
} from './bo-automation-input';
import { iStringSchema } from '../i18n/bo';

export const ActionTypes = [
  'CREATE_MISSION',
  'FIND_MEMBERS',
  'FIND_RECORDS',
  'CREATE_RECORD',
  'UPDATE_RECORD',
  'OPENAI_GENERATE_TEXT',
  'DEEPSEEK',
  'SEND_REPORT',
  'SEND_EMAIL',
  'FIND_DASHBOARD',
  'FIND_WIDGET',
  'FIND_MISSIONS',
  'FORMAPP_AI',
  'TOOLSDK_AI',
  'AI_SUMMARY',
  'CALL_AGENT',
  'WECOM_WEBHOOK',
  'FEISHU_WEBHOOK',
  'DINGTALK_WEBHOOK',
  'SLACK_WEBHOOK',
  'TELEGRAM_SEND_MESSAGE',
  'X_CREATE_TWEET',
  'TWITTER_UPLOAD_MEDIA',
  'RUN_SCRIPT',
  'WEBHOOK',
  'CREATE_DOCUMENT',
  // Replace .xlsx, .docx, .pptx, batch generate attachments
  'REPLACE_FILE',
  'CREATE_NODE_RESOURCE',
  'FILTER',
  'CONDITION',
  'DELAY',
  'LOOP',
  'RANDOM',
  'ROUND_ROBIN',
  'DUMMY_ACTION',

  // 'MIDJOURNEY',
] as const;

export const ActionTypeSchema = z.enum(ActionTypes);
export type ActionType = z.infer<typeof ActionTypeSchema>;

export const BaseActionSchema = z.object({
  templateId: z.string().optional(),
  description: iStringSchema.optional(),
  actionType: ActionTypeSchema,

  state: ActionStateSchema.optional(),
  // add for editor
  id: z.string().optional(),
});
export type BaseAction = z.infer<typeof BaseActionSchema>;

export const CreateRecordActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.CREATE_RECORD),
  input: RecordBodyInputSchema,
});
export type CreateRecordAction = z.infer<typeof CreateRecordActionSchema>;

export const UpdateRecordActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.UPDATE_RECORD),
  input: UpdateRecordsActionInputSchema,
});
export type UpdateRecordAction = z.infer<typeof UpdateRecordActionSchema>;

export const FindDashboardActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.FIND_DASHBOARD),
  input: DashboardInputSchema,
});
export type FindDashboardAction = z.infer<typeof FindDashboardActionSchema>;

export const FindWidgetActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.FIND_WIDGET),
  input: WidgetInputSchema,
});
export type FindWidgetAction = z.infer<typeof FindWidgetActionSchema>;

// Find Members
export const FindMembersActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.FIND_MEMBERS),
  input: MemberInputSchema,
});
export type FindMembersAction = z.infer<typeof FindMembersActionSchema>;

export const FindMissionsActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.FIND_MISSIONS),
  input: MissionInputSchema,
});
export type FindMissionsAction = z.infer<typeof FindMissionsActionSchema>;

// Mission (Recursive)
const BaseCreateMissionActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.CREATE_MISSION),
});
export type BaseCreateMissionAction = z.infer<typeof BaseCreateMissionActionSchema> & {
  input: MissionBodyInput;
};
export type BaseCreateMissionActionZodInput = z.input<typeof BaseCreateMissionActionSchema> & {
  input: MissionBodyInputZodInput;
};
export type BaseCreateMissionActionZodOutput = z.output<typeof BaseCreateMissionActionSchema> & {
  input: MissionBodyInputZodOutput;
};
export const CreateMissionActionSchema: z.ZodType<
  BaseCreateMissionActionZodOutput,
  z.ZodTypeDef,
  BaseCreateMissionActionZodInput
> = BaseCreateMissionActionSchema.extend({
  input: z.lazy(() => MissionBodyInputSchema),
});
export type CreateMissionAction = z.infer<typeof CreateMissionActionSchema>;

export const AISummaryActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.AI_SUMMARY),
  input: AISummaryInputSchema,
});
export type AISummaryAction = z.infer<typeof AISummaryActionSchema>;

export const CallAgentActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.CALL_AGENT),
  input: CallAgentInputSchema,
});
export type CallAgentAction = z.infer<typeof CallAgentActionSchema>;

export const RunScriptActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.RUN_SCRIPT),
  input: ScriptInputSchema,
});
export type RunScriptAction = z.infer<typeof RunScriptActionSchema>;

export const WebhookActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.WEBHOOK),
  input: WebhookInputSchema,
});
export type WebhookAction = z.infer<typeof WebhookActionSchema>;

export const DingtalkWebhookActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.DINGTALK_WEBHOOK),
  input: IMWebhookInputSchema.or(DingtalkWebhookActionInputSchema),
});
export type DingtalkWebhookAction = z.infer<typeof DingtalkWebhookActionSchema>;

export const FeishuWebhookActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.FEISHU_WEBHOOK),
  input: IMWebhookInputSchema.or(FeishuWebhookActionInputSchema),
});
export type FeishuWebhookAction = z.infer<typeof FeishuWebhookActionSchema>;

export const SlackWebhookActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.SLACK_WEBHOOK),
  input: IMWebhookInputSchema.or(SlackWebhookActionInputSchema),
});
export type SlackWebhookAction = z.infer<typeof SlackWebhookActionSchema>;

export const WecomWebhookActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.WECOM_WEBHOOK),
  input: IMWebhookInputSchema.or(WecomWebhookActionInputSchema),
});
export type WecomWebhookAction = z.infer<typeof WecomWebhookActionSchema>;

export const IMWebhookActionSchema = z.discriminatedUnion('actionType', [
  WecomWebhookActionSchema,
  DingtalkWebhookActionSchema,
  SlackWebhookActionSchema,
  FeishuWebhookActionSchema,
]);
export type IMWebhookAction = z.infer<typeof IMWebhookActionSchema>;

export const TelegramSendMessageActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.TELEGRAM_SEND_MESSAGE),
  input: TelegramSendMessageInputSchema,
});
export type TelegramSendMessageAction = z.infer<typeof TelegramSendMessageActionSchema>;

export const XCreateTweetActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.X_CREATE_TWEET),
  input: XCreateTweetInputSchema,
});
export type XCreateTweetAction = z.infer<typeof XCreateTweetActionSchema>;

export const TwitterUploadMediaActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.TWITTER_UPLOAD_MEDIA),
  input: TwitterUploadMediaInputSchema,
});
export type TwitterUploadMediaAction = z.infer<typeof TwitterUploadMediaActionSchema>;

export const SendReportActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.SEND_REPORT),
  input: z.discriminatedUnion('type', [
    MarkdownReportInputSchema,
    AIPromptReportInputSchema,
    TemplateReportInputSchema,
  ]),
});
export type SendReportAction = z.infer<typeof SendReportActionSchema>;

export const SendEmailActionInput = z.union([ServiceEmailInputSchema, SmtpInputSchema, SmtpIntegrationInputSchema]);
export const SendEmailActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.SEND_EMAIL),
  input: SendEmailActionInput,
});
export type SendEmailAction = z.infer<typeof SendEmailActionSchema>;

// Delay Action
export const DelayActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.DELAY),
  input: DelayInputSchema,
});
export type DelayAction = z.infer<typeof DelayActionSchema>;

// Random Action
export const RandomActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.RANDOM),
  state: RandomActionStateSchema.optional(),
  input: z.union([PrevActionInputSchema, DatabaseInputSchema, DatabaseViewInputSchema]),
});
export type RandomAction = z.infer<typeof RandomActionSchema>;
// Round Robin Action
export const RoundRobinActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.ROUND_ROBIN),
  state: RoundRobinActionStateSchema.optional(),
  input: z.union([PrevActionInputSchema, DatabaseInputSchema, DatabaseViewInputSchema, UserInputSchema]),
}).default({
  actionType: ActionTypeSchema.enum.ROUND_ROBIN,
  input: {
    type: 'DATABASE',
  },
});
export type RoundRobinAction = z.infer<typeof RoundRobinActionSchema>;

// recursive https://zod.dev/?id=recursive-types
// Condition Action (Recursive)
const BaseConditionActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.CONDITION),
  input: z.union([PrevActionInputSchema, DatabaseInputSchema, UserInputSchema]),
});
type BaseConditionActionZodInput = z.input<typeof BaseConditionActionSchema> & {
  actions?: ActionZodInput[];
};
type BaseConditionActionZodOutput = z.output<typeof BaseConditionActionSchema> & {
  actions?: ActionZodOutput[];
};
export const ConditionActionSchema: z.ZodType<BaseConditionActionZodOutput, z.ZodTypeDef, BaseConditionActionZodInput> =
  BaseConditionActionSchema.extend({
    actions: z.lazy(() => ActionSchema.array().optional()),
  });
export type ConditionAction = z.infer<typeof ConditionActionSchema>;

const BaseLoopActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.LOOP),
  input: LoopActionInputSchema,
});
type BaseLoopActionZodInput = z.input<typeof BaseLoopActionSchema> & {
  actions?: ActionZodInput[];
};
type BaseLoopActionZodOutput = z.output<typeof BaseLoopActionSchema> & {
  actions?: ActionZodOutput[];
};
export const LoopActionSchema: z.ZodType<BaseLoopActionZodOutput, z.ZodTypeDef, BaseLoopActionZodInput> =
  BaseLoopActionSchema.extend({
    actions: z.lazy(() => ActionSchema.array().optional()),
  });
export type LoopAction = z.infer<typeof LoopActionSchema>;

// Find Records (Recursive)
const BaseFindRecordsActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.FIND_RECORDS),
  input: FindRecordsActionInputSchema,
});
type BaseFindRecordsActionInput = z.input<typeof BaseFindRecordsActionSchema> & {
  actions?: ActionZodInput[];
};
type BaseFindRecordsActionOutput = z.output<typeof BaseFindRecordsActionSchema> & {
  actions?: ActionZodOutput[];
};
export const FindRecordsActionSchema: z.ZodType<BaseFindRecordsActionOutput, z.ZodTypeDef, BaseFindRecordsActionInput> =
  BaseFindRecordsActionSchema.extend({
    actions: z.lazy(() => ActionSchema.array().optional()),
  });
export type FindRecordsAction = z.infer<typeof FindRecordsActionSchema>;

// Dummy Action, only for displaying the action name and description in the "Coming Soon" type template
export const DummyActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.DUMMY_ACTION),
  input: z.any().optional(),
});
export type DummyAction = z.infer<typeof DummyActionSchema>;

export const OpenAIGenerateTextActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.OPENAI_GENERATE_TEXT),
  input: OpenAICreateTextInputSchema,
});
export type OpenAIGenerateTextAction = z.infer<typeof OpenAIGenerateTextActionSchema>;

export const DeepSeekGenerateTextActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.DEEPSEEK),
  // DeepSeek shares OpenAI's Input
  input: OpenAICreateTextInputSchema,
});
export type DeepSeekGenerateTextAction = z.infer<typeof DeepSeekGenerateTextActionSchema>;

export const CreateDocumentActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.CREATE_DOCUMENT),
  input: CreateDocumentInputSchema,
});
export type CreateDocumentAction = z.infer<typeof CreateDocumentActionSchema>;

export const CreateNodeResourceActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.CREATE_NODE_RESOURCE),
  input: CreateNodeResourceInputSchema,
});
export type CreateNodeResourceAction = z.infer<typeof CreateNodeResourceActionSchema>;

export const ReplaceFileActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.REPLACE_FILE),
  input: ReplaceFileActionInputSchema,
});
export type ReplaceFileAction = z.infer<typeof ReplaceFileActionSchema>;

export const FilterActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.FILTER),
  input: FilterActionInputSchema,
});
export type FilterAction = z.infer<typeof FilterActionSchema>;

export const FormAppAIActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.FORMAPP_AI),
  input: FormAppAIActionInputSchema,
});
export type FormAppAIAction = z.infer<typeof FormAppAIActionSchema>;

export const ToolSDKAIActionSchema = BaseActionSchema.extend({
  actionType: z.literal(ActionTypeSchema.enum.TOOLSDK_AI),
  input: ToolSDKAIActionInputSchema,
});
export type ToolSDKAIAction = z.infer<typeof ToolSDKAIActionSchema>;

export const ActionSchema = z.union([
  FindMembersActionSchema,
  FindRecordsActionSchema, // Recursive
  CreateRecordActionSchema,
  UpdateRecordActionSchema,
  FindDashboardActionSchema,
  FindWidgetActionSchema,
  FindMissionsActionSchema,
  CreateMissionActionSchema, // Recursive
  RunScriptActionSchema,
  WebhookActionSchema,
  WecomWebhookActionSchema,
  FeishuWebhookActionSchema,
  DingtalkWebhookActionSchema,
  SlackWebhookActionSchema,
  TelegramSendMessageActionSchema,
  XCreateTweetActionSchema,
  TwitterUploadMediaActionSchema,
  AISummaryActionSchema,
  CallAgentActionSchema,
  ConditionActionSchema, // Recursive
  DelayActionSchema,
  LoopActionSchema, // Recursive
  RandomActionSchema,
  RoundRobinActionSchema, // Recursive
  SendReportActionSchema,
  SendEmailActionSchema,
  DummyActionSchema,
  OpenAIGenerateTextActionSchema,
  DeepSeekGenerateTextActionSchema,
  CreateDocumentActionSchema,
  CreateNodeResourceActionSchema,
  ReplaceFileActionSchema,
  FilterActionSchema,
  FormAppAIActionSchema,
  ToolSDKAIActionSchema,
]);
export type Action = z.infer<typeof ActionSchema>;
export type ActionZodInput = z.input<typeof ActionSchema>;
export type ActionZodOutput = z.output<typeof ActionSchema>;
