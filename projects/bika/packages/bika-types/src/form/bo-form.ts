import { z } from 'zod';
import { BaseNodeResourceBOSchema, FormNodeType } from '../node/base';
import { AvatarLogoSchema } from '../system/avatar';

const ViewMetadataType = z.literal('VIEW');

const FieldMetadataType = z.literal('FIELD');

export const FormMetadataTypeSchema = z.union([ViewMetadataType, FieldMetadataType]);

export type FormMetadataType = z.infer<typeof FormMetadataTypeSchema>;

export const FormFieldSchema = z.object({
  fieldId: z.string().optional(),
  fieldTemplateId: z.string().optional(),
});

export const FormMetadataViewSchema = z.object({
  type: ViewMetadataType,
  viewId: z.string().optional(),
  viewTemplateId: z.string().optional(),
  brandLogoEnabled: z.boolean().optional(),
  bannerEnabled: z.boolean().optional(),
});

export type FormMetadataViewType = z.infer<typeof FormMetadataViewSchema>;

export const FormMetadataFieldTypeSchema = z.object({
  type: FieldMetadataType,
  fields: z.array(FormFieldSchema),
  brandLogoEnabled: z.boolean().optional(),
  bannerEnabled: z.boolean().optional(),
});

export type FormMetadataFieldType = z.infer<typeof FormMetadataFieldTypeSchema>;

export const FormMetadataSchema = z.union([FormMetadataViewSchema, FormMetadataFieldTypeSchema]);

export type FormMetadata = z.infer<typeof FormMetadataSchema>;

export const FormTypes = ['DATABASE', 'AUTOMATION'] as const;
export const FormTypeSchema = z.enum(FormTypes);

const BaseFormBOSchema = BaseNodeResourceBOSchema.extend({
  resourceType: FormNodeType,
  cover: z.union([z.string(), AvatarLogoSchema]).optional(),
  brandLogo: z.union([z.string(), AvatarLogoSchema]).optional(),
  formType: FormTypeSchema,
});

export const DatabaseFormBOSchema = BaseFormBOSchema.extend({
  formType: z.literal(FormTypeSchema.enum.DATABASE),
  databaseId: z.string().optional(),
  databaseTemplateId: z.string().optional(),
  metadata: FormMetadataSchema.optional(),
});

export const AutomationFormBOSchema = BaseFormBOSchema.extend({
  formType: z.literal(FormTypeSchema.enum.AUTOMATION),
  automationId: z.string(),
});

export const FormBOSchema = z.discriminatedUnion('formType', [DatabaseFormBOSchema, AutomationFormBOSchema]);

export type FormBO = z.infer<typeof FormBOSchema>;

export type DatabaseFormBO = z.infer<typeof DatabaseFormBOSchema>;

export type AutomationFormBO = z.infer<typeof AutomationFormBOSchema>;
