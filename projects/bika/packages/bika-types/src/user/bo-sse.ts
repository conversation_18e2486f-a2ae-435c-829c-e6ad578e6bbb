import { z } from 'zod';
import { UserVOSchema } from './vo-user';
import { DatabaseFieldUnionSchema } from '../database/bo-field';
import { ViewSchema } from '../database/bo-view';
import { RecordRenderVOSchema } from '../database/vo';
import { SpaceUIModalSchema } from '../space/bo-ui-modals';
import { TalkDetailVOSchema } from '../space/vo-talk';

export const SSEEventNames = [
  'space-sidebar',
  'ai-message',
  'online-user',
  'snackbar',
  'node',
  'refresh-grid',
  // billing ui show up
  'billing',
  'record-comment',

  // grid message

  `field-created`,
  `field-updated`,
  `field-deleted`,

  'database-import-start',
  'database-import-progress',
  'database-import-complete',
  'database-import-failure',

  /**
   * @deprecated no need to use this event, use `rows-created` instead
   */
  `row-created`,
  `rows-created`,
  /**
   * @deprecated no need to use this event, use `rows-updated` instead
   */
  `row-updated`,
  `rows-updated`,
  `rows-deleted`,

  `batch-user-commands`,

  `view-created`,
  `view-updated`,
  `view-deleted`,

  'column-update-started',
  'column-update-ended',
  'talk',
] as const;

export const SSEEventNameSchema = z.enum(SSEEventNames);

const SpaceSideBarInfoTypeSchema = z.enum(['REDDOTS', 'ROOT_NODE', 'FOLDER']);

export const SSEEventSpaceSidebarRefresh = z.object({
  name: z.literal(SSEEventNameSchema.enum['space-sidebar']),
  spaceId: z.string(),
  nodeId: z.string().optional(), // get current node position
  refreshInfos: SpaceSideBarInfoTypeSchema.array(),
});
export type SpaceSidebarInfoType = z.infer<typeof SpaceSideBarInfoTypeSchema>;
/**
 * Marks the beginning of an AI message state.
 * The following messages will be in a specific AI message format until done
 */
export const SSEEventAIMessage = z.object({
  name: z.literal(SSEEventNameSchema.enum['ai-message']),
  // Should match AI WizardID
  aiWizardID: z.string(),
  // The string returned each time
  text: z.string().or(z.literal('[DONE]')),
});

/**
 * Notifies when a user comes online on this route
 */
export const SSEEventOnlineUser = z.object({
  name: z.literal(SSEEventNameSchema.enum['online-user']),
  userId: z.string(),
  route: z.string(),
  onlineSessionId: z.string(),
  user: UserVOSchema,
});

export const SSEEventSnackbar = z.object({
  name: z.literal(SSEEventNameSchema.enum.snackbar),
  text: z.string(),
  closeable: z.boolean().optional(),
  action: z
    .object({
      text: z.string(),
      showModal: SpaceUIModalSchema.optional(),
    })
    .optional(),
});

export const SSEEventNodeChange = z.object({
  name: z.literal(SSEEventNameSchema.enum.node),
  spaceId: z.string(),
  nodeId: z.string(),
});
// Call AG Grid's refresh database interface to dynamically update the database
export const SSEEventRefreshGrid = z.object({
  name: z.literal(SSEEventNameSchema.enum['refresh-grid']),
  spaceId: z.string(),
  nodeId: z.string(),
  viewId: z.string().optional(),
});

export const SSEEventRecordComment = z.object({
  name: z.literal(SSEEventNameSchema.enum['record-comment']),
  spaceId: z.string(),
  nodeId: z.string(),
  recordId: z.string(),
});

/**
 * When usage is insufficient, the server will push this event.
 * The frontend should display the payment UI upon receiving it
 */
export const SSEEventBilling = z.object({
  name: z.literal(SSEEventNameSchema.enum.billing),
});

/**
 * @deprecated use `rows-created` instead
 */
export const SSEEventDatabaseRowCreatedSchema = z.object({
  name: z.literal(SSEEventNameSchema.enum['row-created']),
  databaseId: z.string(),
  data: z.object({
    newValue: RecordRenderVOSchema,
  }),
  updateBy: z.string(),
});
export type SSEEventDatabaseRowCreated = z.infer<typeof SSEEventDatabaseRowCreatedSchema>;

export const SSEEventDatabaseRowsCreatedSchema = z.object({
  name: z.literal(SSEEventNameSchema.enum['rows-created']),
  databaseId: z.string(),
  data: RecordRenderVOSchema.array(),
  updateBy: z.string(),
});
export type SSEEventDatabaseRowsCreated = z.infer<typeof SSEEventDatabaseRowsCreatedSchema>;

/**
 * Record updated data
 */
export const SSEEventDatabaseRowUpdatedDataSchema = z.object({
  newValue: RecordRenderVOSchema,
  oldValue: RecordRenderVOSchema,
});
export type SSEEventDatabaseRowUpdatedData = z.infer<typeof SSEEventDatabaseRowUpdatedDataSchema>;

/**
 * Single row update event
 * @deprecated use `rows-updated` instead
 */
export const SSEEventDatabaseRowUpdatedSchema = z.object({
  name: z.literal(SSEEventNameSchema.enum['row-updated']),
  databaseId: z.string(),
  data: SSEEventDatabaseRowUpdatedDataSchema,
  updateBy: z.string(),
});

export type SSEEventDatabaseRowUpdated = z.infer<typeof SSEEventDatabaseRowUpdatedSchema>;

/**
 * multiple row update event
 */
export const SSEEventDatabaseRowsUpdatedSchema = z.object({
  name: z.literal(SSEEventNameSchema.enum['rows-updated']),
  databaseId: z.string(),
  dataList: SSEEventDatabaseRowUpdatedDataSchema.array(),
  updateBy: z.string(),
});

export type SSEEventDatabaseRowsUpdated = z.infer<typeof SSEEventDatabaseRowsUpdatedSchema>;

// TODO refacotring support more scenerio
// export const SSEEventBatchUserCommandsRowCreatedSchema = z.object({
//   name: z.literal(SSEEventNameSchema.enum['batch-user-commands']),
//   databaseId: z.string(),
//   dataList: z.array(SSEEventDatabaseRowCreatedSchema),
//   updateBy: z.string(),
// });

/**
 * delete multiple row event
 */
export const SSEEventDatabaseRowDeletedSchema = z.object({
  name: z.literal(SSEEventNameSchema.enum['rows-deleted']),
  databaseId: z.string(),
  dataList: z.array(
    z.object({
      recId: z.string(),
    }),
  ),
  updateBy: z.string(),
});
export type SSEEventDatabaseRowDeleted = z.infer<typeof SSEEventDatabaseRowDeletedSchema>;

export const SSEEventFieldUpdatedSchema = z.object({
  name: z.literal(SSEEventNameSchema.enum['field-updated']),
  databaseId: z.string(),
  data: z.object({
    // TODO add Field
    oldValue: DatabaseFieldUnionSchema.nullish(),
    newValue: DatabaseFieldUnionSchema,
  }),
  updateBy: z.string(),
});

export const SSEEventFieldDeletedSchema = z.object({
  name: z.literal(SSEEventNameSchema.enum['field-deleted']),
  databaseId: z.string(),
  data: z.object({
    id: z.string(),
  }),
  updateBy: z.string(),
});

export const SSEEventFieldCreatedSchema = z.object({
  name: z.literal(SSEEventNameSchema.enum['field-created']),
  databaseId: z.string(),
  data: z.object({
    newValue: DatabaseFieldUnionSchema,
  }),
  updateBy: z.string(),
});

export const SSEEventViewCreatedSchema = z.object({
  name: z.literal(SSEEventNameSchema.enum['view-created']),
  databaseId: z.string(),
  data: z.object({
    newValue: ViewSchema,
  }),
  updateBy: z.string(),
});

export const SSEEventViewUpdatedSchema = z.object({
  name: z.literal(SSEEventNameSchema.enum['view-updated']),
  databaseId: z.string(),
  data: z.object({
    newValue: ViewSchema,
    oldValue: ViewSchema,
  }),
  updateBy: z.string(),
});

export const SSEEventViewDeletedSchema = z.object({
  name: z.literal(SSEEventNameSchema.enum['view-deleted']),
  databaseId: z.string(),
  viewId: z.string().describe('viewId'),
  updateBy: z.string(),
});

/**
 * Database import start event
 */
export const SSEEventDatabaseImportStartSchema = z.object({
  name: z.literal(SSEEventNameSchema.enum['database-import-start']),
  databaseId: z.string(),
  jobId: z.string(),
  totalRecords: z.number(),
  processedRecord: z.number(),
});
export type SSEEventDatabaseImportStart = z.infer<typeof SSEEventDatabaseImportStartSchema>;

/**
 * Database import progress event
 */
export const SSEEventDatabaseImportProgressSchema = z.object({
  name: z.literal(SSEEventNameSchema.enum['database-import-progress']),
  databaseId: z.string(),
  jobId: z.string(),
  totalRecords: z.number(),
  processedRecord: z.number(),
});
export type SSEEventDatabaseImportProgress = z.infer<typeof SSEEventDatabaseImportProgressSchema>;

/**
 * Database import complete event
 */
export const SSEEventDatabaseImportCompleteSchema = z.object({
  name: z.literal(SSEEventNameSchema.enum['database-import-complete']),
  databaseId: z.string(),
  jobId: z.string(),
  totalRecords: z.number(),
  processedRecord: z.number(),
  operator: z.string(),
});
export type SSEEventDatabaseImportComplete = z.infer<typeof SSEEventDatabaseImportCompleteSchema>;

/**
 * Database import failure event
 */
export const SSEEventDatabaseImportFailureSchema = z.object({
  name: z.literal(SSEEventNameSchema.enum['database-import-failure']),
  databaseId: z.string(),
  jobId: z.string(),
  totalRecords: z.number(),
  processedRecord: z.number(),
  reason: z.string(),
});
export type SSEEventDatabaseImportFailure = z.infer<typeof SSEEventDatabaseImportFailureSchema>;

export const SSEEventTalkSchema = z.object({
  name: z.literal(SSEEventNameSchema.enum.talk),
  talk: TalkDetailVOSchema,
  memberId: z.string(),
});
export type SSEEventTalk = z.infer<typeof SSEEventTalkSchema>;

export const SSEEventSchema = z.discriminatedUnion('name', [
  SSEEventAIMessage,
  SSEEventNodeChange,
  SSEEventOnlineUser,
  SSEEventSpaceSidebarRefresh,
  SSEEventSnackbar,
  SSEEventRefreshGrid,
  SSEEventRecordComment,
  SSEEventBilling,

  SSEEventDatabaseImportStartSchema,
  SSEEventDatabaseImportProgressSchema,
  SSEEventDatabaseImportCompleteSchema,
  SSEEventDatabaseImportFailureSchema,

  SSEEventDatabaseRowCreatedSchema,
  SSEEventDatabaseRowsCreatedSchema,
  SSEEventDatabaseRowDeletedSchema,
  SSEEventDatabaseRowUpdatedSchema,
  SSEEventDatabaseRowsUpdatedSchema,

  SSEEventFieldCreatedSchema,
  SSEEventFieldDeletedSchema,
  SSEEventFieldUpdatedSchema,

  SSEEventViewCreatedSchema,
  SSEEventViewUpdatedSchema,
  SSEEventViewDeletedSchema,

  // SSEEventBatchRowCreatedSchema,

  // SSEEventBatchRowUpdatedSchema,
  SSEEventTalkSchema,
]);

export const SSEEventRowRelatedSchema = z.discriminatedUnion('name', [
  SSEEventDatabaseRowCreatedSchema,
  SSEEventDatabaseRowsCreatedSchema,
  SSEEventDatabaseRowUpdatedSchema,
  SSEEventDatabaseRowsUpdatedSchema,
  SSEEventDatabaseRowDeletedSchema,
]);

export type SSERowEvent = z.infer<typeof SSEEventRowRelatedSchema>;

export type SSEEvent = z.infer<typeof SSEEventSchema>;
export type ISSEEventOnlineUser = z.infer<typeof SSEEventOnlineUser>;

export type SSEEventName = SSEEvent['name'];
export type SSEEventRecords = Record<SSEEventName, SSEEvent>;

export const SsePOSchema = z.object({
  onlineSessionId: z.string(),
  userId: z.string(),
  expiresAt: z.string().date(),
  event: SSEEventSchema,
});

export type SsePO = z.infer<typeof SsePOSchema>;
