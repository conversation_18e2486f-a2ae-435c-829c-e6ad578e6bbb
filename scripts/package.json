{"name": "@bika/scripts", "description": "Internal utils scripts for Bika", "author": "", "dependencies": {"@bika/contents": "workspace:*", "@bika/domains": "workspace:*", "@bika/server-orm": "workspace:*", "@bika/types": "workspace:*", "@huggingface/hub": "^2.4.0", "@svgr/core": "^8.1.0", "@toolsdk.ai/registry": "1.0.89", "@trivago/prettier-plugin-sort-imports": "^5.2.1", "@types/autocannon": "^7.12.5", "@vikadata/vika": "^1.4.1", "autocannon": "^7.15.0", "axios": "^1.9.0", "html-to-image": "^1.11.11", "https-proxy-agent": "^7.0.5", "lodash": "^4.17.21", "mime-types": "^2.1.35", "pinyin-pro": "^3.19.3", "prettier": "^3.4.2", "puppeteer": "^22.12.1", "random-words": "^2.0.1", "rimraf": "^4.1.2", "wanakana": "^5.3.1"}, "keywords": [], "license": "ISC", "main": "index.ts", "private": true, "devDependencies": {"@inquirer/prompts": "^5.3.2", "@types/mime-types": "^2.1.4", "@types/pinyin": "^2.10.2", "@types/yargs": "^17.0.32", "bun-types": "^1.1.20", "typescript": "^5.7.2", "yargs": "^17.7.2"}, "version": "1.9.1-alpha.1"}