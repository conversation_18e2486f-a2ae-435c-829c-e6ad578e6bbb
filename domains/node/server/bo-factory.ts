import { Automation } from '@bika/types/automation/bo';
import { Dashboard } from '@bika/types/dashboard/bo';
import { Database } from '@bika/types/database/bo';
import { FormBO } from '@bika/types/form/bo';
import { Folder, MirrorBO, NodeResource } from '@bika/types/node/bo';
import { AbstractNodeBOProcessor, FolderBOProcessor } from './node-bo-processor';
import { AutomationBOProcessor } from '../../automation/server/automation-bo-processor';
import { DashboardBOProcessor } from '../../dashboard/server/dashboard-bo-processor';
import { DatabaseBOProcessor } from '../../database/server/database-bo-processor';
import { FormBOProcessor } from '../../form/server/form-bo-processor';
import { MirrorBOProcessor } from '../../mirror/server/mirror-bo-processor';
/**
 * Node Resource BO 加工厂
 */
export class NodeResourceBOFactory {
  static getProcessor(bo: NodeResource): AbstractNodeBOProcessor<NodeResource> {
    switch (bo.resourceType) {
      case 'FOLDER':
        return new FolderBOProcessor(bo as Folder);
      case 'DATABASE':
        return new DatabaseBOProcessor(bo as Database);
      case 'AUTOMATION':
        return new AutomationBOProcessor(bo as Automation);
      case 'DASHBOARD':
        return new DashboardBOProcessor(bo as Dashboard);
      case 'FORM':
        return new FormBOProcessor(bo as FormBO);
      case 'MIRROR':
        return new MirrorBOProcessor(bo as MirrorBO);
      default:
        throw new Error(`Not implemented: getProcessor. Resource type: ${bo.resourceType}`);
    }
  }
}
