import assert from 'assert';
import _ from 'lodash';
import { generateNanoID } from 'sharelib/nano-id';
import { sleep } from 'sharelib/sleep';
import { beforeEach, describe, expect, test } from 'vitest';
import { waitForMatchToBeMet, MockContext } from '@bika/domains/__tests__/mock';
import { AutomationSO } from '@bika/domains/automation/server/automation-so';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { db } from '@bika/server-orm';
import { WidgetBO } from '@bika/types/dashboard/bo';
import { Folder } from '@bika/types/node/bo';
import { LauncherSO } from '../../ai/server/launcher-so';
import { DashboardSO } from '../../dashboard/server/dashboard-so';
import { FolderSO } from '../server/folder-so';
/**
 * node crud test suite
 */

beforeEach(async () => {
  // Clear db data before each test run
  // await TestUtil.cleanDatabase();
});

describe('create resource', () => {
  test('test create folder node', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const folder = await (await NodeSO.init(rootFolder.id)).toResourceSO<FolderSO>();
    await folder.createChildSimple(user, { name: 'test create folder', resourceType: 'FOLDER' });
    const node = await rootFolder.getChildren();
    expect(node).toBeDefined();
  });

  test('test create folder node double', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    await rootFolder.createChildSimple(user, { name: 'test create folder 01', resourceType: 'FOLDER' });
    await rootFolder.createChildSimple(user, { name: 'test create folder 02', resourceType: 'FOLDER' });
    await rootFolder.reloadChildren();
    const node = await rootFolder.getChildren();
    expect(node.length).toEqual(2);
  });

  test('test create folder before template', async () => {
    const { user, rootFolder, space } = await MockContext.initUserContext();
    const templateFolder = await space.installTemplateById(user, '@vika/scrum-standup');
    await waitForMatchToBeMet(
      async () => {
        const node = await NodeSO.init(templateFolder.id);
        const numberState = node.state.find((state) => state.state === 'NUMBER');
        return numberState !== undefined && numberState.number === 4;
      },
      10000,
      1000,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
    const folder = await (await NodeSO.init(rootFolder.id)).toResourceSO<FolderSO>();
    await folder.createChildSimple(user, { name: 'test create folder 01', resourceType: 'FOLDER' });
    await folder.createChildSimple(user, { name: 'test create folder 02', resourceType: 'FOLDER' });
    await rootFolder.reloadChildren();
    const node = await rootFolder.getChildren();
    expect(node.length).toEqual(3);
  });

  test('test create database node', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const folder = await (await NodeSO.init(rootFolder.id)).toResourceSO<FolderSO>();
    const node = await folder.createChildSimple(user, {
      name: 'test create database',
      resourceType: 'DATABASE',
    });
    const database = await DatabaseSO.init(node.id);
    expect(database).toBeDefined();
    expect(database.viewModels.length).toBe(1);
  });

  test('test create automation node', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const folder = await (await NodeSO.init(rootFolder.id)).toResourceSO<FolderSO>();
    const node = await folder.createChildSimple(user, {
      name: 'test create automation',
      resourceType: 'AUTOMATION',
    });
    const automation = await AutomationSO.init(node.id);
    expect(automation).toBeDefined();
  });
});

describe('delete resource', () => {
  test('delete resource', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const folder = await (await NodeSO.init(rootFolder.id)).toResourceSO<FolderSO>();
    const node = await folder.createChildSimple(user, {
      name: 'test create folder',
      resourceType: 'FOLDER',
    });
    await node.delete(user);

    // 确定数据库已删
    const nodePO = await db.prisma.node.findUnique({ where: { id: node.id } });
    expect(nodePO).toBeNull();

    await rootFolder.reloadChildren();
    const children = await rootFolder.getChildren();
    expect(children.length).toBe(0);
  });

  test('delete installed template resource', async () => {
    const { user, rootFolder, space } = await MockContext.initUserContext();
    const folder = await space.installTemplateById(user, '@vika/scrum-standup');
    await waitForMatchToBeMet(
      async () => {
        const node = await NodeSO.init(folder.id);
        const numberState = node.state.find((state) => state.state === 'NUMBER');
        return numberState !== undefined && numberState.number === 4;
      },
      10000,
      1000,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
    const node = await NodeSO.init(folder.id);
    await node.delete(user);
    await rootFolder.reloadChildren();
    const children = await rootFolder.getChildren();
    expect(children.length).toBe(0);
  });

  test('delete folder in folder', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const folderNode = await rootFolder.createChildSimple(user, {
      name: 'parent',
      resourceType: 'FOLDER',
    });
    const folder = await folderNode.toResourceSO<FolderSO>();
    const childNode = await folder.createChildSimple(user, {
      name: 'child',
      resourceType: 'FOLDER',
    });
    const childFolder = await childNode.toResourceSO<FolderSO>();
    await childFolder.createChildSimple(user, { name: 'child database', resourceType: 'DATABASE' });
    const checkNumberState = async (id: string) => {
      const node = await NodeSO.init(id);
      const numberState = node.state.find((state) => state.state === 'NUMBER');
      return numberState !== undefined && numberState.number === 1;
    };
    await waitForMatchToBeMet(
      async () => {
        const folderCheck = await checkNumberState(folder.id);
        const childCheck = await checkNumberState(childNode.id);
        return folderCheck && childCheck;
      },
      10000,
      1000,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
    await folderNode.delete(user);

    const children = await rootFolder.getAllChildren();
    expect(children.length).toBe(0);
  });
});

describe('update resource', () => {
  test('update folder', async () => {
    const { user, space } = await MockContext.initUserContext();
    const folder = await space.installTemplateById(user, '@vika/scrum-standup');
    await waitForMatchToBeMet(
      async () => {
        const node = await NodeSO.init(folder.id);
        const numberState = node.state.find((state) => state.state === 'NUMBER');
        return numberState !== undefined && numberState.number === 4;
      },
      10000,
      1000,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
    const node = await NodeSO.init(folder.id);
    await node.update(user, { resourceType: 'FOLDER', name: 'test' });
    const updatedNode = await NodeSO.init(folder.id);
    expect(updatedNode.name).toEqual('test');
  });

  test('update node icon', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const _lastFolder = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'last' });
    const folder = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'test' });
    // 当前节点没有icon
    expect(folder.icon == null).toBe(true);
    // 修改icon
    const updatedNode = await folder.update(user, {
      resourceType: 'FOLDER',
      icon: { type: 'COLOR', color: 'BLUE' },
    });
    expect(updatedNode.icon).toEqual({ type: 'COLOR', color: 'BLUE' });
    expect(updatedNode.preNodeId).toBeNull();
  });

  /**
   * 文件夹子节点检查器
   */
  const childrenOrderCheck = async (userId: string, parentId: string, expectedOrder: string[]) => {
    const folder = await FolderSO.init(parentId);
    const { children } = await folder.toVO({ userId });
    expect(children?.length).toBe(expectedOrder.length);
    const childrenNames = children?.map((child) => child.name);
    expect(childrenNames).toStrictEqual(expectedOrder);
  };

  /**
   * 同级目录下：移动到首位
   */
  test('move node to first location', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const _lastFolder = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'last' });
    const middleFolder = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'middle' });
    const firstFolder = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'first' });

    await childrenOrderCheck(user.id, rootFolder.id, ['first', 'middle', 'last']);

    // 修改节点位置, middleFolder 移动到 firstFolder 之前
    const middle = await NodeSO.init(middleFolder.id);
    const updatedNode = await middle.move(user, {
      parentId: rootFolder.id,
      preNodeId: null,
    });
    expect(updatedNode.preNodeId).toBeNull();
    // 修改后, firstFolder 在 middleFolder 之后
    const first = await NodeSO.init(firstFolder.id);
    expect(first.preNodeId).toBe(middle.id);

    // 检查顺序
    await childrenOrderCheck(user.id, rootFolder.id, ['middle', 'first', 'last']);
  });

  /**
   * 同级目录下：移动到末尾
   */
  test('move node to last location', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const lastFolder = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'last' });
    const middleFolder = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'middle' });
    const firstFolder = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'first' });

    // 检查顺序
    await childrenOrderCheck(user.id, rootFolder.id, ['first', 'middle', 'last']);

    // 修改节点位置, middleFolder 移动到最后
    const middle = await NodeSO.init(middleFolder.id);
    const updatedNode = await middle.move(user, {
      parentId: rootFolder.id,
      preNodeId: lastFolder.id,
    });
    expect(updatedNode.preNodeId).toBe(lastFolder.id);
    // 修改后, lastFolder 在 middleFolder 之前
    const last = await NodeSO.init(lastFolder.id);
    expect(last.preNodeId).toBe(firstFolder.id);

    // 检查顺序
    await childrenOrderCheck(user.id, rootFolder.id, ['first', 'last', 'middle']);
  });

  /**
   * 同级目录下：移动到中间任意位置
   */
  test('move node to middle any location', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const _lastFolder = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'last' });
    const threeFolder = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'three' });
    const twoFolder = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'two' });
    const firstFolder = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'first' });

    // 检查顺序
    await childrenOrderCheck(user.id, rootFolder.id, ['first', 'two', 'three', 'last']);

    // 修改节点位置,  threeFolder 移动到 firstFolder 之后
    const three = await NodeSO.init(threeFolder.id);
    const updatedNode = await three.move(user, {
      parentId: rootFolder.id,
      preNodeId: firstFolder.id,
    });
    expect(updatedNode.preNodeId).toBe(firstFolder.id);
    // 修改后, twoFolder 在 threeFolder 之后
    const two = await NodeSO.init(twoFolder.id);
    expect(two.preNodeId).toBe(threeFolder.id);

    // 检查顺序
    await childrenOrderCheck(user.id, rootFolder.id, ['first', 'three', 'two', 'last']);
  });

  /**
   * 跨文件夹: 目标文件夹的首位
   */
  test('move node cross folder to first', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    // 创建两个文件夹
    const bFolderNode = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'b' });
    const bFolder = await bFolderNode.toResourceSO<FolderSO>();
    // bFolder 下创建几个节点
    const _lastOfbFolder = await bFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'b-last' });
    const _middleOfbFolder = await bFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'b-middle' });
    const _firstOfbFolder = await bFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'b-first' });

    // 检查 b folder 的顺序
    await childrenOrderCheck(user.id, bFolder.id, ['b-first', 'b-middle', 'b-last']);

    const aFolderNode = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'a' });
    const aFolder = await aFolderNode.toResourceSO<FolderSO>();
    // aFolder 下创建几个节点
    const _lastOfaFolder = await aFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'a-last' });
    const middleOfaFolder = await aFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'a-middle' });
    const _firstOfaFolder = await aFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'a-first' });

    // 检查 a folder 的顺序
    await childrenOrderCheck(user.id, aFolder.id, ['a-first', 'a-middle', 'a-last']);

    // a-middle 移动到 bFolder 的首位
    const aMiddle = await NodeSO.init(middleOfaFolder.id);
    const updatedNode = await aMiddle.move(user, {
      parentId: bFolder.id,
      preNodeId: null,
    });
    expect(updatedNode.preNodeId).toBeNull();

    // 检查 b folder的顺序
    await childrenOrderCheck(user.id, bFolder.id, ['a-middle', 'b-first', 'b-middle', 'b-last']);

    // 检查 a folder的顺序
    await childrenOrderCheck(user.id, aFolder.id, ['a-first', 'a-last']);
  });

  /**
   * 跨文件夹: 目标文件夹的末尾
   */
  test('move node cross folder to last', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    // 创建两个文件夹
    const bFolderNode = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'b' });
    const bFolder = await bFolderNode.toResourceSO<FolderSO>();
    // bFolder 下创建几个节点
    const lastOfbFolder = await bFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'b-last' });
    const _middleOfbFolder = await bFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'b-middle' });
    const _firstOfbFolder = await bFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'b-first' });

    // 检查 b folder 的顺序
    await childrenOrderCheck(user.id, bFolder.id, ['b-first', 'b-middle', 'b-last']);

    const aFolderNode = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'a' });
    const aFolder = await aFolderNode.toResourceSO<FolderSO>();
    // aFolder 下创建几个节点
    const _lastOfaFolder = await aFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'a-last' });
    const middleOfaFolder = await aFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'a-middle' });
    const _firstOfaFolder = await aFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'a-first' });

    // 检查 a folder 的顺序
    await childrenOrderCheck(user.id, aFolder.id, ['a-first', 'a-middle', 'a-last']);

    // a-middle 移动到 bFolder 的末尾
    const aMiddle = await NodeSO.init(middleOfaFolder.id);
    const updatedNode = await aMiddle.move(user, {
      parentId: bFolder.id,
      preNodeId: lastOfbFolder.id,
    });
    expect(updatedNode.preNodeId).toBe(lastOfbFolder.id);

    // 检查 b folder的顺序
    await childrenOrderCheck(user.id, bFolder.id, ['b-first', 'b-middle', 'b-last', 'a-middle']);

    // 检查 a folder的顺序
    await childrenOrderCheck(user.id, aFolder.id, ['a-first', 'a-last']);
  });

  /**
   * 跨文件夹: 目标文件夹的中间任意位置
   */
  test('move node cross folder to middle', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    // 创建两个文件夹
    const bFolderNode = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'b' });
    const bFolder = await bFolderNode.toResourceSO<FolderSO>();
    // bFolder 下创建几个节点
    const lastOfbFolder = await bFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'b-last' });
    const middleOfbFolder = await bFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'b-middle' });
    const firstOfbFolder = await bFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'b-first' });

    // 检查 b folder 的顺序
    await childrenOrderCheck(user.id, bFolder.id, ['b-first', 'b-middle', 'b-last']);

    const aFolderNode = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'a' });
    const aFolder = await aFolderNode.toResourceSO<FolderSO>();
    // aFolder 下创建几个节点
    const _lastOfaFolder = await aFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'a-last' });
    const middleOfaFolder = await aFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'a-middle' });
    const _firstOfaFolder = await aFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'a-first' });

    // 检查 a folder 的顺序
    await childrenOrderCheck(user.id, aFolder.id, ['a-first', 'a-middle', 'a-last']);

    // a-middle 移动到 bFolder 的中间
    const aMiddle = await NodeSO.init(middleOfaFolder.id);
    const updatedNode = await aMiddle.move(user, {
      parentId: bFolder.id,
      preNodeId: middleOfbFolder.id,
    });
    expect(updatedNode.preNodeId).toBe(middleOfbFolder.id);

    // 检查 b folder的顺序
    await childrenOrderCheck(user.id, bFolder.id, ['b-first', 'b-middle', 'a-middle', 'b-last']);

    // 检查 a folder的顺序
    await childrenOrderCheck(user.id, aFolder.id, ['a-first', 'a-last']);
  });

  /**
   * 不符合规范传递参数去移动节点
   */
  test('update node location with error preNodeId', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const _lastFolder = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'last' });
    const middleFolderNode = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'middle' });
    const firstFolderNode = await rootFolder.createChildSimple(user, { resourceType: 'FOLDER', name: 'first' });

    const middle = await NodeSO.init(middleFolderNode.id);

    // 不允许移动根节点
    await expect(
      rootFolder.toNodeSO().move(user, { preNodeId: firstFolderNode.id, parentId: rootFolder.id }),
    ).rejects.toThrowError();
    // 移动首位时没有parentId
    await expect(middle.move(user, { preNodeId: null, parentId: '' })).rejects.toThrowError();
    // 传递一个空字符串
    await expect(middle.move(user, { preNodeId: '', parentId: rootFolder.id })).rejects.toThrowError();
    // 传递一个空格
    await expect(middle.move(user, { preNodeId: ' ', parentId: rootFolder.id })).rejects.toThrowError();
    // 移动到自己节点下面
    await expect(
      middle.move(user, { preNodeId: middleFolderNode.id, parentId: middleFolderNode.id }),
    ).rejects.toThrowError();
    // 移动到不存在的节点下面
    await expect(middle.move(user, { preNodeId: 'not-exist', parentId: rootFolder.id })).rejects.toThrowError();
  });

  test('move private node to space -- database link another database', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const database = await rootFolder.createChildSimple(
      user,
      {
        resourceType: 'DATABASE',
        name: 'database',
      },
      { scope: 'PRIVATE' },
    );
    const database2 = await rootFolder.createChildSimple(
      user,
      { resourceType: 'DATABASE', name: 'database2' },
      { scope: 'PRIVATE' },
    );
    // add link field
    const databaseSO = await DatabaseSO.init(database.id);
    await databaseSO.createField(user, {
      name: 'link',
      type: 'ONE_WAY_LINK',
      property: {
        foreignDatabaseId: database2.id,
      },
    });
    // move database to space
    await expect(
      database.move(user, { scope: 'SPACE', parentId: rootFolder.id, preNodeId: null }),
    ).rejects.toThrowError();
  });

  test('move private node to space -- database linked themselves', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const database = await rootFolder.createChildSimple(
      user,
      {
        resourceType: 'DATABASE',
        name: 'database',
      },
      { scope: 'PRIVATE' },
    );
    // add link field
    const databaseSO = await DatabaseSO.init(database.id);
    await databaseSO.createField(user, {
      name: 'link',
      type: 'ONE_WAY_LINK',
      property: {
        foreignDatabaseId: database.id,
      },
    });
    // move database to space
    await expect(
      database.move(user, { scope: 'SPACE', parentId: rootFolder.id, preNodeId: null }),
    ).resolves.not.toThrow();
  });

  test('move private node to space -- automation linked database', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const database = await rootFolder.createChildSimple(
      user,
      {
        resourceType: 'DATABASE',
        name: 'database',
      },
      { scope: 'PRIVATE' },
    );
    const automationNode = await rootFolder.createChildSimple(
      user,
      { resourceType: 'AUTOMATION', name: 'automation' },
      { scope: 'PRIVATE' },
    );
    const automation = await AutomationSO.init(automationNode.id);
    await automation.addTrigger(user, {
      triggerType: 'RECORD_CREATED',
      input: {
        type: 'DATABASE',
        databaseId: database.id,
      },
    });
    await expect(
      automationNode.move(user, { scope: 'SPACE', parentId: rootFolder.id, preNodeId: null }),
    ).rejects.toThrowError();
  });

  test('move private node to space -- automation linked form', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const database = await rootFolder.createChildSimple(
      user,
      {
        resourceType: 'DATABASE',
        name: 'database',
      },
      { scope: 'PRIVATE' },
    );
    const form = await rootFolder.createChildSimple(
      user,
      {
        resourceType: 'FORM',
        name: 'form',
        formType: 'DATABASE',
        databaseId: database.id,
      },
      { scope: 'PRIVATE' },
    );
    const automationNode = await rootFolder.createChildSimple(
      user,
      { resourceType: 'AUTOMATION', name: 'automation' },
      { scope: 'PRIVATE' },
    );
    const automation = await AutomationSO.init(automationNode.id);
    await automation.addTrigger(user, {
      triggerType: 'FORM_SUBMITTED',
      input: {
        type: 'FORM',
        formId: form.id,
      },
    });
    await expect(
      automationNode.move(user, { scope: 'SPACE', parentId: rootFolder.id, preNodeId: null }),
    ).rejects.toThrowError();
  });

  test('move private node to space -- database under a folder', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    await rootFolder.createChildSimple(user, { resourceType: 'DATABASE', name: 'database1' });
    const folderNode = await rootFolder.createChildSimple(
      user,
      {
        resourceType: 'FOLDER',
        name: 'folder',
      },
      { scope: 'PRIVATE' },
    );
    const folder = await FolderSO.init(folderNode.id);
    await folder.createChildSimple(user, { resourceType: 'DATABASE', name: 'database1' });
    const database = await folder.createChildSimple(user, { resourceType: 'DATABASE', name: 'database2' });
    await database.move(user, {
      scope: 'SPACE',
      parentId: rootFolder.id,
      preNodeId: null,
    });
    const children = (await rootFolder.toVO({ scope: 'SPACE', userId: user.id })).children!;
    expect(children.length).toBe(2);
    expect(children[0].name).toBe('database2');
  });
});

describe('update database views sort', async () => {
  const { user, rootFolder } = await MockContext.initUserContext();
  const viewIds = [
    generateNanoID('viw1'),
    generateNanoID('viw2'),
    generateNanoID('viw3'),
    generateNanoID('viw4'),
    generateNanoID('viw5'),
  ];
  const node = await rootFolder.createChildSimple(user, {
    resourceType: 'DATABASE',
    name: 'database',
    views: viewIds.map((id) => ({ id, name: `${id}_name`, type: 'TABLE' })),
  });
  const database = await DatabaseSO.init(node.id);
  const getNewViewIds = async () => {
    await database.reloadViews();
    const views = await database.getViews();
    return views.map((view) => view.id);
  };

  test('not move', async () => {
    await node.update(user, {
      resourceType: 'DATABASE',
      views: [{ id: viewIds[0] }, { id: viewIds[1] }, { id: viewIds[2] }, { id: viewIds[3] }, { id: viewIds[4] }],
    });
    const newViewIds = await getNewViewIds();
    expect(newViewIds).toEqual([viewIds[0], viewIds[1], viewIds[2], viewIds[3], viewIds[4]]);
  });

  test('move the last view to first', async () => {
    // move view5 to first
    await node.update(user, {
      resourceType: 'DATABASE',
      views: [{ id: viewIds[4] }, { id: viewIds[0] }, { id: viewIds[1] }, { id: viewIds[2] }, { id: viewIds[3] }],
    });
    const newViewIds = await getNewViewIds();
    expect(newViewIds).toEqual([viewIds[4], viewIds[0], viewIds[1], viewIds[2], viewIds[3]]);
  });

  test('move the first view to last', async () => {
    // move view1 to last
    await node.update(user, {
      resourceType: 'DATABASE',
      views: [{ id: viewIds[1] }, { id: viewIds[2] }, { id: viewIds[3] }, { id: viewIds[4] }, { id: viewIds[0] }],
    });
    const newViewIds = await getNewViewIds();
    expect(newViewIds).toEqual([viewIds[1], viewIds[2], viewIds[3], viewIds[4], viewIds[0]]);
  });

  test('move the middle view to first', async () => {
    // move view3 to first
    await node.update(user, {
      resourceType: 'DATABASE',
      views: [{ id: viewIds[3] }, { id: viewIds[0] }, { id: viewIds[1] }, { id: viewIds[2] }, { id: viewIds[4] }],
    });
    const newViewIds = await getNewViewIds();
    expect(newViewIds).toEqual([viewIds[3], viewIds[0], viewIds[1], viewIds[2], viewIds[4]]);
  });

  test('move the middle view to last', async () => {
    // move view3 to last
    await node.update(user, {
      resourceType: 'DATABASE',
      views: [{ id: viewIds[0] }, { id: viewIds[1] }, { id: viewIds[2] }, { id: viewIds[4] }, { id: viewIds[3] }],
    });
    const newViewIds = await getNewViewIds();
    expect(newViewIds).toEqual([viewIds[0], viewIds[1], viewIds[2], viewIds[4], viewIds[3]]);
  });

  test('move the middle view to middle', async () => {
    // move view3 to middle
    await node.update(user, {
      resourceType: 'DATABASE',
      views: [{ id: viewIds[0] }, { id: viewIds[3] }, { id: viewIds[1] }, { id: viewIds[2] }, { id: viewIds[4] }],
    });
    const newViewIds = await getNewViewIds();
    expect(newViewIds).toEqual([viewIds[0], viewIds[3], viewIds[1], viewIds[2], viewIds[4]]);
  });
});

describe('update automation actions sort', async () => {
  const { user, rootFolder } = await MockContext.initUserContext();
  const actionIds = [
    generateNanoID('act1'),
    generateNanoID('act2'),
    generateNanoID('act3'),
    generateNanoID('act4'),
    generateNanoID('act5'),
  ];
  const node = await rootFolder.createChildSimple(user, {
    resourceType: 'AUTOMATION',
    name: 'automation',
    triggers: [
      {
        id: generateNanoID('trg1'),
        triggerType: 'SCHEDULER',
        input: {
          type: 'SCHEDULER',
          scheduler: {
            datetime: { type: 'OFFSET_DAYS', day: 1 },
          },
        },
      },
    ],
    actions: actionIds.map((id) => ({
      id,
      name: `${id}_name`,
      actionType: 'DELAY',
      input: { type: 'DELAY', value: 1, unit: 'SECOND' },
    })),
  });
  const automation = await AutomationSO.init(node.id);
  const getNewActionIds = async () => {
    const actions = await automation.getActions(false);
    return actions.map((action) => action.id);
  };

  test('not move', async () => {
    await node.update(user, {
      resourceType: 'AUTOMATION',
      actions: actionIds.map((id) => ({ id })),
    });
    const newActionIds = await getNewActionIds();
    expect(newActionIds).toEqual(actionIds);
  });

  test('move the last action to first', async () => {
    // move action5 to first
    await node.update(user, {
      resourceType: 'AUTOMATION',
      actions: [
        { id: actionIds[4] },
        { id: actionIds[0] },
        { id: actionIds[1] },
        { id: actionIds[2] },
        { id: actionIds[3] },
      ],
    });
    const newActionIds = await getNewActionIds();
    expect(newActionIds).toEqual([actionIds[4], actionIds[0], actionIds[1], actionIds[2], actionIds[3]]);
  });

  test('move the first action to last', async () => {
    // move action1 to last
    await node.update(user, {
      resourceType: 'AUTOMATION',
      actions: [
        { id: actionIds[0] },
        { id: actionIds[1] },
        { id: actionIds[2] },
        { id: actionIds[3] },
        { id: actionIds[4] },
      ],
    });
    const newActionIds = await getNewActionIds();
    expect(newActionIds).toEqual([actionIds[0], actionIds[1], actionIds[2], actionIds[3], actionIds[4]]);
  });

  test('move the middle action to first', async () => {
    // move action3 to first
    await node.update(user, {
      resourceType: 'AUTOMATION',
      actions: [
        { id: actionIds[3] },
        { id: actionIds[0] },
        { id: actionIds[1] },
        { id: actionIds[2] },
        { id: actionIds[4] },
      ],
    });
    const newActionIds = await getNewActionIds();
    expect(newActionIds).toEqual([actionIds[3], actionIds[0], actionIds[1], actionIds[2], actionIds[4]]);
  });

  test('move the middle action to last', async () => {
    // move action3 to last
    await node.update(user, {
      resourceType: 'AUTOMATION',
      actions: [
        { id: actionIds[0] },
        { id: actionIds[1] },
        { id: actionIds[2] },
        { id: actionIds[4] },
        { id: actionIds[3] },
      ],
    });
    const newActionIds = await getNewActionIds();
    expect(newActionIds).toEqual([actionIds[0], actionIds[1], actionIds[2], actionIds[4], actionIds[3]]);
  });

  test('move the middle action to middle', async () => {
    // move action3 to middle
    await node.update(user, {
      resourceType: 'AUTOMATION',
      actions: [
        { id: actionIds[0] },
        { id: actionIds[3] },
        { id: actionIds[1] },
        { id: actionIds[2] },
        { id: actionIds[4] },
      ],
    });
    const newActionIds = await getNewActionIds();
    expect(newActionIds).toEqual([actionIds[0], actionIds[3], actionIds[1], actionIds[2], actionIds[4]]);
  });
});

describe('update dashboard widgets layout', async () => {
  const { user, rootFolder } = await MockContext.initUserContext();
  const widgetBOList: WidgetBO[] = [
    {
      id: generateNanoID('wid1'),
      type: 'TEXT',
      name: 'text1',
      datasource: {
        type: 'CUSTOM',
        text: 'text1',
      },
    },
    {
      id: generateNanoID('wid2'),
      type: 'NUMBER',
      name: 'number1',
      datasource: {
        type: 'CUSTOM',
        number: 1,
      },
    },
  ];
  const node = await rootFolder.createChildSimple(user, {
    resourceType: 'DASHBOARD',
    name: 'dashboard',
    widgets: widgetBOList,
  });

  const getWidgets = async () => {
    const dashboard = await DashboardSO.init(node.id);
    const widgets = await dashboard.getWidgets();
    return widgets;
  };

  test('set layout', async () => {
    await node.update(user, {
      resourceType: 'DASHBOARD',
      widgets: widgetBOList.map((widget) => ({ id: widget.id!, layout: { x: 0, y: 0, w: 1, h: 1 } })),
    });
    const widgets = await getWidgets();
    expect(widgets.length).toBe(2);
    const widgetBOMap = _.keyBy(widgetBOList, 'id');

    for (const widget of widgets) {
      const widgetBO = widgetBOMap[widget.id];
      if (widgetBO) {
        expect(widget.toBO()).toStrictEqual({
          ...widgetBO,
          layout: { x: 0, y: 0, w: 1, h: 1 },
          templateId: null,
        });
      }
    }
  });

  test('batch update layout', async () => {
    await node.update(user, {
      resourceType: 'DASHBOARD',
      widgets: widgetBOList.map((widget, index) => {
        if (index === 0) {
          return { id: widget.id!, layout: { x: 0, y: 0, w: 1, h: 2 } };
        }
        return { id: widget.id!, layout: { x: 0, y: 0, w: 2, h: 3 } };
      }),
    });
    const widgets = await getWidgets();
    const widgetMap = _.keyBy(widgets, 'id');
    widgetBOList.forEach((widget, index) => {
      if (index === 0) {
        expect(widgetMap[widget.id!].toBO()).toStrictEqual({
          ...widget,
          layout: { x: 0, y: 0, w: 1, h: 2 },
          templateId: null,
        });
      } else {
        expect(widgetMap[widget.id!].toBO()).toStrictEqual({
          ...widget,
          layout: { x: 0, y: 0, w: 2, h: 3 },
          templateId: null,
        });
      }
    });
  });
});

describe('fetch resource', () => {
  test('test fetch database node', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    for (let i = 0; i < 10; i += 1) {
      await rootFolder.createChildSimple(user, {
        name: `test create database${i}`,
        resourceType: 'DATABASE',
      });
    }
    const databaseList = await space.findNodes({
      type: 'DATABASE',
    });
    expect(databaseList.length).toBe(10);
  });

  test('test fetch database node--with page', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const folderNode = await rootFolder.createChildSimple(user, {
      name: 'folder',
      resourceType: 'FOLDER',
    });
    const folder = await folderNode.toResourceSO<FolderSO>();
    for (let i = 0; i < 10; i += 1) {
      await folder.createChildSimple(user, {
        name: `test create database${i}`,
        resourceType: 'DATABASE',
      });
    }
    const databaseList = await space.findNodes({
      pagination: { pageNo: 1, pageSize: 1 },
      type: 'DATABASE',
      parentId: folder.id,
      orderBy: { createdAt: 'asc' },
    });
    expect(databaseList.length).toBe(1);
    expect(databaseList[0].name).toBe('test create database0');
  });

  test('test fetch database node--with name string', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    for (let i = 0; i < 10; i += 1) {
      await rootFolder.createChildSimple(user, {
        name: `test create database${i}`,
        resourceType: 'DATABASE',
      });
    }
    const databaseList = await space.findNodes({ name: 'test create database' });
    expect(databaseList.length).toBe(10);
  });
});

describe('node search index', () => {
  test('get node parents', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const folderId = await rootFolder.createChildren(user, [
      {
        resourceType: 'FOLDER',
        name: 'a',
        children: [
          {
            resourceType: 'FOLDER',
            name: 'b',
            children: [{ resourceType: 'DATABASE', name: 'c' }],
          },
        ],
      },
    ]);
    const folder = await FolderSO.init(folderId);
    const folderNode = folder.toNodeSO();
    const folderBO = await folder.toBORecursive();
    const databaseId = _.get(folderBO, ['children', 0, 'children', 0, 'id']);
    const databaseNode = await NodeSO.init(databaseId);
    const parents = await databaseNode.getParents();
    const parentIds = parents.map((parent) => parent.id);
    expect(parentIds.reverse().join('/')).toEqual(`${folderId}/${_.get(folderBO, ['children', 0, 'id'])}`);
    expect(await folderNode.getParents()).toEqual([]);

    const rootNode = rootFolder.toNodeSO();
    expect(await rootNode.getParents()).toEqual([]);
  });

  test('create children in folder index', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const folderBO = {
      resourceType: 'FOLDER',
      name: generateNanoID('fod'),
      children: [
        {
          resourceType: 'FOLDER',
          name: generateNanoID('fod'),
          children: [{ resourceType: 'DATABASE', name: generateNanoID('dst') }],
        },
      ],
    } as Folder;
    await rootFolder.createChildren(user, [folderBO]);
    await sleep(2000);
    // a b c should all in search index
    const databaseName = _.get(folderBO, ['children', 0, 'children', 0, 'name']);
    const result = await LauncherSO.search(user.id, space.id, 'SMART', databaseName);
    const resourceCommands = result[0].commands;
    // database 的score最高
    const databaseCommand = resourceCommands[0];
    assert(databaseCommand.type === 'NODE_MENU');
    expect(databaseCommand.node.path).to.equal(
      `${_.get(folderBO, ['name'])}/${_.get(folderBO, ['children', 0, 'name'])}`,
    );
  });
});
