import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { RecordSO } from '@bika/domains/database/server/record-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { DatabaseRecordModel, mongoose } from '@bika/server-orm';
import { CellValue, RecordData } from '@bika/types/database/bo';
import { RecordBulkUpdates } from '@bika/types/database/dto';
import { RecordRenderOpts } from '@bika/types/database/vo';
import { ApiGetRecordsReq } from '@bika/types/openapi/dto';
import { BikaRecordVO, GetRecordsResVO, ResponseVO, ResponseVOBuilder } from '@bika/types/openapi/vo';
import { AstFilter } from '../../../../../database/server/views/filter/ast-filter';
import { FilterSO } from '../../../../../database/server/views/filter-so';
import { SortSO } from '../../../../../database/server/views/sort-so';
import { VikaCreateRecordReq, VikaUpdateRecordReq, VikaCreateRecordRes, VikaUpdateRecordRes } from '../../types';
import { OpenAPIUtil } from '../util';

export class RecordController {
  /**
   * Get Records
   */
  static async getRecords(
    user: UserSO,
    databaseId: string,
    req: ApiGetRecordsReq,
  ): Promise<ResponseVO<GetRecordsResVO>> {
    const {
      viewId,
      pageNum,
      pageSize,
      recordIds,
      fields,
      filter,
      sort,
      maxRecords,
      cellFormat = 'json',
      fieldKey = 'name',
    } = req;

    // 初始化表
    const database = await OpenAPIUtil.checkAndGetDatabase(databaseId);

    // 转换字段标识
    const showFields = fields?.map((key) => database.getFieldIdByFieldKey(key));
    const sortOrders = sort?.map(({ field, order }) => ({
      field: database.getFieldIdByFieldKey(field),
      order,
    }));

    // Find view
    const view = viewId ? await database.getView(viewId) : null;

    const startRow = (pageNum - 1) * pageSize;
    let endRow = pageNum * pageSize;
    if (maxRecords) {
      endRow = Math.min(endRow, maxRecords);
    }

    let total = view ? await view.getRecordCount(user) : await database.getRecordsCount();
    if (maxRecords) {
      total = Math.min(maxRecords, total);
    }

    // Do get records
    let records: RecordSO[] = [];

    // TODO 下面的找时间合并代码到一起, 临时解决先
    if (view) {
      // 指定视图搜索
      view.setStartRow(startRow);
      view.setEndRow(endRow);
      view.setRecordIds(recordIds);
      for (const { field, order } of sortOrders ?? []) {
        view.addAdditionalSort(field, order === 'asc');
      }
      if (filter) {
        const viewFilter = new AstFilter(user, database).toViewFilter(filter);
        view.setAdditionalFilter(viewFilter);
      }
      records = await view.getRecords(user);
    } else {
      // 表内搜索
      let filterQuery: mongoose.FilterQuery<DatabaseRecordModel> = {};
      // 过滤查询
      if (filter) {
        // 转换里面的field name到id
        const viewFilter = new AstFilter(user, database).toViewFilter(filter);
        const additionalFilterQuery = await new FilterSO(database, viewFilter).buildQuery();
        filterQuery = {
          ...filterQuery,
          ...additionalFilterQuery,
        };
      }
      // 指定记录查询
      if (recordIds && recordIds.length > 0) {
        filterQuery = {
          ...filterQuery,
          id: {
            $in: recordIds,
          },
        };
      }
      // 排序
      const sorts: SortSO[] = [];
      for (const { field, order } of sortOrders ?? []) {
        const sortSO = database.buildSort(field, order === 'asc');
        if (sortSO) {
          sorts.push(sortSO);
        }
      }
      records = await database.getRecordsAsPage({
        skip: startRow,
        limit: endRow - startRow,
        filterQuery,
        sorts,
        idDesc: true,
      });
    }

    // Convert to VO
    const recordsVOs = await Promise.all(
      records.map(async (record) => {
        // Convert cells to fields
        const recordVO = await RecordController.toRecordVO(record, {
          locale: user.locale,
          timeZone: user.timeZone ?? undefined,
          // 指定过滤字段查询
          fieldIds: showFields,
          returnFieldName: fieldKey === 'name',
        });

        return {
          recordId: recordVO.recordId,
          fields: recordVO.fields,
          createdAt: record.model.createdAt.getTime(),
          updatedAt: record.model.updatedAt.getTime(),
        };
      }),
    );

    return ResponseVOBuilder.success({
      total,
      pageNum,
      pageSize: records.length,
      records: recordsVOs,
    });
  }

  /**
   * Create Records
   */
  static async createRecords(
    userId: string,
    databaseId: string,
    _viewId: string | null,
    createRecord: VikaCreateRecordReq,
  ): Promise<ResponseVO<VikaCreateRecordRes>> {
    const db = await OpenAPIUtil.checkAndGetDatabase(databaseId);

    const { records, fieldKey } = createRecord;
    const cells: RecordData[] = records.map((record) => {
      const cellEntries: [string, CellValue | undefined][] = Object.entries(record.fields).map(([fk, value]) => {
        const fieldId = db.getFieldIdByFieldKey(fk);
        return [fieldId, value];
      });
      return Object.fromEntries(cellEntries);
    });

    const user = await UserSO.init(userId);
    const member = await user.getMember(db.spaceId);

    // Do create records
    const createdRecords = await db.createRecords(user, member, cells);

    // Convert to VO
    const recordVOs = await Promise.all(
      createdRecords.map((record) =>
        RecordController.toRecordVO(record, {
          locale: user.locale,
          timeZone: user.timeZone ?? undefined,
          returnFieldName: fieldKey === 'name',
        }),
      ),
    );

    return ResponseVOBuilder.success({
      records: recordVOs,
    });
  }

  /**
   * Update Records
   */
  static async updateRecords(
    userId: string,
    databaseId: string,
    _viewId: string | null,
    updateRecord: VikaUpdateRecordReq,
  ): Promise<ResponseVO<VikaUpdateRecordRes>> {
    const db = await OpenAPIUtil.checkAndGetDatabase(databaseId);

    const user = await UserSO.init(userId);

    const { records: newRecords, fieldKey } = updateRecord;

    // 构造更新记录参数
    const recordBulkUpdates: RecordBulkUpdates = newRecords.map(({ recordId, fields }) => {
      const cellEntries: [string, CellValue | undefined][] = Object.entries(fields).map(([fk, value]) => {
        const fieldId = db.getFieldIdByFieldKey(fk);
        return [fieldId, value];
      });
      return { recordId, cells: Object.fromEntries(cellEntries) };
    });
    // 执行更新操作
    const updatedRecords = await db.updateRecords(user, recordBulkUpdates);

    // Convert to VO
    const recordVOs = await Promise.all(
      updatedRecords.map(async (record) => {
        const recordVO = await RecordController.toRecordVO(record, {
          locale: user.locale,
          timeZone: user.timeZone ?? undefined,
          returnFieldName: fieldKey === 'name',
        });

        return {
          ...recordVO,
          fields: recordVO.fields,
        };
      }),
    );

    return ResponseVOBuilder.success({
      records: recordVOs,
    });
  }

  /**
   * Delete Records
   */
  static async deleteRecords(userId: string, databaseId: string, recordIds: string[]): Promise<ResponseVO<null>> {
    const db = await OpenAPIUtil.checkAndGetDatabase(databaseId);

    const user = await UserSO.init(userId);
    const member = await user.getMember(db.spaceId);
    const _space = await member.getSpace();

    const database = await DatabaseSO.init(databaseId);
    await database.deleteRecords(user, recordIds);

    return ResponseVOBuilder.success(null);
  }

  /**
   * 这个将成为 vika openapi 唯一处理的适配返回记录单元格方法,其他地方请勿用
   */
  private static async toRecordVO(recordSO: RecordSO, opts?: RecordRenderOpts): Promise<BikaRecordVO> {
    // TODO: toVO()没有实时的数据加载, 要使用toRenderVO()才行
    const record = recordSO.toVO(opts);
    const recordFields: RecordData = {};
    for (const [fieldId, cell] of Object.entries(record.cells)) {
      recordFields[fieldId] = cell.value;
    }
    return {
      recordId: recordSO.id,
      fields: recordFields,
    };
  }
}
