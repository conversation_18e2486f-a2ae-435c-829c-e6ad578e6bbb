import assert from 'assert';
import { createRoute, type OpenAP<PERSON>H<PERSON> } from '@hono/zod-openapi';
import { z } from 'zod';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { NodeController } from '@bika/domains/node/apis';
import { DatabaseVOSchema, FieldVOSchema, ViewVOSchema } from '@bika/types/database/vo';
import { createResponseVOSchema, ResponseVOBuilder } from '@bika/types/openapi/vo';
import type { ContextVariable } from '../../types';
import { SpaceParamSchema } from '../../types';

/**
 * Get Database Resource
 */
const getDatabaseResourceRoute = createRoute({
  tags: ['Databases'],
  description: 'Get Database',
  method: 'get',
  path: '/v1/spaces/:spaceId/resources/databases/:databaseId',
  request: {
    params: SpaceParamSchema.extend({
      databaseId: z.string().openapi({ param: { name: 'databaseId', in: 'path', required: true }, example: 'dat***' }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(DatabaseVOSchema),
        },
      },
      description: 'Create Database Response',
    },
  },
});

const getDatabaseFieldsResourceRoute = createRoute({
  tags: ['Databases'],
  description: 'Get Database Fields',
  method: 'get',
  path: '/v1/spaces/:spaceId/resources/databases/:databaseId/fields',
  request: {
    params: SpaceParamSchema.extend({
      databaseId: z.string().openapi({ param: { name: 'databaseId', in: 'path', required: true }, example: 'dat***' }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(FieldVOSchema.array()).openapi({
            description: 'Array of field',
          }),
        },
      },
      description: 'Database Field Response',
    },
  },
});

const getDatabaseViewsResourceRoute = createRoute({
  tags: ['Databases'],
  description: 'Get Database Views',
  method: 'get',
  path: '/v1/spaces/:spaceId/resources/databases/:databaseId/views',
  request: {
    params: SpaceParamSchema.extend({
      databaseId: z.string().openapi({ param: { name: 'databaseId', in: 'path', required: true }, example: 'dat***' }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(z.array(ViewVOSchema)),
        },
      },
      description: 'Database View VO Response',
    },
  },
});

export function initResourceDatabaseRoutes(app: OpenAPIHono<{ Variables: ContextVariable }>) {
  /**
   * Get Database
   */
  app.openapi(getDatabaseResourceRoute, async (c) => {
    const reqCtx = c.get('context');

    const {
      // spaceId,
      databaseId,
    } = c.req.param();
    // const body = c.req.valid('json');

    const node = await NodeController.retrieveNodeTree(reqCtx, {
      id: databaseId,
    });
    assert(node.permission?.abilities.readNode === true, 'no permission to read node');

    const database = await DatabaseSO.init(databaseId);
    // TODO: 通过node确认user有权限

    const databaseVO = await database.toVO();
    const response = ResponseVOBuilder.success(databaseVO);
    return c.json(response);
  });

  app.openapi(getDatabaseFieldsResourceRoute, async (c) => {
    const reqCtx = c.get('context');
    const {
      // spaceId,
      databaseId,
    } = c.req.param();
    // const body = c.req.valid('json');

    const node = await NodeController.retrieveNodeTree(reqCtx, {
      id: databaseId,
    });
    assert(node.permission?.abilities.readNode === true, 'no permission to read node');

    const database = await DatabaseSO.init(databaseId);

    const fields = database.getFields();
    const fieldsVOs = fields.map((field) => field.toVO());
    const response = ResponseVOBuilder.success(fieldsVOs);
    return c.json(response);
  });

  app.openapi(getDatabaseViewsResourceRoute, async (c) => {
    const reqCtx = c.get('context');

    const {
      // spaceId,
      databaseId,
    } = c.req.param();
    // const body = c.req.valid('json');

    const node = await NodeController.retrieveNodeTree(reqCtx, {
      id: databaseId,
    });
    assert(node.permission?.abilities.readNode === true, 'no permission to read node');

    const database = await DatabaseSO.init(databaseId);

    const views = await database.getViews();
    const viewsVO = views.map((viw) => viw.toVO());
    const response = ResponseVOBuilder.success(viewsVO);
    return c.json(response);
  });
}
