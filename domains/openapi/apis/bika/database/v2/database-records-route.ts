import { createRoute, OpenAPIHono, z } from '@hono/zod-openapi';
import { HTTPException } from 'hono/http-exception';
import qs from 'qs';
import { NodeController } from '@bika/domains/node/apis';
import { SpaceSO } from '@bika/domains/space/server';
import { UserSO } from '@bika/domains/user/server';
import {
  ApiCreateRecordsReqV2Schema,
  ApiDeleteRecordReqV2Schema,
  ApiGetRecordReqV2Schema,
  ApiGetRecordsReqV2Schema,
  ApiUpdateRecordReqV2Schema,
  ApiUpdateRecordsReqV2Schema,
  RecordSortArraySchema,
} from '@bika/types/openapi/dto';
import {
  ApiRecordVOSchema,
  createResponseVOSchema,
  ApiGetRecordsResSchema,
  ResponseVOBuilder,
  ApiCreateRecordsResSchema,
  ApiUpdateRecordsResSchema,
  ApiDeleteRecordResSchema,
} from '@bika/types/openapi/vo';
import { Service } from './service';
import { ContextVariable, SpaceParamSchema } from '../../types';

const paramSchema = SpaceParamSchema.extend({
  databaseId: z.string().openapi({ param: { name: 'databaseId', in: 'path', required: true }, example: 'dat***' }),
});

/**
 * 路由 - 获取记录
 */
const listRecordsRoute = createRoute({
  tags: ['Databases v2'],
  operationId: 'List Records',
  description: `List Records`,
  method: 'get',
  path: '/v2/spaces/:spaceId/resources/databases/:databaseId/records',
  request: {
    params: paramSchema,
    query: ApiGetRecordsReqV2Schema,
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(ApiGetRecordsResSchema).openapi('GetRecordsResponse'),
        },
      },
      description: 'List Records Response',
    },
  },
});

/**
 * 获取单个记录
 */
const getRecordRoute = createRoute({
  tags: ['Databases v2'],
  operationId: 'Get Record',
  description: `Get Record`,
  method: 'get',
  path: '/v2/spaces/:spaceId/resources/databases/:databaseId/records/:recordId',
  request: {
    params: paramSchema.extend({
      recordId: z.string().openapi({ param: { name: 'recordId', in: 'path', required: true }, example: 'rec***' }),
    }),
    query: ApiGetRecordReqV2Schema.openapi('GetRecordRequest'),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(ApiRecordVOSchema).openapi('GetRecordResponse'),
        },
      },
      description: 'Get Record Response',
    },
  },
});

/**
 * 创建记录
 */
const createRecordsRoute = createRoute({
  tags: ['Databases v2'],
  operationId: 'Create Records',
  description: `Create Records`,
  method: 'post',
  path: '/v2/spaces/:spaceId/resources/databases/:databaseId/records',
  request: {
    params: paramSchema,
    body: {
      content: {
        'application/json': {
          schema: ApiCreateRecordsReqV2Schema.openapi('CreateRecordsRequest'),
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(ApiCreateRecordsResSchema).openapi('CreateRecordsResponse'),
        },
      },
      description: 'Create Records Response',
    },
  },
});

/**
 * 修改单个记录
 */
const updateRecordRoute = createRoute({
  tags: ['Databases v2'],
  operationId: 'Update Record',
  description: `Update Record`,
  method: 'put',
  path: '/v2/spaces/:spaceId/resources/databases/:databaseId/records/:recordId',
  request: {
    params: paramSchema.extend({
      recordId: z.string().openapi({ param: { name: 'recordId', in: 'path', required: true }, example: 'rec***' }),
    }),
    body: {
      content: {
        'application/json': {
          schema: ApiUpdateRecordReqV2Schema.openapi('UpdateRecordRequest'),
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(ApiRecordVOSchema).openapi('UpdateRecordResponse'),
        },
      },
      description: 'Update Record Response',
    },
  },
});

/**
 * 批量修改记录
 */
const updateRecordsRoute = createRoute({
  tags: ['Databases v2'],
  operationId: 'Update Multiple Records',
  description: `Updates up to 10 records`,
  method: 'put',
  path: '/v2/spaces/:spaceId/resources/databases/:databaseId/records',
  request: {
    params: paramSchema,
    body: {
      content: {
        'application/json': {
          schema: ApiUpdateRecordsReqV2Schema.openapi('UpdateRecordsRequest'),
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(ApiUpdateRecordsResSchema).openapi('UpdateRecordsResponse'),
        },
      },
      description: 'Update Records Response',
    },
  },
});

/**
 * 删除单个记录
 */
const deleteRecordRoute = createRoute({
  tags: ['Databases v2'],
  operationId: 'Delete Record',
  description: `Delete Record`,
  method: 'delete',
  path: '/v2/spaces/:spaceId/resources/databases/:databaseId/records/:recordId',
  request: {
    params: paramSchema.extend({
      recordId: z.string().openapi({ param: { name: 'recordId', in: 'path', required: true }, example: 'rec***' }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(ApiDeleteRecordResSchema).openapi('DeleteRecordResponse'),
        },
      },
      description: 'Delete Record Response',
    },
  },
});

/**
 * 批量删除记录
 */
const deleteRecordsRoute = createRoute({
  tags: ['Databases v2'],
  operationId: 'Delete Multiple Records',
  description: `Delete up to 10 recordIds`,
  method: 'delete',
  path: '/v2/spaces/:spaceId/resources/databases/:databaseId/records',
  request: {
    params: paramSchema,
    query: ApiDeleteRecordReqV2Schema.openapi('DeleteRecordsRequest'),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(ApiDeleteRecordResSchema.array()).openapi('DeleteRecordsResponse'),
        },
      },
      description: 'Delete Records Response',
    },
  },
});

/**
 * 批量删除记录
 */

export const initDatabaseRecordsRoutes = (app: OpenAPIHono<{ Variables: ContextVariable }>) => {
  // 获取记录
  app.openapi(listRecordsRoute, async (c) => {
    const userId = c.get('token').userId;
    const ctx = c.get('context');
    const { spaceId, databaseId } = c.req.param();
    const user = await UserSO.init(userId);
    const space = await SpaceSO.init(spaceId);
    // 检查用户是否可以访问空间
    await user.checkExistSpace(space.id);
    // 校验表格访问权限
    const node = await NodeController.retrieveNodeTree(ctx, {
      id: databaseId,
    });
    if (!node.permission?.abilities.readNode) {
      throw new HTTPException(403, { message: 'No permission to read node' });
    }
    const rawQuery = c.req.valid('query');
    const url = new URL(c.req.url);
    // 处理 sort 参数: ?sort[0][field]=foo&sort[0][order]=desc
    const queryString = url.search.slice(1);
    const parsed = qs.parse(queryString, { depth: 5 });
    // console.log('Parsed Query:', parsed.sort);
    if (parsed.sort) {
      rawQuery.sort = RecordSortArraySchema.parse(parsed.sort);
    }
    // 查找记录
    const service = await Service.init(databaseId);
    const res = await service.listRecords(user, rawQuery);
    const response = ResponseVOBuilder.success(res);
    return c.json(response);
  });
  // 获取单个记录
  app.openapi(getRecordRoute, async (c) => {
    const userId = c.get('token').userId;
    const ctx = c.get('context');
    const { spaceId, databaseId, recordId } = c.req.param();
    const user = await UserSO.init(userId);
    const space = await SpaceSO.init(spaceId);
    // 检查用户是否可以访问空间
    await user.checkExistSpace(space.id);
    // 校验表格访问权限
    const node = await NodeController.retrieveNodeTree(ctx, {
      id: databaseId,
    });
    if (!node.permission?.abilities.readNode) {
      throw new HTTPException(403, { message: 'No permission to read node' });
    }
    const rawQuery = c.req.valid('query');
    // 获取单个记录信息
    const service = await Service.init(databaseId);
    const record = await service.getRecord(user, recordId, rawQuery);
    const data = ResponseVOBuilder.success(record);
    return c.json(data);
  });
  // 创建记录(多个)
  app.openapi(createRecordsRoute, async (c) => {
    const userId = c.get('token').userId;
    const ctx = c.get('context');
    const { spaceId, databaseId } = c.req.param();
    const user = await UserSO.init(userId);
    const space = await SpaceSO.init(spaceId);
    // 检查用户是否可以访问空间
    await user.checkExistSpace(space.id);
    // 校验表格访问权限
    const node = await NodeController.retrieveNodeTree(ctx, {
      id: databaseId,
    });
    if (!node.permission?.abilities.createNode) {
      throw new HTTPException(403, { message: 'No permission to create node' });
    }
    const req = c.req.valid('json');
    // 创建记录
    const service = await Service.init(databaseId);
    const res = await service.createRecords(user, req);
    const data = ResponseVOBuilder.success(res);
    return c.json(data);
  });
  // 修改单个记录
  app.openapi(updateRecordRoute, async (c) => {
    const userId = c.get('token').userId;
    const ctx = c.get('context');
    const { spaceId, databaseId, recordId } = c.req.param();
    const user = await UserSO.init(userId);
    const space = await SpaceSO.init(spaceId);
    // 检查用户是否可以访问空间
    await user.checkExistSpace(space.id);
    // 校验表格访问权限
    const node = await NodeController.retrieveNodeTree(ctx, {
      id: databaseId,
    });
    if (!node.permission?.abilities.updateNode) {
      throw new HTTPException(403, { message: 'No permission to update node' });
    }
    const req = c.req.valid('json');
    // 更新记录
    const service = await Service.init(databaseId);
    const res = await service.updateRecord(user, recordId, req);
    const data = ResponseVOBuilder.success(res);
    return c.json(data);
  });
  // 批量修改记录
  app.openapi(updateRecordsRoute, async (c) => {
    const userId = c.get('token').userId;
    const ctx = c.get('context');
    const { spaceId, databaseId } = c.req.param();
    const user = await UserSO.init(userId);
    const space = await SpaceSO.init(spaceId);
    // 检查用户是否可以访问空间
    await user.checkExistSpace(space.id);
    // 校验表格访问权限
    const node = await NodeController.retrieveNodeTree(ctx, {
      id: databaseId,
    });
    if (!node.permission?.abilities.updateNode) {
      throw new HTTPException(403, { message: 'No permission to update node' });
    }
    const req = c.req.valid('json');
    // 批量更新记录
    const service = await Service.init(databaseId);
    const res = await service.updateRecords(user, req);
    const data = ResponseVOBuilder.success(res);
    return c.json(data);
  });
  // 删除单个记录
  app.openapi(deleteRecordRoute, async (c) => {
    const userId = c.get('token').userId;
    const ctx = c.get('context');
    const { spaceId, databaseId, recordId } = c.req.param();
    const user = await UserSO.init(userId);
    const space = await SpaceSO.init(spaceId);
    // 检查用户是否可以访问空间
    await user.checkExistSpace(space.id);
    // 校验表格访问权限
    const node = await NodeController.retrieveNodeTree(ctx, {
      id: databaseId,
    });
    if (!node.permission?.abilities.deleteNode) {
      throw new HTTPException(403, { message: 'No permission to delete node' });
    }
    // 删除记录
    const service = await Service.init(databaseId);
    const res = await service.deleteRecord(user, recordId);
    const data = ResponseVOBuilder.success(res);
    return c.json(data);
  });
  // 批量删除记录
  app.openapi(deleteRecordsRoute, async (c) => {
    const userId = c.get('token').userId;
    const ctx = c.get('context');
    const { spaceId, databaseId } = c.req.param();
    const { records: recordIds } = c.req.valid('query');
    const user = await UserSO.init(userId);
    const space = await SpaceSO.init(spaceId);
    // 检查用户是否可以访问空间
    await user.checkExistSpace(space.id);
    // 校验表格访问权限
    const node = await NodeController.retrieveNodeTree(ctx, {
      id: databaseId,
    });
    if (!node.permission?.abilities.deleteNode) {
      throw new HTTPException(403, { message: 'No permission to delete node' });
    }
    // 批量删除记录
    const service = await Service.init(databaseId);
    const res = await service.deleteRecords(user, recordIds);
    const data = ResponseVOBuilder.success(res);
    return c.json(data);
  });
};
