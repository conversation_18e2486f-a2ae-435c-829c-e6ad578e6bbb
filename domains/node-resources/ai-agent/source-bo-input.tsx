import type React from 'react';
import type { ILocaleContext } from '@bika/contents/i18n';
import { type AINodeSourceBO } from '@bika/types/ai/bo';
import { AINodeSourceType } from '@bika/types/ai/bo';
import type { INodeResourceApi } from '@bika/types/node/context';
import { IconButton } from '@bika/ui/button-component';
import DeleteOutlined from '@bika/ui/icons/components/delete_outlined';
import { NodeResourceSelect } from '@bika/ui/node/types-form/node-resource-select';
import { SelectInput } from '@bika/ui/shared/types-form/select-input';
import { StringInput } from '@bika/ui/shared/types-form/string-input';

export interface ToSchemaBOInputProps {
  // 可以声明多少个类型，可隐藏一些不要的
  value: AINodeSourceBO;
  onChange: (newVal?: AINodeSourceBO) => void;
  locale: ILocaleContext;
  api?: INodeResourceApi;
  actionId?: string;
  disabledNodes?: string[]; // 用于禁用某些节点
}

const typesOptions: { label: string; value: AINodeSourceType; disabled: boolean }[] = [
  {
    label: 'Node',
    value: 'NODE',
    disabled: false,
  },
  {
    label: 'Sitemap',
    value: 'SITEMAP',
    disabled: true,
  },
  {
    label: 'URL',
    value: 'URL',
    disabled: true,
  },
];

export function SourceBOInput(props: ToSchemaBOInputProps) {
  const { locale, api, value, actionId } = props;
  const { t } = locale;
  const variables = api?.automation.getAutomationGlobalVariables(actionId);

  return (
    <div className="border border-[--border-default] rounded-lg space-y-1 bg-[--bg-controls]">
      <div className="flex items-center justify-between px-4 pl-[8px]">
        <SelectInput<string>
          options={typesOptions}
          value={value.type}
          classes={{
            joySelect: {
              backgroundColor: 'transparent',
              '&:hover': {
                backgroundColor: 'var(--bg-controls-hover)',
              },
            },
          }}
          onChange={(newVal) => {
            if (!newVal) {
              return;
            }

            if (newVal === value.type) {
              // Type hasn't changed, do nothing
              return;
            }

            // Type has changed, create a new object with the correct structure
            let newValue: AINodeSourceBO;
            if (newVal === 'NODE') {
              newValue = { type: newVal, nodeId: '' };
            } else if (newVal === 'SITEMAP') {
              newValue = { type: newVal, url: '' };
            } else if (newVal === 'URL') {
              newValue = { type: newVal, url: '' };
            } else {
              // Should not happen with current options, but handle defensively
              return;
            }
            props.onChange(newValue);
          }}
          sx={{
            width: 'fit-content',
            bgcolor: 'transparent',
            marginLeft: '8px',
            '.MuiInputBase-input': {
              padding: '0px 8px',
            },
          }}
        />
        <IconButton
          onClick={() => {
            props.onChange();
          }}
          size="sm"
        >
          <DeleteOutlined />
        </IconButton>
      </div>

      <div className="px-4">
        {value.type === 'NODE' && (
          <div className="mb-6">
            <NodeResourceSelect
              hiddenNodes={props.disabledNodes}
              allowResourceType={['FOLDER', 'DATABASE', 'DOCUMENT', 'PAGE']}
              locale={props.locale}
              resourceId={value.nodeId}
              setResourceId={(id: string) => {
                props.onChange({ ...value, nodeId: id });
              }}
              hideLabel={true}
            />
          </div>
        )}
        {value.type === 'SITEMAP' && (
          <StringInput
            value={null}
            onChange={() => {}}
            placeholder={t.resource.type.ai_agent.settings_datasource_sitemap_placeholder}
          />
        )}
        {value.type === 'URL' && (
          <StringInput
            value={null}
            onChange={() => {}}
            placeholder={t.resource.type.ai_agent.settings_datasource_url_placeholder}
          />
        )}
      </div>
    </div>
  );
}
