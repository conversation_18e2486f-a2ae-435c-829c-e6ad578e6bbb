'use client';

// import { omit, pick } from 'lodash';
import type React from 'react';
import { useState } from 'react';
import type { ILocaleContext } from '@bika/contents/i18n';
import { AIModelsSelector } from '@bika/domains/ai/client/ai-models/ai-models-selector/ai-models-selector';
// import { AITextFieldBOInputInternal } from '@bika/domains/ai/client/ai-models/ai-text-field-bo-input';
import { AIPromptConfigInput } from '@bika/domains/ai/client/ai-models/ai-prompt-config-input';
import type { AiNodeBO } from '@bika/types/ai/bo';
import type { INodeResourceApi } from '@bika/types/node/context';
import { SkillsetsSelectBOInput } from '@bika/ui/ai/type-form/skillsets-select-bo-input';
import { Button } from '@bika/ui/button-component';
import { FormLabel } from '@bika/ui/form-components';
import AddOutlined from '@bika/ui/icons/components/add_outlined';
import { SkillsSelectModal } from './skills-select/ai-skills-select-modal';
import { SourceBOInput } from './source-bo-input';

interface IAIAgentBOInputProps {
  locale: ILocaleContext;
  api: INodeResourceApi;
  required?: boolean;
  label?: string;
  setErrors?: (errors?: Record<string, string>) => void;
  value: AiNodeBO;
  setValue: (value: AiNodeBO) => void;
}

export const AIAgentBOInput = (props: IAIAgentBOInputProps) => {
  const { t } = props.locale;
  const [openSelectSkills, setOpenSelectSkills] = useState(false);
  const sourceNodes = props.value.sources?.filter((i) => i.type === 'NODE') || [];
  const [defaultSelectToolset, setDefaultSelectToolset] = useState<string>('');

  const onChange = (value: any) => {
    const newValue = {
      ...value,
      ...value.property,
    };
    props.setValue(newValue);
  };

  const handleAIRewrite = async (value: any) => {
    const res = await props.api.ai.callAIWriter(
      {
        type: 'RECORD_CELL_FILLING',
        databaseId: '',
        field: {
          ...value,
          type: 'AI_TEXT',
        },
      },
      '',
      {
        createdAt: new Date().toISOString(),
      },
    );
    return res;
  };

  return (
    <div>
      <AIModelsSelector
        value={props.value.model || { kind: 'auto' }}
        onChange={(newVal) => {
          props.setValue({
            ...props.value,
            model: newVal,
          });
        }}
        // Agent 禁止 custom BYOK
        disableCustom={true}
      />

      <AIPromptConfigInput
        value={props.value.prompt || ''}
        onChange={(newValue) => {
          props.setValue({
            ...props.value,
            prompt: newValue,
          });
        }}
        // editorVariableList={editorVariableList}
      />

      <FormLabel>{t.resource.type.ai_agent.data_source}</FormLabel>
      <div className="mb-2 flex flex-col space-y-4">
        <div className="space-y-4">
          {sourceNodes.map((item, index) => (
            <SourceBOInput
              key={item.nodeId}
              value={item}
              disabledNodes={sourceNodes.map((source) => source.nodeId).filter((id) => id !== item.nodeId)}
              onChange={(newValue) => {
                const newSources = [...(props.value.sources || [])];
                if (!newValue) {
                  newSources.splice(index, 1);
                  props.setValue({ ...props.value, sources: newSources });
                  return;
                }

                // 检查是否已经存在相同的节点
                if (newValue.type === 'NODE' && newValue.nodeId) {
                  const isDuplicate = newSources.some(
                    (source, idx) => idx !== index && source.type === 'NODE' && source.nodeId === newValue.nodeId,
                  );
                  if (isDuplicate) {
                    // 如果存在重复节点，不进行更新
                    return;
                  }
                }

                newSources[index] = newValue;
                props.setValue({ ...props.value, sources: newSources });
              }}
              locale={props.locale}
              api={props.api}
            />
          ))}
        </div>

        <Button
          variant="soft"
          color="neutral"
          sx={{ width: 'fit-content', minWidth: 'unset', fontWeight: 'normal' }}
          onClick={() => {
            const newSource = [
              ...(props.value.sources || []),
              {
                type: 'NODE' as const,
                nodeId: '',
              },
            ];
            props.setValue({ ...props.value, sources: newSource });
          }}
        >
          <AddOutlined color={'var(--text-secondary)'} />
          {t.buttons.add}
        </Button>
      </div>
      <FormLabel>{t.resource.type.ai_agent.skillset}</FormLabel>
      <div className="mb-2 flex flex-col space-y-2">
        <div className="mb-2 flex flex-col space-y-2">
          {props.value.skillsets?.map((item, index) => (
            <SkillsetsSelectBOInput
              key={item.key}
              value={item}
              api={props.api}
              onDelete={() => {
                const newSkillsets = [...(props.value.skillsets || [])];
                newSkillsets.splice(index, 1);
                props.setValue({ ...props.value, skillsets: newSkillsets });
              }}
              onChange={(newValue) => {
                const newSkillsets = [...(props.value.skillsets || [])];
                if (!newValue) {
                  newSkillsets.splice(index, 1);
                  props.setValue({ ...props.value, skillsets: newSkillsets });
                  return;
                }

                newSkillsets[index] = newValue;
                props.setValue({ ...props.value, skillsets: newSkillsets });
              }}
              onSelect={(key) => {
                setOpenSelectSkills(true);
                setDefaultSelectToolset(key);
              }}
            />
          ))}
        </div>
        <SkillsSelectModal
          key={defaultSelectToolset}
          defaultSelectToolset={defaultSelectToolset}
          api={props.api}
          value={props.value.skillsets || []}
          locale={props.locale}
          open={openSelectSkills}
          onClose={() => setOpenSelectSkills(false)}
          onChange={(skillsets) => {
            props.setValue({ ...props.value, skillsets });
          }}
        />
        <Button
          sx={{ width: 'fit-content', minWidth: 'unset', fontWeight: 'normal' }}
          variant="soft"
          color="neutral"
          onClick={() => {
            setOpenSelectSkills(true);
          }}
        >
          <AddOutlined color={'var(--text-secondary)'} />
          {t.editor.add_skill}
        </Button>
      </div>
      {/* <>
        <ToolsBOInput types={[]} value={null} onChange={() => {}} locale={props.locale} api={props.api} />
        <Button variant="soft" color="neutral" sx={{ fontWeight: 'normal' }}>
          <AddOutlined />
          {t.buttons.add}
        </Button>
      </> */}
    </div>
  );
};
