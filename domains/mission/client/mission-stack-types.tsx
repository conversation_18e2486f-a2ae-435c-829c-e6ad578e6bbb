import { z } from 'zod';

export const EditorScreenMISSIONCREATERECORD = z.object({
  screenType: z.literal('MISSION_CREATE_RECORD'),
});

export const EditorScreenMISSIONREMINDERLIST = z.object({
  screenType: z.literal('MISSION_REMINDER_LIST'),
});

export const EditorScreenMISSIONREMINDERCREATE = z.object({
  screenType: z.literal('MISSION_REMINDER_CREATE'),
});

export const MissionEditorScreenPropsSchema = z.discriminatedUnion('screenType', [
  EditorScreenMISSIONREMINDERCREATE,
  EditorScreenMISSIONCREATERECORD,
  EditorScreenMISSIONREMINDERLIST,
  z.object({
    screenType: z.literal('MISSION_AFTER_ACTION_CREATE'),
  }),
  z.object({
    screenType: z.literal('MISSION_AFTER_ACTION_LIST'),
  }),
]);

export type IMissionEditorScreenPropsSchema = z.infer<typeof MissionEditorScreenPropsSchema>;
