// eslint-disable-next-line max-classes-per-file
import { isNotBlank } from 'basenext/utils/string';
import { BObuildIdConverter } from '@bika/domains/node/server/bo-builder/bo-build-id-converter';
import { isVariableTemplate, render, replaceVariables, replaceVariablesInString } from '@bika/domains/shared/server';
import {
  Action,
  ActionOutput,
  IWebhookInputBody,
  WebhookAction,
  WebhookActionInputUrlChooseV1,
  WebhookActionOutput,
  WebhookActionOutputSchema,
  WebhookActionSchema,
} from '@bika/types/automation/bo';
import { SpaceIntegrationType, WebHookUrlIntegration } from '@bika/types/integration/bo';
import { AbstractActionOmitExportBuilderHandler } from './abstract-action-builder-handler';
import { IActionRunInput, IActionRunContext } from './types';
import { sendRequest } from '../automation-helper';

export abstract class AbstractIMWebhookTypeActionHandler<
  T extends Action = Action,
> extends AbstractActionOmitExportBuilderHandler<T> {
  override desensitizeInput(input: T['input']): T['input'] {
    switch (input.urlType) {
      case 'INTEGRATION':
        return { ...input, integrationId: '' };
      case 'URL':
        return { ...input, url: '' };
      default:
        break;
    }
    // 兼容 WebhookActionInputUrlChooseV1Schema 的 urlType 为 undefined 的情况
    if ('url' in input && isNotBlank(input.url)) {
      return { ...input, url: '' };
    }
    return input;
  }

  override convertInput(input: T['input'], converter: BObuildIdConverter): T['input'] {
    const relationFunc = converter.getRelationFunc();

    const actionInput =
      'data' in input && input.data ? { ...input, data: replaceVariables(input.data, relationFunc) } : input;

    return actionInput;
  }

  override async fetchOutputMaybeFake(input: IActionRunInput): Promise<WebhookActionOutput> {
    const testOutput = input.action.state?.testResult?.output;
    if (testOutput) {
      const { success, data } = WebhookActionOutputSchema.safeParse(testOutput);
      if (success) {
        return data;
      }
    }
    const output = await super.getActionOutputFromRunHistory(input);
    if (output) {
      const { success, data } = WebhookActionOutputSchema.safeParse(output);
      if (success) {
        return data;
      }
    }
    return { status: 200, body: {} };
  }

  override async doTest(input: IActionRunInput, context: IActionRunContext): Promise<ActionOutput> {
    return this.fetchRunOutput(input, context);
  }

  protected async sendPostRawJsonRequest(
    url: string,
    data: unknown,
    context: IActionRunContext,
    additionalHeaders?: { key: string; value: string }[],
  ): Promise<WebhookActionOutput> {
    const headers = [...(additionalHeaders || []), { key: 'content-type', value: 'application/json' }];
    const body: IWebhookInputBody = {
      type: 'raw',
      format: 'json',
      data,
    };
    return sendRequest('POST', headers, url, body, { ...context });
  }

  protected async parseWebhookUrlInput<I extends WebHookUrlIntegration>(
    spaceId: string,
    integrationType: SpaceIntegrationType,
    input: WebhookActionInputUrlChooseV1,
  ): Promise<string> {
    const { urlType } = input;
    switch (urlType) {
      case 'INTEGRATION': {
        const integration = await this.parseIntegrationInput(spaceId, integrationType, input);
        return integration.getBO<I>().webHookUrl;
      }
      case 'URL':
      default:
        return input.url;
    }
  }

  protected validateWebhookUrlInput(input: WebhookActionInputUrlChooseV1): boolean {
    const { urlType } = input;
    switch (urlType) {
      case 'INTEGRATION':
        return super.validateIntegrationInput(input);
      case 'URL':
        return isNotBlank(input.url);
      default:
        break;
    }
    return false;
  }
}

export class WebhookActionHandler<T extends WebhookAction> extends AbstractActionOmitExportBuilderHandler<T> {
  override desensitizeInput(input: T['input']): T['input'] {
    const headers = input.headers?.map((header) => {
      // 常见的请求头，或者值是变量值，默认保留
      if (header.key.toLowerCase() === 'content-type' || isVariableTemplate(header.value)) {
        return header;
      }
      // 其他请求头，需要脱敏时，值置空，防止 token 之类数据泄露
      return { ...header, value: '' };
    });
    return { ...input, headers };
  }

  override convertInput(input: T['input'], converter: BObuildIdConverter): T['input'] {
    const relationFunc = converter.getRelationFunc();

    const url = replaceVariablesInString(input.url, relationFunc);

    const convertHeaders = () =>
      input.headers?.map((header) => {
        if (isVariableTemplate(header.value)) {
          return { ...header, value: replaceVariablesInString(header.value, relationFunc) };
        }
        return header;
      });

    const convertBody = () => {
      const body = input.body;
      if (!body) {
        return body;
      }
      switch (body.type) {
        case 'form-data': {
          const formData = body.formData.map((item) => {
            if (item.value) {
              return { ...item, value: replaceVariablesInString(item.value, relationFunc) };
            }
            return item;
          });
          return { ...body, formData };
        }
        case 'raw': {
          return { ...body, data: replaceVariables(body.data, relationFunc) };
        }
        default:
          break;
      }
      return body;
    };

    return { ...input, url, headers: convertHeaders(), body: convertBody() };
  }

  override validateBO(action: Action): boolean {
    const { success } = WebhookActionSchema.safeParse(action);
    if (!success) {
      return false;
    }
    return isNotBlank(action.input.url);
  }

  override async doTest(input: IActionRunInput, context: IActionRunContext): Promise<WebhookActionOutput> {
    return this.fetchRunOutput(input, context);
  }

  override async fetchRunOutput(input: IActionRunInput, context?: IActionRunContext): Promise<WebhookActionOutput> {
    const action = WebhookActionSchema.parse(input.action);
    const { method, headers, url, body, timeout } = action.input;

    const h = headers?.map((header) => {
      if (isVariableTemplate(header.value)) {
        return { ...header, value: render(header.value, { ...context }) };
      }
      return header;
    });

    // url may contain variables, need to render it with lodash template
    const formattedUrl = render(url, { ...context }, true);

    return sendRequest(method, h, formattedUrl, body, { ...context }, timeout ? timeout * 1000 : undefined);
  }
}
