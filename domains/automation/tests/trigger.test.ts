import assert from 'assert';
import dayjs from 'dayjs';
import { describe, expect, test, beforeEach } from 'vitest';
import { MockContext, waitForMatchToBeMet } from '@bika/domains/__tests__/mock';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { RecordSO } from '@bika/domains/database/server/record-so';
import { DefaultViewName } from '@bika/domains/database/server/views/types';
import { FormSO } from '@bika/domains/form/server/form-so';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { utils } from '@bika/domains/shared/server';
import { DatetimeFieldReachedTriggerSchedulerProperty } from '@bika/domains/system/server/types';
import { UserSO } from '@bika/domains/user/server';
import {
  Automation,
  DatetimeFieldReachedTriggerOutput,
  InboundEmailTriggerState,
  ManualTriggerOutput,
  RecordMatchTrigger,
  Trigger,
  WebhookReceivedTriggerOutput,
} from '@bika/types/automation/bo';
import { Database } from '@bika/types/database/bo';
import { DateTimeSO } from '@bika/types/system';
import { AutomationSO } from '../server/automation-so';
import { AutomationListener } from '../server/listener/automation-listener';
import { IHttpIfChangeTriggerEventContext, TriggerHandlerManager } from '../server/trigger';
import { TriggerSO } from '../server/trigger-so';

test('Record Match Trigger', async () => {
  // create user
  const { user, member, rootFolder } = await MockContext.initUserContext();

  const databaseTemplate: Database = {
    name: 'Database for Trigger Match Test',
    templateId: 'database_template_id',
    databaseType: 'DATUM',
    fields: [
      {
        templateId: 'long_text_field_template_id',
        name: 'Text Field for Trigger Match Test',
        type: 'LONG_TEXT',
      },
      {
        templateId: 'checkbox_field_template_id',
        name: 'Checkbox Field for Trigger Match Test',
        type: 'CHECKBOX',
      },
      {
        templateId: 'single_select_field_template_id',
        name: 'Single Select Field for Trigger Match Test',
        type: 'SINGLE_SELECT',
        property: {
          options: [
            {
              templateId: 'select_option_template_id',
              id: 'select_option_id',
              name: 'select option name',
            },
          ],
        },
      },
    ],
    resourceType: 'DATABASE',
    records: [
      {
        data: {
          long_text_field_template_id: 'i am a record in record match trigger test',
        },
      },
    ],
  };
  const automationTemplate: Automation = {
    resourceType: 'AUTOMATION',
    templateId: 'trigger_match_test_automation_template_id',
    name: 'Automation for Trigger Match Test',
    triggers: [
      {
        triggerType: 'RECORD_CREATED',
        templateId: 'record_created_trigger',
        input: {
          type: 'DATABASE',
          databaseTemplateId: 'database_template_id',
        },
      },
      {
        triggerType: 'RECORD_MATCH',
        templateId: 'record_match_trigger',
        input: {
          type: 'DATABASE_WITH_FILTER',
          databaseTemplateId: 'database_template_id',
        },
      },
      {
        triggerType: 'RECORD_MATCH',
        templateId: 'filter_text_contains_trigger',
        description: 'Record Match Trigger with Filter for Trigger Match Test',
        input: {
          type: 'DATABASE_WITH_FILTER',
          databaseTemplateId: 'database_template_id',
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldType: 'LONG_TEXT',
                fieldTemplateId: 'long_text_field_template_id',
                clause: {
                  operator: 'Contains',
                  value: 'Keyword',
                },
              },
            ],
          },
        },
      },
      {
        triggerType: 'RECORD_MATCH',
        templateId: 'filter_is_option_id_trigger',
        input: {
          type: 'DATABASE_WITH_FILTER',
          databaseTemplateId: 'database_template_id',
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldType: 'SINGLE_SELECT',
                fieldTemplateId: 'single_select_field_template_id',
                clause: {
                  operator: 'Is',
                  value: 'select_option_id',
                },
              },
            ],
          },
        },
      },
    ],
    actions: [],
    status: 'ACTIVE',
  };

  // 在模板内创建表格和自动化
  const templateFolderSO = await MockContext.initEmptyTemplateNodeFolder(user.id, rootFolder);
  const databaseNode = await templateFolderSO.createChildSimple(user, databaseTemplate);
  const databaseSO = await databaseNode.toResourceSO<DatabaseSO>();
  // find record
  const records = await databaseSO.getRecordsAsPage();
  expect(records.length).toBe(1);
  const recordSO = records[0];
  const recordVO = recordSO.toVO({ withTemplateId: true });
  // create automation
  const automationNode = await templateFolderSO.createChildSimple(user, automationTemplate);
  const automationSO = await automationNode.toResourceSO<AutomationSO>();
  const triggers = await automationSO.getTriggers();
  expect(triggers.length).toBe(4);
  // trigger do test
  await triggers[2].doTest(user);
  await triggers[3].doTest(user);

  // 在模板外创建表格和自动化
  const databaseNode2 = await rootFolder.createChildSimple(user, databaseTemplate);
  const databaseSO2 = await databaseNode2.toResourceSO<DatabaseSO>();
  // find record
  const records2 = await databaseSO2.getRecordsAsPage();
  expect(records2.length).toBe(1);
  const recordSO2 = records2[0];
  const recordVO2 = recordSO2.toVO({ withTemplateId: true });
  // create automation
  const autoNode = await rootFolder.createChildSimple(user, automationTemplate);

  await autoNode.move(user, { preNodeId: databaseNode2.id });

  // 模板内的表格新增记录，仅匹配到模板内的自动化节点
  const resp = await AutomationListener.findMatchActivatedTriggers(['RECORD_CREATED'], {
    database: databaseSO,
  });
  expect(resp.length).toBe(1);
  expect(resp[0].automationId).toBe(automationNode.id);
  expect(resp[0].id).toBe(triggers[0].id);
  expect(resp[0].templateId).toBe('record_created_trigger');
  const output = await triggers[0].match({
    database: databaseSO,
    records: [recordVO, recordVO2], // recordVO2 不在 databaseSO 中，不会匹配到
  });
  expect(output).toBeDefined();
  expect(Array.isArray(output) && output.length === 1).toBeTruthy();

  // 模板外的触发器，使用 databaseTemplateId 匹配不到节点
  const resp2 = await AutomationListener.findMatchActivatedTriggers(['RECORD_CREATED'], {
    database: databaseSO2,
  });
  expect(resp2.length).toBe(0);
  // 模板内的触发器，可以查询到
  const recordMatchTriggerResp = await AutomationListener.findMatchActivatedTriggers(['RECORD_MATCH'], {
    database: databaseSO,
  });
  expect(recordMatchTriggerResp.length).toBe(3);
  const recordMatchTriggerOutput = await triggers[1].match({
    database: databaseSO,
    records: [recordVO, recordVO2], // recordVO2 不在 databaseSO 中，不会匹配到
  });
  expect(recordMatchTriggerOutput).toHaveLength(1);
  const secondMatchTriggerOutput = await triggers[2].match({
    database: databaseSO,
    records: [recordVO, recordVO2],
  });
  expect(secondMatchTriggerOutput).toHaveLength(0);

  const recordContainKeyword = await databaseSO.createRecord(user, member, {
    long_text_field_template_id: 'Contains Keyword',
  });
  const recordVO3 = recordContainKeyword.toVO({ withTemplateId: true });
  const secondMatchTriggerOutput2 = await triggers[2].match({
    database: databaseSO,
    records: [recordVO, recordVO3, recordVO2],
  });
  expect(secondMatchTriggerOutput2).toBeDefined();
  expect(secondMatchTriggerOutput2).toHaveLength(1);

  const thirdMatchTriggerOutput = await triggers[3].match({
    database: databaseSO,
    records: [recordVO, recordVO3, recordVO2],
  });
  expect(thirdMatchTriggerOutput).toHaveLength(0);

  // 验证创建记录（recordContainKeyword）事件，触发了自动化
  await waitForMatchToBeMet(
    async () => {
      // 获取运行历史，确认触发器成功匹配了3个
      const { pagination, list } = await automationSO.getRunHistories();
      if (pagination.total !== 1) {
        return false;
      }
      // 创建记录触发
      const createRecordRunHistory = list[0].toDetailVO();
      if (createRecordRunHistory.triggers.length !== 3) {
        return false;
      }
      expect(createRecordRunHistory.triggers[0].templateId).toBe('record_created_trigger');
      expect(createRecordRunHistory.triggers[1].templateId).toBe('record_match_trigger');
      expect(createRecordRunHistory.triggers[2].templateId).toBe('filter_text_contains_trigger');
      return true;
    },
    5000,
    100,
  ).catch((error: Error) => {
    throw new Error(error.message);
  });

  // 测试记录修改触发成功
  await databaseSO.updateRecords(user, [
    {
      recordId: recordContainKeyword.id,
      cells: {
        single_select_field_template_id: ['select option name'],
      },
    },
  ]);
  await waitForMatchToBeMet(
    async () => {
      // 获取运行历史，确认触发器成功匹配了3个
      const { pagination, list } = await automationSO.getRunHistories();
      console.log('pagination.total: ', pagination.total);
      if (pagination.total !== 2) {
        return false;
      }
      // 更新记录触发
      const updateRecordRunHistory = list[0].toDetailVO();
      if (updateRecordRunHistory.triggers.length !== 2) {
        return false;
      }
      expect(updateRecordRunHistory.triggers[0].templateId).toBe('record_match_trigger');
      expect(updateRecordRunHistory.triggers[1].templateId).toBe('filter_is_option_id_trigger');
      // 记录只修改 long_text_field_template_id 字段，即使文本单元格包含关键字，也不会触发 filter_text_contains_trigger
      return true;
    },
    5000,
    100,
  ).catch((error: Error) => {
    throw new Error(error.message);
  });

  const writeOptionRecord = await databaseSO.createRecord(user, member, {
    single_select_field_template_id: ['select option name'],
  });
  const recordVO4 = await writeOptionRecord.toVO({ withTemplateId: true });
  const thirdMatchTriggerOutput2 = await triggers[3].match({
    database: databaseSO,
    records: [recordVO4],
    modifiedFieldKeys: ['single_select_field_template_id'],
  });
  expect(thirdMatchTriggerOutput2).toHaveLength(1);
});

test('Record Created Trigger', async () => {
  // create user
  const { user, member, space } = await MockContext.initUserContext();
  // use base template
  const templateFolder = await space.installTemplateById(user, 'base-crm');
  expect(templateFolder.model.type).toBe('FOLDER');

  const randomName = utils.generateRandomString(10);
  const randomEmail = utils.generateEmail();
  // create automation
  const automationTpl: Automation = {
    templateId: 'record_created_trigger_bot',
    resourceType: 'AUTOMATION',
    name: 'Automation for Record Created Trigger Test',
    status: 'ACTIVE',
    triggers: [
      {
        triggerType: 'RECORD_CREATED',
        input: {
          type: 'DATABASE',
          databaseTemplateId: 'organization',
        },
      },
    ],
    actions: [
      {
        description: 'Create A People Record When Organization Created',
        actionType: 'CREATE_RECORD',
        input: {
          type: 'RECORD_BODY',
          databaseTemplateId: 'people',
          fieldKeyType: 'TEMPLATE_ID',
          data: {
            name: randomName,
            email: randomEmail,
          },
        },
      },
    ],
  };
  const automationNode = await templateFolder.createChildSimple(user, automationTpl);
  const automationSO = await automationNode.toResourceSO<AutomationSO>();
  const triggers = await automationSO.getTriggers();
  expect(triggers.length).toBe(1);

  const peopleDatabaseNodeSO = await templateFolder.findChildNodeByTemplateId('people');
  const peopleDatabaseSO = await peopleDatabaseNodeSO?.toResourceSO<DatabaseSO>();
  expect(peopleDatabaseSO).toBeDefined();
  expect(peopleDatabaseSO?.model.name).toBe('People');
  // people database should be empty when automation executed
  const recordCount = await peopleDatabaseSO!.getRecordsCount();
  expect(recordCount).toBe(0);

  const organizationDatabaseNodeSO = await templateFolder.findChildNodeByTemplateId('organization');
  const organizationDatabaseSO = await organizationDatabaseNodeSO?.toResourceSO<DatabaseSO>();
  expect(organizationDatabaseSO).toBeDefined();
  // create a record in organization database to trigger automation
  const newRecord = await organizationDatabaseSO!.createRecord(user, member, {
    name: 'i am a organization',
    description: 'for test create record trigger',
  });
  expect(newRecord).toBeDefined();
  expect(newRecord.id).toBeDefined();

  const orgViews = await organizationDatabaseSO?.getViews();
  expect(orgViews?.length).toBe(1);
  expect(orgViews![0].name).toBe(DefaultViewName);
  const orgRecords = await organizationDatabaseSO!.getRecordsAsPage();
  expect(orgRecords.length).toBe(1);

  // trigger do test
  const trigger = triggers[0];
  await trigger.doTest(user);

  // wait for automation event executed
  await waitForMatchToBeMet(
    async () => {
      // check people database should have 1 record
      const peopleRecords = await peopleDatabaseSO!.getRecordsAsPage();
      if (peopleRecords.length !== 1) {
        return false;
      }
      expect(peopleRecords[0].getCellValue('name')).toBe(randomName);
      expect(peopleRecords[0].getCellValue('email')).toBe(randomEmail);
      return true;
    },
    5000,
    100,
  ).catch((error: Error) => {
    throw new Error(error.message);
  });
});

test('Form Submitted Trigger', async () => {
  // create user
  const { user, member, space, rootFolder } = await MockContext.initUserContext();

  // create empty template folder
  const templateFolderSO = await MockContext.initEmptyTemplateNodeFolder(user.id, rootFolder);

  // create database
  const databaseTemplate: Database = {
    resourceType: 'DATABASE',
    name: 'Database for Form Submitted Trigger Test',
    templateId: 'form_submitted_trigger_database',
    databaseType: 'DATUM',
    fields: [
      {
        templateId: 'long_text_field',
        name: 'Long Text',
        type: 'LONG_TEXT',
      },
      {
        templateId: 'checkbox_field',
        name: 'Checkbox',
        type: 'CHECKBOX',
      },
    ],
    views: [
      {
        templateId: 'form_submitted_trigger_view',
        name: 'ALL',
        type: 'TABLE',
      },
    ],
  };
  const databaseId = await templateFolderSO.createChildren(user, [databaseTemplate]);
  const databaseSO = await DatabaseSO.init(databaseId);
  const databaseNode2 = await templateFolderSO.createChildSimple(user, {
    resourceType: 'DATABASE',
    name: 'database2',
  });

  // create form
  const formId = await templateFolderSO.createChildren(user, [
    {
      resourceType: 'FORM',
      name: 'form',
      templateId: 'form_submitted_trigger_form',
      databaseId,
      metadata: {
        type: 'VIEW',
        viewId: databaseSO.model.views[0].id,
      },
    },
  ]);
  const formSO = await FormSO.init(formId);
  const formNode2 = await templateFolderSO.createChildSimple(user, {
    resourceType: 'FORM',
    name: 'form2',
    formType: 'DATABASE',
    databaseId: databaseNode2.id,
    viewId: '',
  });

  // create automation
  const automationTemplate: Automation = {
    resourceType: 'AUTOMATION',
    templateId: 'form_submitted_trigger_automation',
    name: 'Automation for Form Submitted Trigger Test',
    triggers: [
      {
        triggerType: 'FORM_SUBMITTED',
        templateId: 'form_submitted_trigger_use_form_id',
        input: {
          type: 'FORM',
          formId: formSO.id,
        },
      },
      {
        triggerType: 'FORM_SUBMITTED',
        templateId: 'form_submitted_trigger_use_form_template_id',
        input: {
          type: 'FORM',
          formTemplateId: formSO.model.templateId!,
        },
      },
      {
        triggerType: 'FORM_SUBMITTED',
        templateId: 'form_submitted_trigger_use_other_form',
        input: {
          type: 'FORM',
          formId: formNode2.id,
        },
      },
    ],
    actions: [
      {
        actionType: 'UPDATE_RECORD',
        input: {
          type: 'SPECIFY_RECORD_BODY',
          databaseId: databaseSO.model.id,
          recordId: '<%= _triggers.form_submitted_trigger_use_form_id.record.id %>',
          fieldKeyType: 'TEMPLATE_ID',
          data: {
            checkbox_field: true,
          },
        },
      },
    ],
    status: 'ACTIVE',
  };
  const automationId = await templateFolderSO.createChildren(user, [automationTemplate], {
    loadExistingResource: true, // 引用了已创建的表单，需要主动加载文件夹中已存在的资源，建立关联
  });
  const automationNode = await NodeSO.init(automationId);
  const automationSO = await automationNode.toResourceSO<AutomationSO>();
  const triggers = await automationSO.getTriggers();
  expect(triggers.length).toBe(3);
  // trigger do test
  const firstTrigger = triggers[0];
  await firstTrigger.doTest(user);
  expect(firstTrigger.state!.testResult!.success).toBe(true);

  const recordSO = await databaseSO.createRecord(
    user,
    member,
    { long_text_field: 'i am a record in template' },
    { formId: formSO.id }, // formId 用于触发器匹配
  );
  // 确认自动化执行成功
  await waitForMatchToBeMet(
    async () => {
      const recordSO2 = await RecordSO.init(recordSO.id, space.id);
      const recordVO2 = await recordSO2.toVO({ withTemplateId: true });
      return recordVO2.cells.checkbox_field.data === true;
    },
    3000,
    100,
  ).catch((error: Error) => {
    throw new Error(error.message);
  });

  // 获取运行历史，确认触发器成功匹配了2个
  const { pagination, list } = await automationSO.getRunHistories();
  expect(pagination.total).toBe(1);
  const runHistoryDetail = list[0].toDetailVO();
  expect(runHistoryDetail.triggers.length).toBe(2);
  expect(runHistoryDetail.triggers[0].templateId).toBe('form_submitted_trigger_use_form_id');
  expect(runHistoryDetail.triggers[1].templateId).toBe('form_submitted_trigger_use_form_template_id');

  // continue do test
  const secondTrigger = triggers[1];
  await secondTrigger.doTest(user);
  expect(secondTrigger.state!.testResult!.success).toBe(true);
  const thirdTrigger = triggers[2];
  await thirdTrigger.doTest(user);
  expect(thirdTrigger.state!.testResult!.success).toBe(true);
});

test('Datetime Filed Reached Trigger', async () => {
  // create user
  const { user, member, space, rootFolder } = await MockContext.initUserContext();

  const templateFolderSO = await MockContext.initEmptyTemplateNodeFolder(user.id, rootFolder);

  const now = DateTimeSO.now().toDayJS();
  const sevenDayLatter = now.add(7, 'day');

  // create database
  const databaseTemplate: Database = {
    name: 'database',
    templateId: 'due_date_database',
    databaseType: 'DATUM',
    fields: [
      {
        templateId: 'primary',
        name: 'Primary',
        type: 'LONG_TEXT',
      },
      {
        templateId: 'due_date_field',
        name: 'Due Date',
        type: 'DATETIME',
        property: {
          autofill: true, // 自动添加今天时间
          dateFormat: 'YYYY/MM/DD',
          includeTime: true,
        },
      },
      {
        templateId: 'created_time',
        name: 'Created Time',
        type: 'CREATED_TIME',
        property: {
          dateFormat: 'YYYY-MM-DD',
          timeFormat: 'HH:mm',
          includeTime: false,
        },
      },
      {
        templateId: 'updated_time',
        name: 'Update Time',
        type: 'MODIFIED_TIME',
        property: {
          dateFormat: 'YYYY-MM-DD',
          timeFormat: 'HH:mm',
          includeTime: true,
        },
      },
    ],
    resourceType: 'DATABASE',
    // 直接在模板内创建记录，调用 createRecord()会触发创建记录事件，CI环境执行太慢，先创建记录，再创建的自动化，前者的异步事件还能匹配到后创建的自动化
    records: [
      {
        data: {
          // 时间字段单元格写入四天之前的记录
          due_date_field: now.subtract(4, 'day').toISOString(),
        },
      },
      {
        data: {
          // 写入七天之后的记录
          due_date_field: sevenDayLatter.toISOString(),
        },
      },
    ],
  };
  const databaseNodeSO = await templateFolderSO.createChildSimple(user, databaseTemplate);
  const databaseSO = await databaseNodeSO.toResourceSO<DatabaseSO>();
  // find record
  const records = await databaseSO.getRecordsAsPage({ limit: 100 });
  expect(records.length).toBe(2);
  const recordSO = records.find((r) => r.getCellData('due_date_field') === sevenDayLatter.toISOString());
  expect(recordSO).toBeDefined();
  // 四天之前的记录
  const fourDayLatterRecord = records.find((r) => r.id !== recordSO!.id);
  expect(fourDayLatterRecord).toBeDefined();

  // create automation
  const automationTemplate: Automation = {
    templateId: 'datetime_field_reached_trigger_bot',
    resourceType: 'AUTOMATION',
    name: 'Automation for Datetime Filed Reached Trigger Test',
    status: 'ACTIVE',
    triggers: [
      {
        triggerType: 'DATETIME_FIELD_REACHED',
        templateId: 'seven_days_before_due_date',
        description: 'Triggered seven days before due date',
        input: {
          type: 'DATETIME_FIELD_REACHED',
          databaseTemplateId: 'due_date_database',
          fieldTemplateId: 'due_date_database:due_date_field',
          // 提前7天
          datetime: {
            type: 'AHEAD',
            unit: 'DAY',
            value: 7,
          },
        },
      },
      {
        triggerType: 'DATETIME_FIELD_REACHED',
        templateId: 'at_due_date_zero_clock',
        description: 'Triggered at 00:00 of due date',
        input: {
          type: 'DATETIME_FIELD_REACHED',
          databaseTemplateId: 'due_date_database',
          fieldTemplateId: 'due_date_database:due_date_field',
          datetime: {
            type: 'TODAY',
          },
        },
      },
      {
        triggerType: 'DATETIME_FIELD_REACHED',
        templateId: 'three_days_after_due_date',
        description: 'Triggered at 12:00 three days after due date',
        input: {
          type: 'DATETIME_FIELD_REACHED',
          databaseTemplateId: 'due_date_database',
          fieldTemplateId: 'due_date_database:due_date_field',
          // 延后三天12点
          datetime: {
            type: 'DELAY',
            unit: 'DAY',
            value: 3,
            hour: 12,
          },
        },
      },
    ],
    actions: [],
  };
  const automationId = await templateFolderSO.createChildren(user, [automationTemplate], {
    loadExistingResource: true,
  });
  const automationSO = await AutomationSO.init(automationId);
  const triggers = await automationSO.getTriggers();
  expect(triggers.length).toBe(3);
  // 单元格的时间是四天之前的那一条记录，三个触发器都不满足条件；其次判断单元格的时间是七天之后的那一条记录
  // 第一个触发器，单元格的时间是7天之后，触发器虽然是提前7天，但是前者计算得到的时间已经过了当前时间点，不能再触发，所以没有scheduler
  const firstTrigger = triggers[0];
  await firstTrigger.doTest(user);
  const firstTriggerScheduler = await firstTrigger.findSchedulers();
  expect(firstTriggerScheduler.length).toBe(0);
  // 第二个触发器，单元格的时间是7天之后，触发器是当天，所以会有scheduler
  const secondTrigger = triggers[1];
  const secondTriggerScheduler = await secondTrigger.findSchedulers();
  expect(secondTriggerScheduler.length).toBe(1);
  expect(secondTriggerScheduler[0].model.runTime).toStrictEqual(sevenDayLatter.toDate());
  // 调度器属性，满足同一时刻触发自动化条件的时间只有sevenDayLatter，所以只有begin
  const secondTriggerSchedulerProperty = secondTriggerScheduler[0]
    .property as DatetimeFieldReachedTriggerSchedulerProperty;
  expect(secondTriggerSchedulerProperty.recordId).toBe(recordSO!.id);
  expect(secondTriggerSchedulerProperty.cellData).toBe(sevenDayLatter.toISOString());
  expect(secondTriggerSchedulerProperty.matchDateRange.begin).toBe(sevenDayLatter.toISOString());
  expect(secondTriggerSchedulerProperty.matchDateRange.end).toBeUndefined();
  // 第三个触发器，单元格的时间是7天之后，触发器是延后3天12点，即10天之后12点，所以会有scheduler
  const thirdTrigger = triggers[2];
  const thirdTriggerScheduler = await thirdTrigger.findSchedulers();
  expect(thirdTriggerScheduler.length).toBe(1);
  expect(thirdTriggerScheduler[0].model.runTime).toStrictEqual(
    sevenDayLatter.add(3, 'day').hour(12).startOf('hour').toDate(),
  );
  // 调度器属性，满足同一时刻触发自动化的记录单元格，时间范围是7天后0点到8天后0点（次日0点不包含），这段时间内的所有记录都会在10天后12点触发
  const thirdTriggerSchedulerProperty = thirdTriggerScheduler[0]
    .property as DatetimeFieldReachedTriggerSchedulerProperty;
  expect(thirdTriggerSchedulerProperty.recordId).toBe(recordSO!.id);
  expect(thirdTriggerSchedulerProperty.cellData).toBe(sevenDayLatter.toISOString());
  expect(thirdTriggerSchedulerProperty.matchDateRange.begin).toBe(sevenDayLatter.startOf('day').toISOString());
  expect(thirdTriggerSchedulerProperty.matchDateRange.end).toBe(
    sevenDayLatter.add(1, 'day').startOf('day').toISOString(),
  );

  // 测试触发器匹配结果
  describe('datetime filed reached trigger - match result', async () => {
    // 未偏移时分的触发器，只匹配同一个时刻的记录
    const secondTriggerMatchResult = await secondTrigger.match({
      runTime: '',
      property: secondTriggerSchedulerProperty,
    });
    expect(secondTriggerMatchResult).toBeDefined();
    expect(Array.isArray(secondTriggerMatchResult) && secondTriggerMatchResult.length === 1).toBeTruthy();
    expect((secondTriggerMatchResult as DatetimeFieldReachedTriggerOutput[])[0].record.id).toBe(recordSO!.id);
    // 偏移时分的触发器，匹配时间范围内的记录
    const thirdTriggerMatchResult = await thirdTrigger.match({
      runTime: '',
      property: thirdTriggerSchedulerProperty,
    });
    expect(Array.isArray(thirdTriggerMatchResult) && thirdTriggerMatchResult.length === 1).toBeTruthy();
    expect((thirdTriggerMatchResult as DatetimeFieldReachedTriggerOutput[])[0].record.id).toBe(recordSO!.id);

    // 分别创建一条七天之后，和七天之后开始一刻的记录
    const recordSO2 = await databaseSO.createRecord(user, member, {
      due_date_field: sevenDayLatter.toISOString(),
    });
    const sevenDayStart = sevenDayLatter.startOf('day');
    const recordSO3 = await databaseSO.createRecord(user, member, {
      due_date_field: sevenDayStart.toISOString(),
    });

    // 对于第二个触发器（当天），满足同一个时刻条件的记录，多了一条
    const secondTriggerMatchResult2 = await secondTrigger.match({
      runTime: '',
      property: secondTriggerSchedulerProperty,
    });
    expect(Array.isArray(secondTriggerMatchResult2) && secondTriggerMatchResult2.length === 2).toBeTruthy();
    expect(
      (secondTriggerMatchResult2 as DatetimeFieldReachedTriggerOutput[]).some((r) => r.record.id === recordSO2.id),
    ).toBeTruthy();

    // 对于第三个触发器（延后3天12点），满足时间范围条件的记录，多了两条
    const thirdTriggerMatchResult2 = await thirdTrigger.match({
      runTime: '',
      property: thirdTriggerSchedulerProperty,
    });
    expect(Array.isArray(thirdTriggerMatchResult2) && thirdTriggerMatchResult2.length === 3).toBeTruthy();
    expect(
      (thirdTriggerMatchResult2 as DatetimeFieldReachedTriggerOutput[]).some((r) => r.record.id === recordSO2.id),
    ).toBeTruthy();
    expect(
      (thirdTriggerMatchResult2 as DatetimeFieldReachedTriggerOutput[]).some((r) => r.record.id === recordSO3.id),
    ).toBeTruthy();
  });

  // 测试触发器更新
  describe('datetime filed reached trigger - update trigger', async () => {
    // 第三个触发器，修改为其他类型
    await thirdTrigger.update(user, {
      trigger: {
        triggerType: 'MANUALLY',
      },
    });
    await waitForMatchToBeMet(
      async () => {
        const schedulers = await thirdTrigger.findSchedulers();
        return schedulers.length === 0;
      },
      2000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
    // 再次修改为延后5天10点
    await thirdTrigger.update(user, {
      trigger: {
        triggerType: 'DATETIME_FIELD_REACHED',
        templateId: 'three_days_after_due_date',
        description: 'Triggered at 10:00 three days after due date',
        input: {
          type: 'DATETIME_FIELD_REACHED',
          databaseTemplateId: 'due_date_database',
          fieldTemplateId: 'due_date_database:due_date_field',
          datetime: {
            type: 'DELAY',
            unit: 'DAY',
            value: 5,
            hour: 10,
          },
        },
      },
    });
    await waitForMatchToBeMet(
      async () => {
        const schedulers = await thirdTrigger.findSchedulers();
        if (schedulers.length !== 1) {
          return false;
        }
        // 匹配到4天之前的记录，延后5天10点，即明天10点
        expect(schedulers[0].runTime).toStrictEqual(now.add(1, 'day').hour(10).startOf('hour').toDate());
        // 调度器属性，满足同一时刻触发自动化的记录单元格，时间范围是4天前0点到3天后0点（次日0点不包含），这段时间内的所有记录都会在明天10点触发
        const { recordId, cellData, matchDateRange } = schedulers[0]
          .property as DatetimeFieldReachedTriggerSchedulerProperty;
        const { begin, end } = matchDateRange;
        expect(recordId).toBe(fourDayLatterRecord!.id);
        expect(cellData).toBe(now.subtract(4, 'day').toISOString());
        expect(begin).toBe(now.subtract(4, 'day').startOf('day').toISOString());
        expect(end).toBe(now.subtract(3, 'day').startOf('day').toISOString());
        return true;
      },
      2000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
  });

  // 测试记录事件
  describe('datetime filed reached trigger - record event', async () => {
    // 再创建一条写入七天之后最后一刻的记录
    const sevenDayLatterEnd = sevenDayLatter.endOf('day');
    const recordSO4 = await databaseSO.createRecord(user, member, {
      due_date_field: sevenDayLatterEnd.toISOString(),
    });
    // tip: 异步事件会匹配更新Trigger.scheduler
    // 对于第一个触发器（提前7天），单元格的时间偏移后是今天的最后一刻，除非当前时间到/过了今天的最后一刻，否则会小于当前时间，仍满足触发条件
    await waitForMatchToBeMet(
      async () => {
        // 兼容极端情况，开始运行时间接近或就是今天的最后一刻，单测跑到这里过了今天，那么这一条记录的时间对于此刻而言，是六天之后，已经不满足触发条件
        if (dayjs().isAfter(now.endOf('day'))) {
          return true;
        }
        const firstTriggerScheduler2 = await firstTrigger.findSchedulers();
        if (firstTriggerScheduler2.length !== 1) {
          return false;
        }
        // 调度器属性
        const { recordId, cellData, matchDateRange } = firstTriggerScheduler2[0]
          .property as DatetimeFieldReachedTriggerSchedulerProperty;
        const { begin, end } = matchDateRange;
        expect(recordId).toBe(recordSO4.id);
        expect(cellData).toBe(sevenDayLatterEnd.toISOString());
        expect(begin).toBe(sevenDayLatterEnd.toISOString());
        expect(end).toBeUndefined();
        return true;
      },
      2000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });

    // 修改记录
    await databaseSO.updateRecords(null, [
      {
        recordId: recordSO4.id,
        cells: {
          due_date_field: now.toISOString(),
        },
      },
    ]);
    await waitForMatchToBeMet(
      async () => {
        const schedulers = await firstTrigger.findSchedulers();
        return schedulers.length === 0;
      },
      2000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
    // 修改记录，写入八天之后的时间
    const eightDayLatter = sevenDayLatter.add(1, 'day');
    await databaseSO.updateRecords(null, [
      {
        recordId: recordSO4.id,
        cells: {
          due_date_field: eightDayLatter.toISOString(),
        },
      },
    ]);
    await waitForMatchToBeMet(
      async () => {
        const schedulers = await firstTrigger.findSchedulers();
        if (schedulers.length !== 1) {
          return false;
        }
        const { runTime, property } = schedulers[0];
        const { recordId, cellData } = property as DatetimeFieldReachedTriggerSchedulerProperty;
        expect(runTime).toStrictEqual(now.add(1, 'day').toDate());
        expect(recordId).toBe(recordSO4.id);
        expect(cellData).toBe(eightDayLatter.toISOString());
        return true;
      },
      2000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
    // 修改另外一条记录，写入八天之后0点1分的时间
    const eightDayLatterZeroClockOneMinute = eightDayLatter.startOf('day').add(1, 'minute');
    await databaseSO.updateRecords(user, [
      {
        recordId: recordSO!.id,
        cells: {
          due_date_field: eightDayLatterZeroClockOneMinute.toISOString(),
        },
      },
    ]);
    await waitForMatchToBeMet(
      async () => {
        const schedulers = await firstTrigger.findSchedulers();
        if (schedulers.length !== 1) {
          return false;
        }
        const { runTime, property } = schedulers[0];
        const { recordId, cellData } = property as DatetimeFieldReachedTriggerSchedulerProperty;
        if (recordId === recordSO!.id) {
          expect(runTime).toStrictEqual(now.add(1, 'day').startOf('day').add(1, 'minute').toDate());
          expect(cellData).toBe(eightDayLatterZeroClockOneMinute.toISOString());
          return true;
        }
        return false;
      },
      2000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });

    // 删除记录
    await databaseSO.deleteRecords(user, [recordSO!.id]);
    await waitForMatchToBeMet(
      async () => {
        const schedulers = await firstTrigger.findSchedulers();
        if (schedulers.length !== 1) {
          return false;
        }
        const { recordId } = schedulers[0].property as DatetimeFieldReachedTriggerSchedulerProperty;
        return recordId === recordSO4.id;
      },
      2000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
    await databaseSO.deleteRecords(user, [recordSO4.id]);
    await waitForMatchToBeMet(
      async () => {
        const schedulers = await firstTrigger.findSchedulers();
        return schedulers.length === 0;
      },
      2000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
  });

  // 测试字段事件
  describe('datetime filed reached trigger - field event', async () => {
    const schedulers = await automationSO.findSchedulers();
    expect(schedulers.length).toBeGreaterThan(0);

    const fieldSO = databaseSO.getFields().find((f) => f.templateId === 'due_date_field');
    expect(fieldSO).toBeDefined();

    const updatedField = await fieldSO!.update(user, {
      templateId: fieldSO!.templateId,
      name: fieldSO!.name,
      type: 'LONG_TEXT',
      property: undefined,
    });
    await waitForMatchToBeMet(
      async () => {
        const schedulerSOs = await automationSO.findSchedulers();
        return schedulerSOs.length === 0;
      },
      2000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });

    await updatedField.update(user, {
      templateId: updatedField.templateId,
      name: updatedField.name,
      type: 'DATETIME',
      property: {
        autofill: true,
        dateFormat: 'YYYY/MM/DD',
        includeTime: true,
      },
    });
    await waitForMatchToBeMet(
      async () => {
        const schedulerSOs = await automationSO.findSchedulers();
        return schedulerSOs.length === schedulers.length;
      },
      2000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });

    // 删除字段，触发器会被删除
    await fieldSO!.delete(user);
    await waitForMatchToBeMet(
      async () => {
        const schedulerSOs = await automationSO.findSchedulers();
        return schedulerSOs.length === 0;
      },
      2000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
  });
});

test('Scheduler Trigger', async () => {
  // create user
  const { user, rootFolder } = await MockContext.initUserContext();
  const templateFolderSO = await MockContext.initEmptyTemplateNodeFolder(user.id, rootFolder);

  const now = dayjs();
  const tomorrowZeroClock = now.add(1, 'day').startOf('day');
  const automationTemplate: Automation = {
    templateId: 'scheduler_trigger_bot',
    resourceType: 'AUTOMATION',
    name: 'Automation for Scheduler Trigger Test',
    status: 'ACTIVE',
    triggers: [
      {
        triggerType: 'SCHEDULER',
        input: {
          type: 'SCHEDULER',
          scheduler: {
            timezone: 'AUTO',
            datetime: {
              type: 'TOMORROW',
            },
            repeat: {
              every: {
                type: 'DAY',
                interval: 1,
              },
            },
          },
        },
      },
      {
        triggerType: 'SCHEDULER',
        input: {
          type: 'SCHEDULER',
          scheduler: {
            timezone: 'AUTO',
            datetime: '2024-01-01 10:00:00',
          },
        },
      },
    ],
    actions: [],
  };

  // create automation
  // UTC版
  const automationNodeSO = await templateFolderSO.createChildSimple(user, automationTemplate);
  const automationSO = await automationNodeSO.toResourceSO<AutomationSO>();
  const schedulers = await automationSO.findSchedulers();
  expect(schedulers.length).toBe(1);
  expect(schedulers[0].model.runTime).toStrictEqual(tomorrowZeroClock.toDate());

  // 时区版
  await user.updateUserInfo({ timeZone: 'Asia/Shanghai' }); // 改变用户时区后，AUTO就不同了
  const userTomorrowZeroClock = now.tz('Asia/Shanghai').add(1, 'day').startOf('day');
  const automationNodeSO2 = await templateFolderSO.createChildSimple(user, automationTemplate);
  const automationSO2 = await automationNodeSO2.toResourceSO<AutomationSO>();
  const schedulers2 = await automationSO2.findSchedulers();
  expect(schedulers2.length).toBe(1);
  expect(schedulers2[0].model.runTime).toStrictEqual(userTomorrowZeroClock.toDate());

  const triggers = await automationSO.getTriggers();
  expect(triggers.length).toBe(2);
  // trigger do test
  const trigger = triggers[0];
  await trigger.doTest(user);
});

test('Http If Change Trigger', async () => {
  // create user
  const { user, rootFolder } = await MockContext.initUserContext();
  const templateFolderSO = await MockContext.initEmptyTemplateNodeFolder(user.id, rootFolder);

  const now = dayjs().toISOString();
  const automationTemplate: Automation = {
    templateId: 'http_if_change_trigger_bot',
    resourceType: 'AUTOMATION',
    name: 'Automation for Http If Change Trigger Test',
    status: 'ACTIVE',
    triggers: [
      {
        triggerType: 'HTTP_IF_CHANGE',
        templateId: 'first_failed_http_if_change_trigger',
        input: {
          type: 'HTTP_IF_CHANGE',
          scheduler: { datetime: now },
          url: 'invalid_url',
          method: 'GET',
          headers: [],
          policy: { type: 'FIRST_FAILED' },
        },
      },
      {
        triggerType: 'HTTP_IF_CHANGE',
        templateId: 'every_failed_http_if_change_trigger',
        input: {
          type: 'HTTP_IF_CHANGE',
          scheduler: { datetime: now },
          url: 'https://aitable.ai/fusion/v1/datasheets/dstxxx/records',
          method: 'GET',
          headers: [],
          policy: { type: 'EVERY_FAILED' },
        },
      },
      {
        triggerType: 'HTTP_IF_CHANGE',
        templateId: 'response_changed_http_if_change_trigger',
        input: {
          type: 'HTTP_IF_CHANGE',
          scheduler: { datetime: now },
          url: 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=68760414-3289-418c-a0a7-cb04c1e84fb0',
          method: 'GET',
          headers: [],
          policy: { type: 'RESPONSE_CHANGED' },
        },
      },
      {
        triggerType: 'HTTP_IF_CHANGE',
        templateId: 'response_changed_first_success_http_if_change_trigger',
        input: {
          type: 'HTTP_IF_CHANGE',
          scheduler: { datetime: now },
          url: 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=68760414-3289-418c-a0a7-cb04c1e84fb0',
          method: 'GET',
          headers: [],
          policy: {
            type: 'RESPONSE_CHANGED',
            firstSuccess: true,
            jsonPath: 'errcode',
          },
        },
      },
    ],
    actions: [],
  };

  // create automation
  const automationNodeSO = await templateFolderSO.createChildSimple(user, automationTemplate);
  const automationSO = await automationNodeSO.toResourceSO<AutomationSO>();
  const triggers = await automationSO.getTriggers();
  expect(triggers.length).toBe(4);
  // do test
  await triggers[0].doTest(user);
  await triggers[1].doTest(user);
  await triggers[2].doTest(user);
  await triggers[3].doTest(user);

  // FIRST_FAILED 首次运行请求失败会有匹配结果，再次运行请求失败无匹配结果
  const firstFailedTriggerEventContext: IHttpIfChangeTriggerEventContext = {
    runTime: now,
  };
  // 无法请求到的url，会触发错误
  const firstFailedTriggerMatchResult = await triggers[0].match(firstFailedTriggerEventContext);
  expect(firstFailedTriggerMatchResult).toBeDefined();
  const firstFailedTriggerMatchResult2 = await triggers[0].match(firstFailedTriggerEventContext);
  expect(firstFailedTriggerMatchResult2).toBeUndefined();

  // EVERY_FAILED 每次运行请求失败都有匹配结果
  const everyFailedTriggerEventContext: IHttpIfChangeTriggerEventContext = {
    runTime: now,
  };
  const everyFailedTriggerMatchResult = await triggers[1].match(everyFailedTriggerEventContext);
  expect(everyFailedTriggerMatchResult).toBeDefined();
  const everyFailedTriggerMatchResult2 = await triggers[1].match(everyFailedTriggerEventContext);
  expect(everyFailedTriggerMatchResult2).toBeDefined();

  // RESPONSE_CHANGED 首次运行请求成功，没有缓存上次的响应值，无法比较，所以默认不触发
  const responseChangedTriggerEventContext: IHttpIfChangeTriggerEventContext = {
    runTime: now,
  };
  // 首次请求成功，无法比较，不会触发
  const responseChangedTriggerMatchResult = await triggers[2].match(responseChangedTriggerEventContext);
  expect(responseChangedTriggerMatchResult).toBeUndefined();
  // 再次请求成功，响应值消息内的 hint 数据变化，会触发
  const responseChangedTriggerMatchResult2 = await triggers[2].match(responseChangedTriggerEventContext);
  expect(responseChangedTriggerMatchResult2).toBeDefined();

  // RESPONSE_CHANGED 首次运行请求成功，且设置了 firstSuccess = true，会有匹配结果
  const responseChangedFirstSuccessTriggerEventContext: IHttpIfChangeTriggerEventContext = {
    runTime: now,
  };
  // firstSuccess = true，会触发
  const responseChangedFirstSuccessTriggerMatchResult = await triggers[3].match(
    responseChangedFirstSuccessTriggerEventContext,
  );
  expect(responseChangedFirstSuccessTriggerMatchResult).toBeDefined();
  // errcode 不变，不会触发
  const responseChangedFirstSuccessTriggerMatchResult2 = await triggers[3].match(
    responseChangedFirstSuccessTriggerEventContext,
  );
  expect(responseChangedFirstSuccessTriggerMatchResult2).toBeUndefined();
});

test('Webhook Received Trigger', async () => {
  // create user
  const { user, rootFolder } = await MockContext.initUserContext();
  const templateFolderSO = await MockContext.initEmptyTemplateNodeFolder(user.id, rootFolder);

  // create automation
  const automationTemplate: Automation = {
    templateId: 'webhook_received_trigger_bot',
    resourceType: 'AUTOMATION',
    name: 'Automation for Webhook Received Trigger Test',
    status: 'ACTIVE',
    triggers: [
      {
        triggerType: 'WEBHOOK_RECEIVED',
        templateId: 'webhook_received_trigger',
        input: {
          type: 'WEBHOOK_RECEIVED',
        },
      },
    ],
    actions: [],
  };
  const automationNodeSO = await templateFolderSO.createChildSimple(user, automationTemplate);
  const automationSO = await automationNodeSO.toResourceSO<AutomationSO>();
  const triggers = await automationSO.getTriggers();
  expect(triggers.length).toBe(1);
  // do test
  const testResult = await triggers[0].doTest(user);
  expect(testResult.success).toBe(false);
  expect(testResult.error).toBe('Please send a sample request to the webhook address to run an automation.');

  // start automation run
  const body = {
    name: 'Tom',
    age: 18,
    country: 'America',
  };
  await triggers[0].run({
    body,
  });
  // 获取运行历史，确认触发器成功匹配运行了
  const { pagination, list } = await automationSO.getRunHistories();
  expect(pagination.total).toBe(1);
  const runHistoryDetail = list[0].toDetailVO();
  expect(runHistoryDetail.triggers.length).toBe(1);
  expect(runHistoryDetail.triggers[0].templateId).toBe('webhook_received_trigger');

  // continue do test
  const testResult2 = await triggers[0].doTest(user);
  expect(testResult2.success).toBe(true);
  expect(testResult2.output).toBeDefined();
  expect((testResult2.output as WebhookReceivedTriggerOutput).body).toStrictEqual(body); // 从运行历史中获取的数据
});

describe.skip('Inbound Email Trigger', async () => {
  // create user
  const { user, space, rootFolder } = await MockContext.initUserContext();

  const imap = {
    host: 'imap.example.com',
    username: '<EMAIL>',
    password: 'pass',
    port: 993,
    tls: true,
  };

  // create integration
  const integration = await space.createIntegration(user.id, {
    type: 'IMAP_EMAIL_ACCOUNT',
    name: 'IMAP Email Account',
    ...imap,
  });

  // create automation
  const automationNode = await rootFolder.createChildSimple(user, {
    name: 'Inbound Email Trigger Test Automation',
    resourceType: 'AUTOMATION',
    triggers: [
      {
        triggerType: 'INBOUND_EMAIL',
        templateId: 'inbound_email_trigger_with_empty_integration',
        input: {
          type: 'INBOUND_EMAIL',
          integrationId: '',
        },
      },
      {
        triggerType: 'INBOUND_EMAIL',
        templateId: 'inbound_email_trigger_with_invalid_mailbox',
        input: {
          type: 'INBOUND_EMAIL',
          integrationId: integration.id,
          mailboxName: 'invalid_mailbox',
        },
      },
      {
        triggerType: 'INBOUND_EMAIL',
        templateId: 'inbound_email_trigger_by_imap_integration',
        input: {
          type: 'INBOUND_EMAIL',
          integrationId: integration.id,
          downloadAttachments: true,
          // mailboxName: '其他文件夹/github',
        },
      },
      {
        triggerType: 'INBOUND_EMAIL',
        templateId: 'inbound_email_trigger_with_search_criteria_and_mailbox',
        input: {
          type: 'IMAP_INTEGRATION',
          integrationId: integration.id,
          // searchCriteria: '[["HEADER", "SUBJECT", "test"]]',
          // 单引号原来不符合json格式（parse会报错），这里是测试自动转换
          searchCriteria: "[['HEADER', 'SUBJECT', 'test']]",
          mailboxName: '其他文件夹/自建信箱文件夹名称',
        },
      },
      {
        triggerType: 'INBOUND_EMAIL',
        templateId: 'inbound_email_trigger_by_imap_with_empty_input',
        input: {
          type: 'IMAP',
          imap: {
            host: 'imap.example.com',
            username: '',
            password: '',
            port: 993,
            tls: true,
          },
        },
      },
      {
        triggerType: 'INBOUND_EMAIL',
        templateId: 'inbound_email_trigger_by_imap',
        input: {
          type: 'IMAP',
          imap,
        },
      },
    ],
    status: 'ACTIVE',
  });
  const automation = await automationNode.toResourceSO<AutomationSO>();
  const triggers = await automation.getTriggers();
  expect(triggers.length).toBe(6);
  // 第一个和第五个触发器，输入存在空值，不满足条件，不会创建调度器
  const schedulers = await automation.findSchedulers();
  expect(schedulers.length).toBe(4);

  // do test
  const firstTrigger = triggers[0];
  await expect(firstTrigger.doTest(user)).rejects.toThrowError();

  // 测试修改清空缓存
  test('Inbound Email Trigger State Clean', async () => {
    await firstTrigger.updateState({ lastMessageUid: 100 });
    await firstTrigger.update(user, {
      trigger: {
        triggerType: 'INBOUND_EMAIL',
        input: {
          type: 'INBOUND_EMAIL',
          integrationId: 'xxxx',
        },
      },
    });
    const updatedFirstTrigger = await TriggerSO.init(firstTrigger.id);
    const updatedFirstTriggerState = updatedFirstTrigger.state as InboundEmailTriggerState;
    expect(updatedFirstTriggerState.lastMessageUid).toBeUndefined();

    await updatedFirstTrigger.updateState({ lastMessageUid: 100 });
    await updatedFirstTrigger.update(user, {
      trigger: {
        triggerType: 'INBOUND_EMAIL',
        input: {
          type: 'IMAP',
          imap,
        },
      },
    });
    const updatedFirstTrigger2 = await TriggerSO.init(firstTrigger.id);
    const updatedFirstTriggerState2 = updatedFirstTrigger2.state as InboundEmailTriggerState;
    expect(updatedFirstTriggerState2.lastMessageUid).toBeUndefined();

    // imap 未修改 host/username，不会清空缓存
    await updatedFirstTrigger2.updateState({ lastMessageUid: 200 });
    await updatedFirstTrigger2.update(user, {
      trigger: {
        triggerType: 'INBOUND_EMAIL',
        input: {
          type: 'IMAP',
          imap: {
            ...imap,
            port: 143,
            tls: false,
          },
        },
      },
    });
    const updatedFirstTrigger3 = await TriggerSO.init(firstTrigger.id);
    const updatedFirstTriggerState3 = updatedFirstTrigger3.state as InboundEmailTriggerState;
    expect(updatedFirstTriggerState3.lastMessageUid).toBe(200);

    await updatedFirstTrigger2.update(user, {
      trigger: {
        triggerType: 'INBOUND_EMAIL',
        input: {
          type: 'IMAP',
          imap: {
            ...imap,
            username: 'N a g i s a',
          },
        },
      },
    });
    const updatedFirstTrigger4 = await TriggerSO.init(firstTrigger.id);
    const updatedFirstTriggerState4 = updatedFirstTrigger4.state as InboundEmailTriggerState;
    expect(updatedFirstTriggerState4.lastMessageUid).toBeUndefined();
  });

  test.skip('Imap Email Connect', async () => {
    await expect(triggers[0].doTest(user)).rejects.toThrowError();
    const invalidMailboxTestResult = await triggers[1].doTest(user);
    expect(invalidMailboxTestResult.success).toBe(false);
    expect(invalidMailboxTestResult.error).toBe('Folder not exist!');
    const trigger = triggers[2];
    const testResult = await trigger.doTest(user);
    expect(testResult.success).toBe(true);
    const { lastMessageUid } = trigger.state as InboundEmailTriggerState;
    // 没有邮件，测试结束
    if (!lastMessageUid) {
      return;
    }

    // 第一次触发自动化，新Trigger无上一次的缓存，只记录当前最新邮件消息uid，不会运行自动化
    await triggers[5].run();

    // 倒退缓存ID，让自动化运行
    await trigger.updateState({ lastMessageUid: lastMessageUid - 2 });
    // 触发自动化
    await trigger.run();
    await waitForMatchToBeMet(
      async () => {
        // 获取运行历史，确认触发器成功匹配运行了
        const { pagination, list } = await automation.getRunHistories();
        if (pagination.total !== 2) {
          return false;
        }
        expect(list.length).toBe(2);
        // 两次都是第三个触发器，而非第六个
        for (const runHistory of list) {
          const runHistoryDetail = runHistory.toDetailVO();
          expect(runHistoryDetail.triggers.length).toBe(1);
          expect(runHistoryDetail.triggers[0].templateId).toBe('inbound_email_trigger_by_imap_integration');
        }

        // 确认第六个触发器缓存ID已更新
        const sixthTrigger = await TriggerSO.init(triggers[5].id);
        if (!sixthTrigger.state) {
          return false;
        }
        return (sixthTrigger.state as InboundEmailTriggerState).lastMessageUid === lastMessageUid;
      },
      5000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
  });
});

test('Manual Trigger', async () => {
  // create user
  const { user, rootFolder } = await MockContext.initUserContext();

  // create automation
  const automationNode = await rootFolder.createChildSimple(user, {
    name: 'Manual Trigger Test Automation',
    resourceType: 'AUTOMATION',
    triggers: [
      {
        triggerType: 'MANUALLY',
        templateId: 'manual_trigger',
        input: {
          type: 'MANUALLY',
          fields: [
            {
              id: 'long_text_field_id',
              type: 'LONG_TEXT',
              templateId: 'long_text_field_template_id',
              name: 'Subject',
            },
            {
              type: 'SINGLE_SELECT',
              templateId: 'status_field_template_id',
              name: 'Status',
              property: {
                options: [
                  { id: 'opt_id_one', name: 'Open' },
                  { id: 'opt_id_two', name: 'Closed' },
                ],
              },
            },
          ],
          result: '<%= _triggers.manual_trigger.cells.long_text_field_id.value %>',
        },
      },
    ],
    status: 'ACTIVE',
  });
  const automation = await automationNode.toResourceSO<AutomationSO>();
  const triggers = await automation.getTriggers();
  expect(triggers.length).toBe(1);
  const trigger = triggers[0];
  // do test
  await trigger.doTest(user);

  // test fetchOutputMaybeFake method
  await trigger.fetchTriggerOutputMaybeFake();

  await trigger.run({
    user,
    cellData: {
      long_text_field_id: 'write something by field id',
      status_field_template_id: ['opt_id_one'],
      not_exist_field: 'not_exist_value',
    },
  });

  await waitForMatchToBeMet(
    async () => {
      const { pagination, list } = await automation.getRunHistories();
      if (pagination.total !== 1) {
        return false;
      }
      expect(list.length).toBe(1);
      const runHistoryDetail = list[0].toDetailVO();
      expect(runHistoryDetail.triggers.length).toBe(1);
      expect(runHistoryDetail.triggers[0].templateId).toBe('manual_trigger');

      const { cells, result } = runHistoryDetail.triggers[0].output as ManualTriggerOutput;
      expect(cells).toBeDefined();
      expect(result).toBe('write something by field id');
      return true;
    },
    5000,
    100,
  ).catch((error: Error) => {
    throw new Error(error.message);
  });
});

describe('update trigger sort', () => {
  let automation: AutomationSO;
  let user: UserSO;

  beforeEach(async () => {
    const { user: userSO, rootFolder } = await MockContext.initUserContext();
    const automationId = await rootFolder.createChildren(userSO, [
      {
        resourceType: 'AUTOMATION',
        name: 'automation',
        triggers: [
          {
            description: 'Trigger1',
            triggerType: 'MANUALLY',
          },
          {
            description: 'Trigger2',
            triggerType: 'MANUALLY',
          },
          {
            description: 'Trigger3',
            triggerType: 'MANUALLY',
          },
          {
            description: 'Trigger4',
            triggerType: 'MANUALLY',
          },
        ],
        actions: [
          {
            actionType: 'WEBHOOK',
            description: 'webhook1',
            input: {
              type: 'WEBHOOK',
              method: 'GET',
              headers: [],
              url: 'test',
            },
          },
        ],
      },
    ]);

    automation = await AutomationSO.init(automationId);
    user = userSO;
  });

  test('update trigger sort -- move to the first', async () => {
    const oldTriggers = await automation.getTriggers();
    // move the last trigger to the first
    const trigger = oldTriggers[3];
    assert(trigger, 'last trigger should be defined');
    await trigger.update(user, {
      preTriggerId: null,
    });
    const newTriggers = await automation.getTriggers(false);
    const firstTrigger = newTriggers[0];
    expect(firstTrigger.id).toBe(trigger.id);
    expect(firstTrigger.preTriggerId).toBe(null);
    expect(newTriggers[1].preTriggerId).toBe(firstTrigger.id);
    // 将倒数第二个移动到了最后一个
    expect(newTriggers[3].id).toBe(oldTriggers[2].id);
  });

  test('update trigger sort -- move on the middle', async () => {
    const oldTriggers = await automation.getTriggers();
    // move the third to the second
    const trigger = oldTriggers[2];
    assert(trigger, 'trigger should be defined');
    await trigger.update(user, {
      preTriggerId: oldTriggers[0].id,
    });
    const newTriggers = await automation.getTriggers(false);
    const firstTrigger = newTriggers[0];
    expect(firstTrigger.id).toBe(oldTriggers[0].id);
    expect(newTriggers[1].id).toBe(trigger.id);
    expect(newTriggers[1].preTriggerId).toBe(oldTriggers[0].id);
    // 调换了位置
    expect(newTriggers[2].id).toBe(oldTriggers[1].id);
    expect(newTriggers[2].preTriggerId).toBe(trigger.id);
    // 最后一个的位置的前置ID
    expect(newTriggers[3].preTriggerId).toBe(oldTriggers[1].id);
  });

  test('update trigger sort -- move to the last', async () => {
    const oldTriggers = await automation.getTriggers();
    // move the first to the last
    const action = oldTriggers[0];
    assert(action, 'last action should be defined');
    await action.update(user, {
      preTriggerId: oldTriggers[3].id,
    });
    const newTriggers = await automation.getTriggers(false);
    expect(newTriggers[0].id).toBe(oldTriggers[1].id);
    expect(newTriggers[1].preTriggerId).toBe(oldTriggers[1].id);
    // 调换了位置
    expect(newTriggers[3].id).toBe(oldTriggers[0].id);
    expect(newTriggers[3].preTriggerId).toBe(oldTriggers[3].id);
  });
});

describe('Test Trigger to Template', () => {
  test('DATETIME_FIELD_REACHED trigger to template', async () => {
    const trigger: Trigger = {
      templateId: 'datetime-reached-trigger',
      triggerType: 'DATETIME_FIELD_REACHED',
      input: {
        type: 'DATETIME_FIELD_REACHED',
        datetime: {
          type: 'TODAY',
          hour: 9,
          minute: 0,
        },
        fieldId: 'field_id',
        databaseId: 'database_id',
      },
    };
    const triggerHandler = TriggerHandlerManager.getTriggerHandler(trigger.triggerType);
    const triggerTemplate = triggerHandler.toTemplateBO(trigger, (id: string) => {
      if (id === 'field_id') {
        return 'field_template_id';
      }
      if (id === 'database_id') {
        return 'database_template_id';
      }
      return undefined;
    });
    expect(triggerTemplate).toStrictEqual({
      templateId: 'datetime-reached-trigger',
      triggerType: 'DATETIME_FIELD_REACHED',
      input: {
        type: 'DATETIME_FIELD_REACHED',
        datetime: {
          type: 'TODAY',
          hour: 9,
          minute: 0,
        },
        databaseId: undefined,
        fieldTemplateId: 'field_template_id',
        databaseTemplateId: 'database_template_id',
        fieldId: undefined,
      },
    });
  });

  test('FORM_SUBMITTED trigger to template', async () => {
    const trigger: Trigger = {
      templateId: 'form-submitted-trigger',
      triggerType: 'FORM_SUBMITTED',
      input: {
        type: 'FORM',
        formId: 'form_id',
      },
    };
    const triggerHandler = TriggerHandlerManager.getTriggerHandler(trigger.triggerType);
    const triggerTemplate = triggerHandler.toTemplateBO(trigger, (_id: string) => 'form_template_id');
    expect(triggerTemplate).toStrictEqual({
      templateId: 'form-submitted-trigger',
      triggerType: 'FORM_SUBMITTED',
      input: {
        type: 'FORM',
        formTemplateId: 'form_template_id',
        formId: undefined,
      },
    });
  });

  test('HTTP_IF_CHANGE trigger to template', async () => {
    const datetime = dayjs().toISOString();
    const trigger: Trigger = {
      templateId: 'http-if-change-trigger',
      triggerType: 'HTTP_IF_CHANGE',
      input: {
        type: 'HTTP_IF_CHANGE',
        scheduler: { datetime },
        url: 'https://aitable.ai/fusion/v1/datasheets/dstxxx/records',
        method: 'GET',
        headers: [{ key: 'test', value: 'test' }],
        policy: { type: 'EVERY_FAILED' },
      },
    };
    const triggerHandler = TriggerHandlerManager.getTriggerHandler(trigger.triggerType);
    const triggerTemplate = triggerHandler.toTemplateBO(trigger);
    // set header values to empty string
    expect(triggerTemplate).toStrictEqual({
      templateId: 'http-if-change-trigger',
      triggerType: 'HTTP_IF_CHANGE',
      input: {
        type: 'HTTP_IF_CHANGE',
        scheduler: { datetime },
        url: 'https://aitable.ai/fusion/v1/datasheets/dstxxx/records',
        method: 'GET',
        headers: [{ key: 'test', value: '' }],
        policy: { type: 'EVERY_FAILED' },
      },
    });
  });

  test('INBOUND_EMAIL integration/inbound trigger to template', async () => {
    const trigger: Trigger = {
      templateId: 'inbound-trigger',
      triggerType: 'INBOUND_EMAIL',
      input: {
        type: 'IMAP_INTEGRATION',
        integrationId: 'integration_id',
      },
    };
    const triggerHandler = TriggerHandlerManager.getTriggerHandler(trigger.triggerType);
    const triggerTemplate = triggerHandler.toTemplateBO(trigger);
    // set header values to empty string
    expect(triggerTemplate).toStrictEqual({
      templateId: 'inbound-trigger',
      triggerType: 'INBOUND_EMAIL',
      input: {
        type: 'IMAP_INTEGRATION',
        integrationId: '',
      },
    });
  });

  test('INBOUND_EMAIL imap trigger to template', async () => {
    const trigger: Trigger = {
      templateId: 'inbound-trigger',
      triggerType: 'INBOUND_EMAIL',
      input: {
        type: 'IMAP',
        imap: {
          host: 'test',
          port: 993,
          username: 'test',
          password: 'test',
          tls: true,
        },
      },
    };
    const triggerHandler = TriggerHandlerManager.getTriggerHandler(trigger.triggerType);
    const triggerTemplate = triggerHandler.toTemplateBO(trigger);
    // set header values to empty string
    expect(triggerTemplate).toStrictEqual({
      templateId: 'inbound-trigger',
      triggerType: 'INBOUND_EMAIL',
      input: {
        type: 'IMAP',
        imap: {
          host: '',
          port: 993,
          username: '',
          password: '',
        },
      },
    });
  });

  test('MANUALLY imap trigger to template', async () => {
    const trigger: Trigger = {
      templateId: 'manually-trigger',
      triggerType: 'MANUALLY',
      input: {
        type: 'MANUALLY',
        fields: [
          {
            id: 'field_id',
            type: 'LONG_TEXT',
            templateId: 'field_template_id',
            name: 'Subject',
          },
        ],
        result: '<%= _triggers.manual_trigger.cells.long_text_field_id.value %>',
      },
    };
    const triggerHandler = TriggerHandlerManager.getTriggerHandler(trigger.triggerType);
    const triggerTemplate = triggerHandler.toTemplateBO(trigger);
    // set header values to empty string
    expect(triggerTemplate).toStrictEqual({
      templateId: 'manually-trigger',
      triggerType: 'MANUALLY',
      input: {
        type: 'MANUALLY',
        fields: [
          {
            id: 'field_id',
            type: 'LONG_TEXT',
            templateId: 'field_template_id',
            name: 'Subject',
          },
        ],
        result: '<%= _triggers.manual_trigger.cells.long_text_field_id.value %>',
      },
    });
  });

  test('RECORD_CREATED imap trigger to template', async () => {
    const trigger: Trigger = {
      templateId: 'record-created-trigger',
      triggerType: 'RECORD_CREATED',
      input: {
        type: 'DATABASE',
        databaseId: 'database_id',
      },
    };
    const triggerHandler = TriggerHandlerManager.getTriggerHandler(trigger.triggerType);
    const triggerTemplate = triggerHandler.toTemplateBO(trigger, (id: string) => {
      if (id === 'database_id') {
        return 'database_template_id';
      }
      return undefined;
    });
    // set header values to empty string
    expect(triggerTemplate).toStrictEqual({
      templateId: 'record-created-trigger',
      triggerType: 'RECORD_CREATED',
      input: {
        type: 'DATABASE',
        databaseId: undefined,
        databaseTemplateId: 'database_template_id',
      },
    });
  });

  test('RECORD_MATCH imap trigger to template', async () => {
    const trigger: Trigger = {
      templateId: 'record-match-trigger',
      triggerType: 'RECORD_MATCH',
      input: {
        type: 'DATABASE_WITH_FILTER',
        databaseId: 'database_id',
        filters: {
          conditions: [],
          conds: [
            {
              fieldId: 'field_id',
              fieldType: 'SINGLE_TEXT',
              clause: {
                operator: 'Is',
                value: 'value',
              },
            },
          ],
          conjunction: 'And',
        },
      },
    };
    const triggerHandler = TriggerHandlerManager.getTriggerHandler(trigger.triggerType);
    const triggerTemplate = triggerHandler.toTemplateBO(trigger, (id: string) => {
      if (id === 'database_id') {
        return 'database_template_id';
      }
      if (id === 'field_id') {
        return 'field_template_id';
      }
      return undefined;
    });
    // set header values to empty string
    const expected: RecordMatchTrigger = {
      templateId: 'record-match-trigger',
      triggerType: 'RECORD_MATCH',
      input: {
        type: 'DATABASE_WITH_FILTER',
        databaseTemplateId: 'database_template_id',
        databaseId: undefined,
        filters: {
          conditions: [],
          conds: [
            {
              fieldTemplateId: 'field_template_id',
              fieldId: undefined,
              fieldType: 'SINGLE_TEXT',
              clause: {
                operator: 'Is',
                value: 'value',
              },
            },
          ],
          conjunction: 'And',
        },
      },
    };
    expect(triggerTemplate).toStrictEqual(expected);
  });

  test('SCHEDULER imap trigger to template', async () => {
    const trigger: Trigger = {
      templateId: 'scheduler-trigger',
      triggerType: 'SCHEDULER',
      input: {
        type: 'SCHEDULER',
        scheduler: {
          timezone: 'AUTO',
          datetime: '2024-01-01 10:00:00',
          repeat: {
            every: {
              type: 'DAY',
              interval: 1,
            },
          },
        },
      },
    };
    const triggerHandler = TriggerHandlerManager.getTriggerHandler(trigger.triggerType);
    const triggerTemplate = triggerHandler.toTemplateBO(trigger);
    // set header values to empty string
    expect(triggerTemplate).toStrictEqual({
      templateId: 'scheduler-trigger',
      triggerType: 'SCHEDULER',
      input: {
        type: 'SCHEDULER',
        scheduler: {
          timezone: 'AUTO',
          datetime: '2024-01-01 10:00:00',
          repeat: {
            every: {
              type: 'DAY',
              interval: 1,
            },
          },
        },
      },
    });
  });

  test('WEBHOOK_RECEIVED imap trigger to template', async () => {
    const trigger: Trigger = {
      templateId: 'scheduler-trigger',
      triggerType: 'WEBHOOK_RECEIVED',
      input: {
        type: 'WEBHOOK_RECEIVED',
      },
    };
    const triggerHandler = TriggerHandlerManager.getTriggerHandler(trigger.triggerType);
    const triggerTemplate = triggerHandler.toTemplateBO(trigger);
    // set header values to empty string
    expect(triggerTemplate).toStrictEqual({
      templateId: 'scheduler-trigger',
      triggerType: 'WEBHOOK_RECEIVED',
      input: {
        type: 'WEBHOOK_RECEIVED',
      },
    });
  });
});
