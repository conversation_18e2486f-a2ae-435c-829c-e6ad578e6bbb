import { SpaceSO } from '@bika/domains/space/server/space-so';
import { PrismaTransactionFn, UnitType, db } from '@bika/server-orm';
import { MemberCellValue } from '@bika/types/database/vo';
import { OpenAPIMemberCellValue } from '@bika/types/openapi/vo';
import { LocaleType } from '@bika/types/system';
import { UnitRenderOpts, UnitVO } from '@bika/types/unit/vo';
import { UnitModel } from './types';

export abstract class UnitSO {
  protected _unitModel: UnitModel;

  protected constructor(unitModel: UnitModel) {
    this._unitModel = unitModel;
  }

  get id(): string {
    return this._unitModel.id;
  }

  get type(): UnitType {
    return this._unitModel.type;
  }

  get spaceId(): string {
    return this._unitModel.spaceId;
  }

  get isMember(): boolean {
    return this.type === UnitType.MEMBER;
  }

  get isTeam(): boolean {
    return this.type === UnitType.TEAM;
  }

  get isRole(): boolean {
    return this.type === UnitType.ROLE;
  }

  /**
   * 获取所在的空间站
   * @returns space so
   */
  async getSpace() {
    return SpaceSO.init(this.spaceId);
  }

  // abstract get name(): iString;

  abstract getName(_locale?: LocaleType): string;

  async delete(): Promise<void> {
    await this.beforeDelete();

    await db.prisma.$transaction(async (transaction) => {
      const deleteInternal = await this.deleteInternal();
      await deleteInternal(transaction);
      await transaction.permission.deleteMany({ where: { unitId: this.id } });
      await transaction.unit.delete({ where: { id: this.id } });
    });

    await this.afterDelete();
  }

  protected beforeDelete(): Promise<void> | void {
    // do nothing
  }

  protected abstract deleteInternal(): Promise<PrismaTransactionFn>;

  protected afterDelete(): Promise<void> | void {
    // do nothing
  }

  abstract toVO(_opts?: UnitRenderOpts): Promise<UnitVO> | UnitVO;

  /**
   * 转换为数据表的单元格渲染值
   */
  abstract toCellValue(_opts?: UnitRenderOpts): Promise<MemberCellValue>;

  /**
   * 输出 OpenAPI 格式的单元格值
   * @param _opts 渲染选项
   */
  abstract toOpenAPICellValue(_opts?: UnitRenderOpts): OpenAPIMemberCellValue;
}
