import { PrismaPromise, Prisma, db, UnitType, PrismaTransactionFn } from '@bika/server-orm';
import { MemberCellValue } from '@bika/types/database/vo';
import { OpenAPIMemberCellValue } from '@bika/types/openapi/vo';
import { Pagination, PaginationInfo, PaginationSchema } from '@bika/types/shared';
import { LocaleType, iString, iStringParse } from '@bika/types/system';
import { UnitRole } from '@bika/types/unit/bo';
import { RolePermissionGroup } from '@bika/types/unit/type';
import { RoleVO, RoleSettingVO, RolePermission, UnitRenderOpts } from '@bika/types/unit/vo';
import { MemberSO } from './member-so';
import { TeamSO } from './team-so';
import { UnitOnRolesModel, RoleIncludeUnitOnlyModel, roleIncludeUnitOnly, RoleModel } from './types';
import { UnitFactory } from './unit-factory';
import { UnitSO } from './unit-so';

export class RoleSO extends UnitSO {
  private _model: RoleIncludeUnitOnlyModel;

  private constructor(model: RoleIncludeUnitOnlyModel) {
    super(model.unit);
    this._model = model;
  }

  private get model() {
    return this._model;
  }

  get templateId() {
    return this.model.templateId;
  }

  // override get name(): iString {
  //   return this.model.name as iString;
  // }

  override getName(locale: LocaleType = 'en'): string {
    return iStringParse(this.model.name as iString, locale);
  }

  get isAdminRole(): boolean {
    return this.model.manageSpace;
  }

  get setting(): RoleSettingVO {
    return this.model.setting as Prisma.JsonObject as RoleSettingVO;
  }

  get memberCount(): number {
    return this.model._count?.units ?? 0;
  }

  get rolePermissions(): RolePermission[] {
    return this.model.manageSpace && this.setting && this.setting.permissions ? this.setting.permissions : [];
  }

  get rolePermissionActionCollections(): string[] {
    return this.rolePermissions.map((permission) => RolePermissionGroup[permission]).flat();
  }

  hasPermission(permission: RolePermission): boolean {
    if (!this.isAdminRole) {
      return false;
    }
    return this.rolePermissions.includes(permission);
  }

  async toVO(opts?: UnitRenderOpts): Promise<RoleVO> {
    return {
      id: this.id,
      templateId: this.model.templateId,
      name: this.getName(opts?.locale),
      type: 'Role',
      manageSpace: this.model.manageSpace,
      permissions: this.setting?.permissions ?? [],
      memberCount: this.memberCount,
      createdAt: this.model.createdAt.toISOString(),
    };
  }

  async toCellValue(opts?: UnitRenderOpts): Promise<MemberCellValue> {
    return {
      id: this.id,
      name: this.getName(opts?.locale),
    };
  }

  toOpenAPICellValue(opts?: UnitRenderOpts): OpenAPIMemberCellValue {
    return {
      id: this.id,
      type: 'Role',
      name: this.getName(opts?.locale),
    };
  }

  static initWithModel(rolePO: RoleIncludeUnitOnlyModel): RoleSO {
    return new RoleSO(rolePO);
  }

  static async init(roleId: string): Promise<RoleSO> {
    const rolePO = await this.findByRoleId(roleId);
    if (!rolePO) {
      throw new Error('Role not found');
    }

    return this.initWithModel(rolePO);
  }

  static async findByIds(spaceId: string, roleIds: string[]): Promise<RoleSO[]> {
    const rolePOs = await db.prisma.unitRole.findMany({
      where: {
        id: { in: roleIds },
        unit: {
          spaceId,
        },
      },
      include: roleIncludeUnitOnly,
    });
    return rolePOs.map((rolePO) => this.initWithModel(rolePO));
  }

  static async findPOsByIds(roleIds: string[]): Promise<RoleModel[]> {
    const rolePOs = await db.prisma.unitRole.findMany({
      where: {
        id: { in: roleIds },
      },
    });
    return rolePOs;
  }

  /**
   * 获取角色下的所有Unit
   * @returns unit 列表
   */
  async getUnits(): Promise<UnitSO[]> {
    const unitOnRoles = await db.prisma.unitOnRoles.findMany({
      where: {
        roleId: this.id,
      },
      include: {
        unit: true,
      },
    });
    return Promise.all(
      unitOnRoles.map(async (unitOnRole) => UnitFactory.determineUnit(unitOnRole.unit.type, unitOnRole.unit.id)),
    );
  }

  private async getTeamsAndMembers(): Promise<{ teams: TeamSO[]; members: MemberSO[] }> {
    const units = await this.getUnits();
    const teams = units.filter((unit) => unit instanceof TeamSO) as TeamSO[];
    const members = units.filter((unit) => unit instanceof MemberSO) as MemberSO[];
    return { teams, members };
  }

  async getMemberIds(): Promise<string[]> {
    const { teams, members } = await this.getTeamsAndMembers();
    const memberIdsOnTeams = await Promise.all(teams.map((team) => team.getMemberIds(true)));
    const mergeMemberIds = [...members.map((m) => m.id), ...memberIdsOnTeams.flat()];
    // 过滤重复的成员, 有可能成员在多个小组中
    return mergeMemberIds.filter((memberId, index, self) => self.findIndex((m) => m === memberId) === index);
  }

  async getMembers(): Promise<MemberSO[]> {
    const { teams, members } = await this.getTeamsAndMembers();
    const membersOnTeams = await Promise.all(teams.map((team) => team.getMembers(true)));
    const mergeMembers = [...members, ...membersOnTeams.flat()];
    // 过滤重复的成员, 有可能成员在多个小组中
    return mergeMembers.filter((member, index, self) => self.findIndex((m) => m.id === member.id) === index);
  }

  async refresh(): Promise<void> {
    const po = await RoleSO.findByRoleId(this.id);
    if (!po) {
      throw new Error('Role not found, reload failed');
    }
    this._model = po;
  }

  /**
   * 将某个成员、部门、甚至其它Role，拉入Role
   *
   * @param userId user id
   * @param unitId unit id
   */
  async addUnit(userId: string, unitId: string): Promise<void> {
    const po = await db.prisma.unitOnRoles.findFirst({
      where: {
        roleId: this.id,
        unitId,
      },
    });
    if (po) {
      throw new Error('User already in this role');
    }

    await this.addUnitOperation({ unitId, createdBy: userId });
  }

  addUnitOperation(
    data: Pick<Prisma.UnitOnRolesCreateManyInput, 'unitId' | 'createdBy'>,
  ): PrismaPromise<UnitOnRolesModel> {
    return db.prisma.unitOnRoles.create({
      data: {
        roleId: this.id,
        ...data,
      },
    });
  }

  async addUnits(userId: string, unitIds: string[]): Promise<void> {
    const data: Prisma.UnitOnRolesCreateManyInput[] = unitIds.map((unitId) => ({
      roleId: this.id,
      unitId,
      createdBy: userId,
    }));
    await db.prisma.unitOnRoles.createMany({ data });
  }

  /**
   * 移除 Role 中的 Unit（成员/小组/角色）
   *
   * @param unitId 同空间站的某个成员/小组
   */
  async deleteUnit(unitId: string) {
    await this.deleteUnitOperation(unitId);
  }

  deleteUnitOperation(unitId: string) {
    return db.prisma.unitOnRoles.delete({
      where: {
        roleId_unitId: {
          roleId: this.id,
          unitId,
        },
      },
    });
  }

  protected async deleteInternal(): Promise<PrismaTransactionFn> {
    return async (transaction) => {
      await transaction.unitOnRoles.deleteMany({ where: { roleId: this.id } });
      await transaction.unitRole.delete({ where: { id: this.id } });
    };
  }

  async update(userId: string, data: { name?: string; manageSpace?: boolean; setting?: RoleSettingVO }): Promise<void> {
    const { name, manageSpace, setting } = data;
    // 只有值存在才更新
    const updateData: Prisma.UnitRoleUpdateInput = { updatedBy: userId };
    if (name) {
      updateData.name = name;
    }
    if (manageSpace !== undefined) {
      updateData.manageSpace = manageSpace;
    }
    if (setting && (this.isAdminRole || manageSpace === true)) {
      updateData.setting = setting;
    }
    this._model = await this.updateOperation(updateData);
  }

  updateOperation(
    data: Pick<Prisma.UnitRoleUpdateInput, 'name' | 'updatedBy' | 'setting'>,
  ): PrismaPromise<RoleIncludeUnitOnlyModel> {
    return db.prisma.unitRole.update({
      where: {
        id: this.id,
      },
      data,
      include: roleIncludeUnitOnly,
    });
  }

  static async createRole(
    userId: string,
    spaceId: string,
    createParam: {
      id?: string;
      name: iString;
      manageSpace?: boolean;
      setting?: RoleSettingVO;
    },
  ): Promise<RoleSO> {
    const { id, name, manageSpace, setting } = createParam;
    const rolePO = await this.createRoleOperation(spaceId, {
      id,
      name,
      createdBy: userId,
      updatedBy: userId,
      manageSpace,
      setting,
    });
    return this.init(rolePO.id);
  }

  /**
   * New a Unit Role
   *
   * @param userId who create this role
   * @param spaceId which space this role belong to
   * @param roleBO role template
   * @returns RoleSO
   */
  static async create(userId: string, spaceId: string, roleBO: UnitRole): Promise<RoleSO> {
    const rolePO = await this.createRoleOperationWithBO(userId, spaceId, roleBO);
    return this.init(rolePO.id);
  }

  /**
   * find a special role with template Id
   *
   * @param spaceId space id
   * @param templateId template id
   * @returns
   */
  static async findByTemplateId(spaceId: string, templateId: string): Promise<RoleSO | null> {
    const role = await this.findByRoleTemplateId(spaceId, templateId);
    return role && new RoleSO(role);
  }

  /**
   * query roles of space with pagination
   * @param q query selection option
   * @param pagination pagination option
   * @returns role pagination
   */
  static async find(
    q: { spaceId: string; name?: string; manageSpace?: boolean },
    pagination?: Pagination,
  ): Promise<{ pagination: PaginationInfo; list: RoleSO[] }> {
    const { spaceId, name, manageSpace } = q;
    const { pageNo, pageSize } = PaginationSchema.parse(pagination ?? {});
    let where: Prisma.UnitRoleWhereInput = { unit: { spaceId } };
    // 过滤角色名称
    if (name) {
      where = { ...where, name: { string_contains: name } };
    }
    // 过滤非管理员
    if (manageSpace !== undefined) {
      where = { ...where, manageSpace };
    }
    // 执行分页查询
    const [rows, total] = await Promise.all([
      db.prisma.unitRole.findMany({
        where,
        skip: pageSize * (pageNo - 1),
        take: pageSize,
        include: roleIncludeUnitOnly,
      }),
      db.prisma.unitRole.count({ where }),
    ]);
    return {
      pagination: { pageNo, pageSize, total },
      list: rows.map((role) => this.initWithModel(role)),
    };
    // const data = rows.map((role) => this.initWithModel(role).toVO());
    // return new Pagination<RoleVO>(pageNo, pageSize, total, data);
  }

  static getAdminRoles(spaceId: string): PrismaPromise<RoleIncludeUnitOnlyModel[]> {
    return db.prisma.unitRole.findMany({
      where: {
        unit: {
          spaceId,
        },
        manageSpace: true,
      },
      include: roleIncludeUnitOnly,
    });
  }

  static findByRoleTemplateId(spaceId: string, templateId: string): PrismaPromise<RoleIncludeUnitOnlyModel | null> {
    return db.prisma.unitRole.findFirst({
      where: {
        templateId,
        unit: {
          spaceId,
        },
      },
      include: roleIncludeUnitOnly,
    });
  }

  static findByRoleId(roleId: string): PrismaPromise<RoleIncludeUnitOnlyModel | null> {
    return db.prisma.unitRole.findUnique({
      where: {
        id: roleId,
      },
      include: roleIncludeUnitOnly,
    });
  }

  /**
   * 创建角色的数据库操作
   * @param userId user id
   * @param spaceId space id
   * @param roleBO role template
   * @param canManageSpace whether can manage space
   */
  static createRoleOperationWithBO(
    userId: string,
    spaceId: string,
    roleBO: UnitRole,
    canManageSpace: boolean = false,
  ): PrismaPromise<RoleModel> {
    return db.prisma.unitRole.create({
      data: {
        name: roleBO.name!,
        templateId: roleBO.templateId,
        createdBy: userId,
        updatedBy: userId,
        manageSpace: canManageSpace,
        unit: {
          create: {
            id: roleBO.id ?? UnitFactory.generateUnitId(UnitType.ROLE),
            type: UnitType.ROLE,
            spaceId,
            createdBy: userId,
            updatedBy: userId,
          },
        },
      },
    });
  }

  static createRoleOperation(
    spaceId: string,
    data: Pick<
      Prisma.UnitRoleCreateInput,
      'name' | 'templateId' | 'createdBy' | 'updatedBy' | 'manageSpace' | 'setting'
    > & { id?: string },
  ): PrismaPromise<RoleModel> {
    return db.prisma.unitRole.create({
      data: {
        name: data.name,
        templateId: data.templateId,
        createdBy: data.createdBy,
        updatedBy: data.createdBy,
        manageSpace: data.manageSpace,
        setting: data.setting,
        unit: {
          create: {
            id: data.id ?? UnitFactory.generateUnitId(UnitType.ROLE),
            type: UnitType.ROLE,
            spaceId,
            createdBy: data.createdBy,
            updatedBy: data.createdBy,
          },
        },
      },
    });
  }
}
