'use client';

import { useDebounce } from 'ahooks';
import classNames from 'classnames';
import _ from 'lodash';
import type React from 'react';
import { useMemo, useState, useEffect, useRef, useCallback, Fragment } from 'react';
import { useApiCaller } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import { useSpaceContextForce, useSpaceRouter } from '@bika/types/space/context';
import type { UnitSearch } from '@bika/types/unit/dto';
import type { TeamSubListPageVO, MemberVO, TeamVO, TeamSubListPageItemVO, UnitVO } from '@bika/types/unit/vo';
import { BMenu, type IBMenuItem } from '@bika/ui/bmenu';
import { Breadcrumbs } from '@bika/ui/breadcrumbs';
import { Button } from '@bika/ui/button';
import { AvatarImg, AvatarSize } from '@bika/ui/components/avatar/index';
import { useSelectMemberUnitModal } from '@bika/ui/database/filter/use-select-member-unit-modal';
import { Checkbox, Input } from '@bika/ui/forms';
import AddCircleOutlined from '@bika/ui/icons/components/add_circle_outlined';
import CheckboxOutlined from '@bika/ui/icons/components/checkbox_outlined';
import ChevronLeftOutlined from '@bika/ui/icons/components/chevron_left_outlined';
import DeleteOutlined from '@bika/ui/icons/components/delete_outlined';
import EditOutlined from '@bika/ui/icons/components/edit_outlined';
import FolderAddOutlined from '@bika/ui/icons/components/folder_add_outlined';
import SearchOutlined from '@bika/ui/icons/components/search_outlined';
import UserAddOutlined from '@bika/ui/icons/components/user_add_outlined';
import { Box, Divider, Stack } from '@bika/ui/layouts';
import { LoadingMask } from '@bika/ui/loading-mask';
import { Modal } from '@bika/ui/modal';
import { useSnackBar } from '@bika/ui/snackbar/snackbar-component';
import { Typography } from '@bika/ui/texts';
import { MemberSelectModal } from '@bika/ui/unit/types-form/member-select-modal';
import { NavHeader } from '@bika/ui/web-layout';
import { BlankPage } from './blank-page';
import { MemberItem } from './member-item';
import { TeamItem } from './team-item';
import { TeamOperateModal } from './team-operate-modal';
import { UnitMemberManager } from './unit-member-manager';
import styles from './unit-settings-style.module.css';

const { slice, union, compact } = _;

const PAGE_SIZE = 50;

export function UnitSettingsView({ spaceId, isFromSidebar = false }: { spaceId: string; isFromSidebar?: boolean }) {
  const locale = useLocale();
  const { t } = locale;
  const spaceContext = useSpaceContextForce();
  const spaceRouter = useSpaceRouter();
  const { rootTeam, permission, data: space } = spaceContext;
  const { trpcQuery, trpc } = useApiCaller();
  const utils = trpcQuery.useUtils();
  const inputRef = useRef<HTMLInputElement | null>(null);
  const { toast } = useSnackBar();

  const { useParams } = spaceRouter;
  const params = useParams<{ teamId?: string; memberId?: string }>();
  const [selected, setSelected] = useState<string[]>([]);
  const [searchInput, setSearchInput] = useState<string>('');
  const [searchResult, setSearchResult] = useState<UnitVO[]>([]);
  const [selectMode, setSelectMode] = useState<'tem' | 'meb'>();
  const [loading, setLoading] = useState(false);
  const [showTeamOperateModal, setShowTeamOperateModal] = useState<null | 'create' | 'rename' | 'delete'>(null);
  const [activeTeams, setActiveTeams] = useState<TeamVO[]>([rootTeam]); // [teamId, teamId, ...

  const [teamId, setTeamId] = useState<string>(rootTeam.id);
  const [pageNo, setPageNo] = useState(1);
  const [teamsAndMembers, setTeamsAndMembers] = useState<TeamSubListPageItemVO[]>([]);
  const [isMore, setIsMore] = useState(true);
  const [memberItem, setMemberItem] = useState<MemberVO | null>(null);

  const title = useMemo(() => {
    if (memberItem) {
      return t.settings.member.edit_member;
    }
    return t.space.members_and_teams;
  }, [memberItem, t]);

  const fetchData = useCallback(async () => {
    setLoading(true);
    utils.team.subList.invalidate();
    const data: TeamSubListPageVO = await trpc.team.subList.query({ spaceId, teamId, pageSize: PAGE_SIZE, pageNo });
    setLoading(false);
    setIsMore(data.data.length >= PAGE_SIZE);
    setTeamsAndMembers((prev) => {
      if (pageNo === 1) {
        return data.data;
      }
      return union(prev || [], data.data);
    });
  }, [spaceId, teamId, pageNo]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    setActiveTeams((prev) => {
      if (prev.length === 1 && prev[0].id === rootTeam.id) {
        return [rootTeam];
      }
      return prev.map((team) => (team.id === rootTeam.id ? rootTeam : team));
    });
  }, [rootTeam]);

  const teams = useMemo(
    () => (teamsAndMembers.filter((item) => item.type === 'Team') || []) as TeamVO[],
    [teamsAndMembers],
  );
  const members = useMemo(() => teamsAndMembers.filter((item) => item.type === 'Member') || [], [teamsAndMembers]);
  const users = useMemo(
    () => teamsAndMembers.filter((item) => item.type === 'Member' && item.relationType !== 'AI') || [],
    [teamsAndMembers],
  );

  const isRoot = activeTeams.length === 1;

  useEffect(() => {
    setSelected([]);
  }, [selectMode]);

  const keyword = useDebounce(searchInput.trim(), { wait: 500 });

  const search = async () => {
    if (selectMode) {
      setSelectMode(undefined);
    }

    if (!keyword) {
      setSearchResult([]);
      return;
    }

    setLoading(true);

    const { data } = await trpc.unit.list.query({ spaceId, name: keyword, isGuest: false });

    setLoading(false);
    const searchRlt = data.filter((item) => item.type !== 'Role');
    setSearchResult(searchRlt);
  };

  useEffect(() => {
    search();
  }, [keyword]);

  const retrieveTeamInfo = async (teamInfo: TeamVO, backTeam = false) => {
    setSelectMode(undefined);

    setTeamsAndMembers([]);
    if (pageNo !== 1) {
      setPageNo(1);
    }
    if (teamInfo.id !== teamId) {
      setTeamId(teamInfo.id);
    }
    if (pageNo === 1 && teamInfo.id === teamId) {
      fetchData();
    }

    if (backTeam) {
      // 从面包屑导航上直接点击
      setActiveTeams((pre) => {
        const clickedIndex = pre.findIndex((team) => team.id === teamInfo.id);
        return slice(pre, 0, clickedIndex + 1);
      });
    } else {
      setActiveTeams((pre) => {
        const newActiveTeams = [...new Set([...pre, teamInfo])];
        return newActiveTeams;
      });
    }
  };

  // Remove member from team
  const removeMember = async (memberId: string) => {
    await trpc.team.removeMembers.mutate({
      spaceId,
      id: activeTeams[activeTeams.length - 1].id,
      memberIds: [memberId],
    });
    toast(t.team.msg_remove_member_from_team_success, { variant: 'success' });
    const lastTeam = activeTeams[activeTeams.length - 1];
    retrieveTeamInfo(lastTeam);
    fetchData();
  };

  // Remove member from space
  const deleteMember = async (memberId: string) => {
    await trpc.member.kickMembers.mutate({ spaceId, memberIds: [memberId] });
    toast(t.team.msg_remove_member_from_space_success, { variant: 'success' });
    const lastTeam = activeTeams[activeTeams.length - 1];
    if (isFromSidebar) {
      spaceRouter.push(`/space/${spaceId}/team/${lastTeam.id}`);
    }
    retrieveTeamInfo(lastTeam);
  };

  // Remove multiple members from team or space
  const onClickKickMember = async () => {
    if (!selected.length) return;

    if (activeTeams.length > 1) {
      // Remove members from team
      await trpc.team.removeMembers.mutate({
        spaceId,
        id: activeTeams[activeTeams.length - 1].id,
        memberIds: selected,
      });
      toast(t.team.msg_remove_member_from_team_success, { variant: 'success' });
      setSelected([]);
      const lastTeam = activeTeams[activeTeams.length - 1];
      retrieveTeamInfo(lastTeam);
      return;
    }

    await trpc.member.kickMembers.mutate({ spaceId, memberIds: selected });
    if (inputRef.current?.value) {
      setSearchResult([]);
      inputRef.current.value = '';
      const lastTeam = activeTeams[activeTeams.length - 1];
      retrieveTeamInfo(lastTeam);
    } else {
      fetchData();
    }
    toast(t.team.msg_remove_member_from_space_success, { variant: 'success' });
  };

  const backToTeam = () => {
    if (memberItem) {
      setMemberItem(null);
      return;
    }

    const previousTeam = activeTeams[activeTeams.length - 2];
    if (!previousTeam) {
      return;
    }
    retrieveTeamInfo(previousTeam, true);
  };

  const createTeamUpdate = async () => {
    const currentTeam = activeTeams[activeTeams.length - 1];
    setShowTeamOperateModal(null);
    if (currentTeam.id === rootTeam.id) {
      fetchData();
    } else {
      retrieveTeamInfo(currentTeam, true);
    }
  };

  const renameTeamUpdate = async (teamName: string) => {
    setShowTeamOperateModal(null);
    setActiveTeams((pre) => pre.with(pre.length - 1, { ...pre[pre.length - 1], name: teamName }));
  };

  const handleTeamUpdate = (teamName: string) => {
    if (showTeamOperateModal === 'create') {
      createTeamUpdate();
      return;
    }
    if (showTeamOperateModal === 'delete') {
      backToTeam();
      setShowTeamOperateModal(null);
      return;
    }
    renameTeamUpdate(teamName);
  };

  const deleteTeam = async () => {
    setLoading(true);
    let i = 0;
    while (i < selected.length) {
      await trpc.team.delete.mutate({ spaceId, id: selected[i] });
      i += 1;
    }
    setLoading(false);
    toast(t.team.msg_delete_team_success, { variant: 'success' });
    const lastTeam = activeTeams[activeTeams.length - 1];
    retrieveTeamInfo(lastTeam);
    setSelected([]);
  };
  const addMembers = async (teamId: string, memberIds: string[]) => {
    await trpc.team.addMembers.mutate({
      spaceId,
      id: teamId,
      memberIds,
    });
    toast(t.team.msg_add_member_success, { variant: 'success' });
    setSelected([]);
    const lastTeam = activeTeams[activeTeams.length - 1];
    retrieveTeamInfo(lastTeam);
    fetchData();
  };

  const unitList = searchInput ? searchResult : teamsAndMembers || union<TeamSubListPageItemVO>(members, teams);
  const userIds = users.map((member) => member.id);

  const {
    isShowModal: showAssignMember,
    setIsShowModal: setShowAssignMember,
    checkedOptions,
  } = useSelectMemberUnitModal(spaceId, userIds, false);

  const isAllChecked =
    (selected.length === (isRoot ? users.length : members.length) && selectMode === 'meb') ||
    (selected.length === teams.length && selectMode === 'tem');

  const handleCheck = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    const id = e.target.value;

    if (isChecked) {
      setSelected((pre) => [...pre, id]);
      return;
    }

    setSelected((pre) => pre.filter((i) => i !== id));
  };

  const checkAll = () => {
    if (isAllChecked) return setSelected([]);
    if (selectMode === 'meb') {
      const ids = isRoot ? userIds : members.map((member) => member.id);
      setSelected(ids);
    } else {
      setSelected(teams.map((team) => team.id));
    }
  };

  const handleUnitMenuItems = useMemo((): IBMenuItem[][] => {
    const choosenItems = compact([
      permission?.deleteMember && {
        label: selectMode === 'meb' ? t.action.cancel : t.record.select_member,
        icon: <CheckboxOutlined currentColor />,
        onClick: () => setSelectMode(selectMode === 'meb' ? undefined : 'meb'),
        disabled: !unitList?.length || members.length === 0,
      },
      permission?.deleteTeam && {
        label: selectMode === 'tem' ? t.action.cancel : t.team.select_team,
        icon: <CheckboxOutlined currentColor />,
        onClick: () => setSelectMode(selectMode === 'tem' ? undefined : 'tem'),
        disabled: !unitList?.length || teams.length === 0,
      },
    ]);
    const editTeamItems = compact([
      permission?.createTeam && {
        label: t.team.create_team,
        icon: <FolderAddOutlined currentColor />,
        disabled: searchResult.length > 0,
        onClick: () => setShowTeamOperateModal('create'),
      },
      permission?.updateTeam && {
        label: t.team.edit_team,
        icon: <EditOutlined currentColor />,
        disabled: searchResult.length > 0 || activeTeams.length <= 1,
        onClick: () => setShowTeamOperateModal('rename'),
      },
    ]);
    const deleteTeamItems = compact([
      permission?.deleteTeam && {
        label: t.team.delete_team,
        icon: <DeleteOutlined currentColor />,
        onClick: () => setShowTeamOperateModal('delete'),
        disabled: selected.length === 0 || selectMode === 'meb',
        color: 'danger',
      },
    ]);
    return compact([
      choosenItems.length > 0 && compact(choosenItems),
      editTeamItems.length > 0 && compact(editTeamItems),
      deleteTeamItems.length > 0 && compact(deleteTeamItems),
    ]);
  }, [
    permission,
    selectMode,
    unitList,
    members.length,
    teams.length,
    searchResult.length,
    activeTeams.length,
    selected.length,
    t.action.cancel,
    t.record.select_member,
    t.team,
  ]);

  const backButton = !isRoot && (
    <Typography
      level="b3"
      className="cursor-pointer"
      startDecorator={<ChevronLeftOutlined size={16} color="var(--text-secondary)" />}
      onClick={() => backToTeam()}
    >
      {t.buttons.back}
    </Typography>
  );

  const unitListRender = () => (
    <Box
      sx={{
        height: 'calc(100% - 108px)',
        p: isFromSidebar ? 0 : '0 16px 0 24px',
      }}
    >
      <Stack direction={isFromSidebar ? 'column-reverse' : 'column'}>
        <div
          className={classNames(
            isFromSidebar ? 'flex-col-reverse space-y-2 gap-2' : 'flex-row items-center',
            'flex mb-3',
          )}
        >
          <Box className={'flex-1 flex items-center space-x-2'}>
            {!searchInput && searchResult.length === 0 && (
              <Breadcrumbs aria-label="breadcrumbs" sx={{ padding: '8px 4px' }}>
                {activeTeams.map((team, i) => (
                  <Stack
                    key={team.id}
                    direction="row"
                    spacing={0.5}
                    onClick={() => {
                      if (i === activeTeams.length - 1 && !isFromSidebar) return;
                      retrieveTeamInfo(team, true);
                      if (isFromSidebar) {
                        spaceRouter.push(`/space/${spaceId}/team/${team.id}`);
                      }
                    }}
                    sx={{
                      px: '4px',
                      borderRadius: '2px',
                      ':hover': { backgroundColor: 'var(--hover)' },
                      ':active': { backgroundColor: 'var(--active)' },
                      cursor: i === activeTeams.length - 1 && !isFromSidebar ? 'default' : 'pointer',
                    }}
                  >
                    {isFromSidebar && team.id === rootTeam.id && (
                      <AvatarImg name={space.name} avatar={space.logo} customSize={AvatarSize.Size20} shape="SQUARE" />
                    )}
                    <Typography
                      level="b3"
                      sx={{
                        color: i === activeTeams.length - 1 ? 'var(--brand)' : 'var(--text-secondary)',
                      }}
                    >
                      {team.name || space.name}
                    </Typography>
                  </Stack>
                ))}
              </Breadcrumbs>
            )}
          </Box>
          {isFromSidebar && backButton}
          <div className={'space-x-2 flex items-center'}>
            {permission?.invite && (
              <>
                {!isRoot && (
                  <Button
                    color="neutral"
                    startDecorator={<AddCircleOutlined color="var(--text-secondary)" />}
                    onClick={() => setShowAssignMember(true)}
                    className={isFromSidebar ? 'flex-1' : ''}
                  >
                    {t.settings.member.assign_members}
                  </Button>
                )}
                <Button
                  variant="soft"
                  color="neutral"
                  startDecorator={<UserAddOutlined color="var(--text-secondary)" />}
                  onClick={() => {
                    spaceContext.showUIModal({
                      type: 'space-settings',
                      tab: {
                        type: 'SPACE_INVITE_MEMBER',
                        props: {
                          teamId: activeTeams[activeTeams.length - 1].id,
                        },
                      },
                    });
                  }}
                  className={isFromSidebar ? 'flex-1' : ''}
                >
                  {t.invite.invite}
                </Button>
                {handleUnitMenuItems.length > 0 && (
                  <BMenu
                    buttonSlotProps={{
                      variant: 'soft',
                      color: 'neutral',
                    }}
                    items={handleUnitMenuItems}
                  />
                )}
              </>
            )}
          </div>
        </div>
        <Stack direction="row" spacing={2} alignItems="center">
          <Input
            sx={{ flex: 1 }}
            value={searchInput}
            startDecorator={<SearchOutlined />}
            placeholder={t.action.search}
            onChange={(e) => setSearchInput(e.target.value)}
            slotProps={{ input: { ref: inputRef } }}
          />
          {searchInput && searchResult.length > 0 && <div className={'my-2 text-b2'}>{t.action.search_result}: </div>}
          {!permission?.invite && handleUnitMenuItems.length > 0 && <BMenu items={handleUnitMenuItems} />}
        </Stack>
      </Stack>
      <Stack
        spacing={0.5}
        className={'overflow-y-auto relative'}
        sx={{ height: selectMode ? 'calc(86vh - 212px)' : 'calc(86vh - 150px)' }}
      >
        {loading && <LoadingMask />}
        {unitList.length > 0 ? (
          <>
            {unitList.map((item, index) => {
              if (!item || item.type === 'Role') return null;
              const isAI = item.type === 'Member' && item.relationType === 'AI';

              const showCheckbox = selectMode && item.id.includes(selectMode) && (!isRoot || !isAI);

              return (
                <Fragment key={item.id}>
                  <div
                    className={classNames(
                      'p-[8px] flex items-center space-x-2 rounded',
                      { 'bg-[var(--selected)]': (params.memberId || params.teamId) === item.id },
                      !isFromSidebar
                        ? {
                            'hover:bg-[var(--hover)]': item.type !== 'Member',
                          }
                        : styles.unitItem,
                    )}
                  >
                    {showCheckbox && (
                      <Checkbox checked={selected.includes(item.id)} value={item.id} onChange={handleCheck} />
                    )}
                    {item.type === 'Member' && (
                      <MemberItem
                        {...item}
                        removeMember={removeMember}
                        deleteMember={deleteMember}
                        editMember={() => {
                          setMemberItem(item);
                        }}
                        isRoot={isRoot}
                        isTeam={true}
                        onClick={() => {
                          if (isFromSidebar) {
                            spaceRouter.push(`/space/${spaceId}/member/${item.id}`);
                          }
                        }}
                      />
                    )}
                    {item.type === 'Team' && (
                      <TeamItem
                        {...(item as TeamVO)}
                        onClick={() => {
                          if (isFromSidebar) {
                            spaceRouter.push(`/space/${spaceId}/team/${item.id}`);
                          } else {
                            retrieveTeamInfo(item as TeamVO);
                          }
                        }}
                        onIconClick={(e) => {
                          if (isFromSidebar) {
                            e.stopPropagation();
                            retrieveTeamInfo(item as TeamVO);
                          }
                        }}
                      />
                    )}
                  </div>
                  {index !== unitList.length - 1 && <Divider />}
                </Fragment>
              );
            })}
            <Box sx={{ textAlign: 'center' }} mt={1}>
              <Button
                variant="soft"
                color="neutral"
                disabled={!isMore}
                // loading={loading}
                onClick={(e) => {
                  if (!isMore) return;
                  setPageNo(pageNo + 1);
                  e.preventDefault();
                }}
              >
                {isMore ? t.pagination.load_more : t.pagination.no_more}
              </Button>
            </Box>
          </>
        ) : (
          !loading && <BlankPage text={t('action.search_result_not_found_description', { keyword: searchInput })} />
        )}
      </Stack>
    </Box>
  );

  return (
    <div className={isFromSidebar ? 'h-full' : styles.SpaceSettings}>
      {!isFromSidebar && (
        <NavHeader leftControls={backButton} onClose={() => spaceContext.showUIModal(null)}>
          {title}
        </NavHeader>
      )}

      {memberItem ? (
        <UnitMemberManager
          spaceId={spaceId}
          member={memberItem}
          onClose={() => {
            setMemberItem(null);
            fetchData();
          }}
          deleteMember={deleteMember}
        />
      ) : (
        unitListRender()
      )}

      {selectMode && !memberItem && (
        <Box
          sx={{
            padding: '0 24px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            height: '64px',
            borderTop: '1px solid var(--border-default)',
          }}
        >
          <div>
            <Checkbox label={t.action.select_all} checked={isAllChecked} onChange={checkAll} />
          </div>
          <div className={'space-x-2'}>
            <Button variant="soft" color="neutral" onClick={() => setSelectMode(undefined)}>
              {t.buttons.cancel}
            </Button>
            <Button
              color="danger"
              loading={loading}
              onClick={() => {
                if (selectMode === 'tem') {
                  Modal.show({
                    type: 'error',
                    title: t.team.delete_team,
                    content: t.team.delete_team_description,
                    okText: t.action.delete,
                    cancelText: t.action.cancel,
                    onOk: deleteTeam,
                  });
                  return;
                }
                Modal.show({
                  type: 'error',
                  title: activeTeams.length > 1 ? t.team.remove_member_from_team : t.team.remove_member_from_space,
                  content:
                    activeTeams.length > 1
                      ? t.team.remove_member_from_team_description
                      : t.team.remove_member_from_space_description,
                  okText: t.action.remove,
                  cancelText: t.action.cancel,
                  onOk: onClickKickMember,
                });
              }}
            >
              {selected.length > 0
                ? t(selectMode === 'meb' ? 'settings.member.remove_members' : 'team.delete_teams', {
                    count: selected.length,
                  })
                : t.buttons.remove}
            </Button>
          </div>
        </Box>
      )}
      {showTeamOperateModal !== null && (
        <TeamOperateModal
          type={showTeamOperateModal}
          spaceId={spaceId}
          currentTeamId={activeTeams[activeTeams.length - 1].id}
          onClose={() => setShowTeamOperateModal(null)}
          cb={handleTeamUpdate}
          teamName={showTeamOperateModal === 'rename' ? activeTeams[activeTeams.length - 1].name : undefined}
          parentTeamId={
            showTeamOperateModal === 'rename'
              ? activeTeams[activeTeams.length - 1].id || rootTeam.id
              : activeTeams[activeTeams.length - 1].id
          }
        />
      )}
      {showAssignMember && (
        <MemberSelectModal
          target={['Member']}
          multiple={true}
          selectedUnits={checkedOptions}
          onChange={(items) =>
            addMembers(
              activeTeams[activeTeams.length - 1].id,
              items.map((item) => item.id),
            )
          }
          onCancel={() => setShowAssignMember(false)}
          setIsEdit={(isOpen) => setShowAssignMember(isOpen)}
          api={{
            useRoles: () => trpcQuery.role.list.useQuery({ spaceId }),
            getSearchUnits: (search: UnitSearch) =>
              trpc.unit.list.query({
                spaceId,
                ...search,
              }),
            getTeamSubList: (teamId: string) =>
              trpc.team.subList.query({
                spaceId,
                teamId,
                pageSize: 50,
              }),
          }}
          locale={locale}
        />
      )}
    </div>
  );
}
