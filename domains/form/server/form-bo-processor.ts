import assert from 'assert';
import { DatabaseBOProcessor } from '@bika/domains/database/server/database-bo-processor';
import { AbstractNodeBOProcessor } from '@bika/domains/node/server/node-bo-processor';
import { ViewFieldVO } from '@bika/types/database/vo';
import { FormBO, DatabaseFormBO } from '@bika/types/form/bo';
import { FormVO, BORenderOpts } from '@bika/types/node/vo';
import { iStringParse } from '@bika/types/system';
import { ViewBOProcessor } from '../../database/server/views/view-bo-processor';

export class FormBOProcessor extends AbstractNodeBOProcessor<FormBO> {
  getDatabase(opts: BORenderOpts): DatabaseBOProcessor {
    const { getProcessor } = opts;
    const { databaseId, databaseTemplateId } = this.bo as DatabaseFormBO;
    return getProcessor?.(databaseId || databaseTemplateId!) as DatabaseBOProcessor;
  }

  getView(opts: BORenderOpts): ViewBOProcessor | null {
    const { metadata } = this.bo as DatabaseFormBO;
    if (metadata?.type === 'VIEW') {
      const { viewId, viewTemplateId } = metadata;
      const database = this.getDatabase(opts);
      return database.getView(viewId || viewTemplateId!);
    }
    return null;
  }

  getFields(opts: BORenderOpts): ViewFieldVO[] {
    const database = this.getDatabase(opts);
    const { metadata } = this.bo as DatabaseFormBO;
    if (metadata?.type === 'VIEW') {
      const view = this.getView(opts);
      return view?.getColumns(opts) ?? [];
    }
    return database.fields.map((f) => f.toVO(opts));
  }

  override toVO<V>(opts: BORenderOpts): V {
    const { getProcessor } = opts;
    assert(this.bo.formType !== 'AUTOMATION', 'FormBOProcessor: toVO is only available for DATABASE form');
    assert(getProcessor, 'FormBOProcessor: getProcessor is required');
    const database = this.getDatabase(opts);
    const view = this.getView(opts);
    const vo: FormVO = {
      id: this.id,
      name: iStringParse(this.bo.name, opts?.locale),
      description: iStringParse(this.bo.description, opts?.locale),
      fields: this.getFields(opts),
      database: database.toSimpleVO(opts),
      view: view?.toSimpleVO(opts),
      metadata: this.bo.metadata!,
      databaseId: database.id,
    };
    return vo as V;
  }
}
