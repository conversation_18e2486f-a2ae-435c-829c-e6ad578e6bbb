/* eslint-disable no-param-reassign */
import assert from 'assert';
import _ from 'lodash';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { AttachmentModel } from '@bika/domains/attachment/server/types';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { ViewSO } from '@bika/domains/database/server/views/view-so';
import { TemplateFolderSO } from '@bika/domains/node/server/folder-so';
import { NodeResourceAdapter } from '@bika/domains/node/server/node-resource-adapter';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { IRelationIdOpts, NodeResourceSO } from '@bika/domains/node/server/types';
import { SpaceAttachmentSO } from '@bika/domains/space/server/space-attachment-so';
import { UserSO } from '@bika/domains/user/server';
import { $Enums, db, MongoTransactionCB, Prisma, PrismaPromise } from '@bika/server-orm';
import { FieldVO, BaseDatabaseVO } from '@bika/types/database/vo';
import { FormBO, FormMetadata } from '@bika/types/form/bo';
import { ExportBOOptions, NodeResourceType, ToTemplateOptions } from '@bika/types/node/bo';
import { FormCreateServerBO, FormUpdateBO } from '@bika/types/node/dto';
import { NodeRenderOpts, FormVO } from '@bika/types/node/vo';
import { AvatarLogo, iString, iStringParse, LocaleType } from '@bika/types/system';

export type FormModel = Prisma.FormGetPayload<{
  include: {
    node: {
      include: {
        folder: true;
      };
    };
  };
}>;

/**
 * form service object.
 */
export class FormSO extends NodeResourceSO {
  private readonly _model: FormModel;

  constructor(model: FormModel) {
    super();
    this._model = model;
  }

  get id(): string {
    return this._model.id;
  }

  get resourceType(): NodeResourceType {
    return 'FORM';
  }

  get model(): FormModel {
    return this._model;
  }

  get spaceId(): string {
    return this._model.spaceId;
  }

  get name(): iString {
    return this._model.name as iString;
  }

  getName(locale: LocaleType = 'en'): string {
    return iStringParse(this.name, locale);
  }

  get description(): iString {
    return this._model.description as iString;
  }

  get databaseId(): string {
    return this._model.databaseId;
  }

  get templateId(): string | undefined {
    return this._model.templateId || undefined;
  }

  get brandLogo(): AvatarLogo | undefined {
    return this._model.brandLogo as AvatarLogo;
  }

  get viewId(): string | undefined {
    return _.get(this.model.metadata, 'viewId');
  }

  async getMetadata(): Promise<FormMetadata> {
    const metadata = this.model.metadata as FormMetadata;
    // should find view id by templateId
    if (metadata && metadata.type === 'VIEW' && !metadata.viewId && metadata.viewTemplateId) {
      const databaseSO = await this.getDatabase();
      if (!databaseSO) {
        return metadata;
      }
      const viewSO = await databaseSO.getViewByViewKey({ viewTemplateId: metadata.viewTemplateId! });
      metadata.viewId = viewSO?.id;
    }
    return metadata;
  }

  static async init(id: string): Promise<FormSO> {
    const form = await this.initMaybeNull(id);
    if (!form) {
      throw new ServerError(errors.common.not_found);
    }
    return form;
  }

  static async initMaybeNull(id: string): Promise<FormSO | undefined> {
    const form = await db.prisma.form.findUnique({
      where: { id },
      include: {
        node: {
          include: {
            folder: true,
          },
        },
      },
    });
    return form ? this.initWithModel(form) : undefined;
  }

  static initWithModel(model: FormModel) {
    return new FormSO(model);
  }

  toNodeSO(): NodeSO {
    return NodeSO.initWithModel(this.model.node);
  }

  async getDatabase(): Promise<DatabaseSO | null> {
    try {
      const database = await DatabaseSO.init(this.model.databaseId);
      return database;
    } catch (_error) {
      console.log(`form:${this.id} linked database:${this.model.databaseId} not exits`);
      return null;
    }
  }

  async getView(): Promise<ViewSO | null> {
    const metadata = await this.getMetadata();
    if (metadata.type === 'VIEW' && metadata.viewId) {
      return ViewSO.initMaybeNull(metadata.viewId);
    }
    return null;
  }

  async getFields(opts?: NodeRenderOpts): Promise<FieldVO[]> {
    const { locale } = opts ?? {};
    const databaseSO = await this.getDatabase();
    if (!databaseSO) {
      return [];
    }
    const metadata = await this.getMetadata();
    if (metadata.type === 'VIEW' && (metadata.viewId || metadata.viewTemplateId)) {
      const viewSO = await databaseSO.getViewByViewKey({
        viewId: metadata.viewId,
        viewTemplateId: metadata.viewTemplateId,
      });
      const viewColumns = viewSO.getFields();
      return viewColumns.map((field) => field.toVO({ locale, viewFields: viewSO.fields }));
    }
    const fields = databaseSO.getFields();
    return fields.map((field) => field.toVO({ locale }));
  }

  override async toBO(): Promise<FormBO> {
    const database = await this.getDatabase();
    return {
      id: this.id,
      templateId: this.templateId,
      resourceType: 'FORM',
      name: this.name,
      description: this.description || undefined,
      icon: this.toNodeSO().icon,
      metadata: this.model.metadata as FormMetadata,
      databaseId: database?.id,
      brandLogo: (this.model.brandLogo as AvatarLogo) || undefined,
      cover: (this.model.node.folder?.cover as AvatarLogo) || undefined,
      formType: 'DATABASE',
      databaseTemplateId: database?.templateId,
    };
  }

  override async toTemplate(opts?: ToTemplateOptions): Promise<FormBO> {
    const form = _.omit(await this.toBO(), ['id', 'databaseId']) as FormBO;
    if (!form.templateId) {
      form.templateId = this.id;
    }
    if (form.formType === 'DATABASE') {
      const { metadata } = form;
      if (opts?.getTemplateId && !opts.getTemplateId(this.model.databaseId)) {
        throw new Error(`the form ${form.id} linked database ${this.model.databaseId} not in current folder`);
      }
      if (opts?.getTemplateId) {
        if (!form.databaseTemplateId) {
          form.databaseTemplateId = opts.getTemplateId(this.model.databaseId);
        }
        if (metadata?.type === 'VIEW') {
          if (!metadata.viewTemplateId) {
            metadata.viewTemplateId = opts.getTemplateId(metadata.viewId!);
            metadata.viewId = undefined;
          }
        }
      }
    }
    return form;
  }

  async export(opts?: ExportBOOptions): Promise<FormBO> {
    const formBO = await this.toBO();
    if (formBO.formType === 'DATABASE') {
      if (formBO.databaseId && !opts?.getInstanceId?.(formBO.databaseId)) {
        throw new Error(`The form ${this.id} linked database ${formBO.databaseId} not in current folder`);
      }
    }
    return formBO;
  }

  override async setTemplateId() {
    const operations: PrismaPromise<Prisma.BatchPayload>[] = [];
    if (!this.templateId) {
      operations.push(
        ...[
          db.prisma.form.updateMany({
            where: { id: this.id },
            data: {
              templateId: this.id,
            },
          }),
          db.prisma.node.updateMany({
            where: { id: this.id },
            data: {
              templateId: this.id,
            },
          }),
        ],
      );
    }
    return operations;
  }

  async toVO(opts?: NodeRenderOpts): Promise<FormVO> {
    const { locale } = opts ?? {};
    const [database, fields, view] = await Promise.all([this.getDatabase(), this.getFields(opts), this.getView()]);
    return {
      id: this.model.id,
      name: this.getName(locale),
      description: iStringParse(this._model.description as iString, locale),
      metadata: await this.getMetadata(),
      cover: this.model.node.folder?.cover as AvatarLogo,
      brandLogo: this.model.brandLogo as AvatarLogo,
      databaseId: this.model.databaseId,
      database: database ? database.toSimpleVO({ locale }) : ({} as BaseDatabaseVO),
      fields,
      view: view ? view.toSimpleVO({ locale }) : undefined,
    };
  }

  async update(user: UserSO, data: FormUpdateBO): Promise<void> {
    const { operations, mongoSessions } = await this.updateWithNodeInput(user, data);
    await db.mongo.transaction(async (session) => {
      for (const mongoSession of mongoSessions) {
        await mongoSession(session);
      }
      await db.prisma.$transaction(operations);
    });
  }

  static createWithoutNodeInput(
    userId: string,
    spaceId: string,
    data: FormCreateServerBO | FormBO,
  ): Prisma.FormUncheckedCreateNestedOneWithoutNodeInput | undefined {
    if ($Enums.NodeResourceType.FORM === data.resourceType) {
      return {
        create: {
          name: data.name,
          description: data.description,
          templateId: data.templateId,
          databaseId: _.get(data, 'databaseId')!,
          brandLogo: data.brandLogo,
          createdBy: userId,
          updatedBy: userId,
          spaceId,
          metadata: {
            type: 'VIEW',
            viewId: _.get(data, 'viewId') || _.get(data, ['metadata', 'viewId'])!,
          },
        },
      };
    }
    return undefined;
  }

  static createWithNodeInput(
    userId: string,
    form: FormBO,
    relation: {
      spaceId: string;
      parentId: string;
      preNodeId?: string;
      nextNodeId?: string;
      unitId?: string;
    },
  ): { id: string; operation: PrismaPromise<FormModel> } {
    assert(form.formType === 'DATABASE' || form.formType === undefined);
    const id = form.id || NodeResourceAdapter.generateId('FORM');
    const operation = db.prisma.form.create({
      data: {
        name: form.name,
        description: form.description,
        spaceId: relation.spaceId,
        templateId: form.templateId,
        metadata: form.metadata,
        createdBy: userId,
        updatedBy: userId,
        databaseId: form.databaseId!,
        node: {
          create: {
            id,
            templateId: form.templateId,
            name: form.name,
            description: form.description,
            type: $Enums.NodeResourceType.FORM,
            createdBy: userId,
            updatedBy: userId,
            icon: form.icon,
            unit: relation.unitId ? { connect: { id: relation.unitId } } : undefined,
            space: {
              connect: {
                id: relation.spaceId,
              },
            },
            parent: {
              connect: {
                id: relation.parentId,
              },
            },
            preNode: relation.preNodeId
              ? {
                  connect: {
                    id: relation.preNodeId,
                  },
                }
              : undefined,
            nextNode: relation.nextNodeId
              ? {
                  connect: {
                    id: relation.nextNodeId,
                  },
                }
              : undefined,
            folder: {
              create: {
                templateId: form.templateId,
                cover: form.cover,
                createdBy: userId,
                updatedBy: userId,
              },
            },
          },
        },
      },
      include: {
        node: {
          include: {
            folder: true,
          },
        },
      },
    });
    return { id, operation };
  }

  static upsertWithNodeInput(
    userId: string,
    form: FormBO & { id: string },
    relation: {
      spaceId: string;
      parentId: string;
      preNodeId?: string;
      nextNodeId?: string;
    },
  ) {
    assert(form.formType === 'DATABASE' || form.formType === undefined);
    const id = form.id;
    const operation = db.prisma.form.upsert({
      where: {
        id,
      },
      create: {
        name: form.name,
        description: form.description,
        templateId: form.templateId,
        spaceId: relation.spaceId,
        databaseId: form.databaseId!,
        metadata: form.metadata,
        createdBy: userId,
        updatedBy: userId,
        node: {
          create: {
            id,
            templateId: form.templateId,
            name: form.name,
            description: form.description,
            type: $Enums.NodeResourceType.FORM,
            createdBy: userId,
            updatedBy: userId,
            space: {
              connect: {
                id: relation.spaceId,
              },
            },
            parent: {
              connect: {
                id: relation.parentId,
              },
            },
            preNode: relation.preNodeId
              ? {
                  connect: {
                    id: relation.preNodeId,
                  },
                }
              : undefined,
            nextNode: relation.nextNodeId
              ? {
                  connect: {
                    id: relation.nextNodeId,
                  },
                }
              : undefined,
            folder: {
              create: {
                templateId: form.templateId,
                cover: form.cover,
                createdBy: userId,
                updatedBy: userId,
              },
            },
          },
        },
      },
      update: {
        name: form.name,
        description: form.description,
        databaseId: form.databaseId,
        metadata: form.metadata,
        updatedBy: userId,
        node: {
          update: {
            name: form.name,
            description: form.description,
            updatedBy: userId,
            folder: {
              update: {
                cover: form.cover,
                updatedBy: userId,
              },
            },
          },
        },
      },
      include: {
        node: {
          include: {
            folder: true,
          },
        },
      },
    });
    return { id, operation };
  }

  async updateWithNodeInput(
    user: UserSO,
    data: FormUpdateBO,
  ): Promise<{
    operations: PrismaPromise<FormModel | AttachmentModel>[];
    mongoSessions: MongoTransactionCB[];
  }> {
    const { name, description, metadata, brandLogo, databaseId, cover } = data;
    const operations: PrismaPromise<FormModel | AttachmentModel>[] = [
      db.prisma.form.update({
        where: {
          id: this.id,
        },
        data: {
          name,
          description,
          metadata,
          updatedBy: user.id,
          brandLogo,
          databaseId,
          node: {
            update: {
              name,
              description,
              updatedBy: user.id,
              folder: {
                update: {
                  cover,
                  updatedBy: user.id,
                },
              },
            },
          },
        },
        include: {
          node: {
            include: {
              folder: true,
            },
          },
        },
      }),
    ];

    const mongoSessions: MongoTransactionCB[] = [];
    if (brandLogo) {
      // 改变了封面
      const space = await this.toNodeSO().getSpace();
      const { operations: attachmentOperations, mongoSessions: attachmentMongoSessions } =
        await SpaceAttachmentSO.buildChangeAvatarSession(
          user.id,
          space,
          { type: 'RESOURCE', id: this.id },
          {
            previous: this.brandLogo,
            current: brandLogo,
          },
        );
      operations.push(...attachmentOperations);
      mongoSessions.push(...attachmentMongoSessions);
    }

    return { operations, mongoSessions };
  }

  static async findByDatabaseId(databaseId: string): Promise<FormSO[]> {
    const forms = await db.prisma.form.findMany({
      where: { databaseId },
      include: {
        node: {
          include: {
            folder: true,
          },
        },
      },
    });
    return forms.map((form) => new FormSO(form));
  }

  static async findForm(param: { templateNodeId?: string; formId?: string; formTemplateId?: string }): Promise<FormSO> {
    const { templateNodeId, formId, formTemplateId } = param;
    return TemplateFolderSO.findNodeResourceByKey<FormSO>(
      {
        nodeId: formId,
        nodeTemplateId: formTemplateId,
        templateNodeId,
      },
      'form',
    );
  }

  override relationInstanceId(form: FormBO, opts: IRelationIdOpts): boolean {
    const { replaceInstanceId } = opts;
    if (!replaceInstanceId) {
      return false;
    }
    if (form.formType === 'DATABASE') {
      form.databaseId = replaceInstanceId(form.databaseTemplateId || form.databaseId!);
      const { metadata } = form;
      if (metadata?.type === 'VIEW') {
        const { viewId, viewTemplateId } = metadata;
        if (viewTemplateId && form.databaseTemplateId) {
          metadata.viewId = replaceInstanceId(`${form.databaseTemplateId}:${viewTemplateId}`);
        } else {
          metadata.viewId = replaceInstanceId(viewId!);
        }
      }
    }
    return true;
  }
}
