import { describe, expect, test } from 'vitest';
import { MockContext, waitForMatchToBeMet } from '@bika/domains/__tests__/mock';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { FolderSO } from '@bika/domains/node/server/folder-so';
import { FormSO } from '../server/form-so';

describe('Install form fom template', async () => {
  test('install from: ai-automated-ticket-system', async () => {
    const { user, space } = await MockContext.initUserContext();
    const templateFolder = await space.installTemplateById(user, 'ai-automated-ticket-system');
    await waitForMatchToBeMet(
      async () => {
        const node = await FolderSO.init(templateFolder.id);
        const children = await node.getAllChildren();
        return children.length === 5 && children[3].type === 'AUTOMATION';
      },
      10000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
  });
});

describe('Create form', async () => {
  test('create form', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const node = await rootFolder.createChildSimple(user, {
      name: {
        en: 'test create database',
      },
      resourceType: 'DATABASE',
    });
    const database = await node.toResourceSO<DatabaseSO>();
    const formNode = await rootFolder.createChildSimple(user, {
      resourceType: 'FORM',
      name: 'new form',
      formType: 'DATABASE',
      databaseId: database.id,
      viewId: (await database.firstView()).id,
    });
    await database.createField(user, {
      type: 'SINGLE_TEXT',
      name: 'new field',
    });
    const form = await formNode.toResourceSO<FormSO>();
    const fields = await form.getFields();
    expect(fields.length).to.equal(database.getFields().length);
  });
});

describe('Upate form', async () => {
  test('update form', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const node = await rootFolder.createChildSimple(user, {
      name: {
        en: 'test create database',
      },
      resourceType: 'DATABASE',
    });
    const database = await node.toResourceSO<DatabaseSO>();
    const formNode = await rootFolder.createChildSimple(user, {
      resourceType: 'FORM',
      name: 'new form',
      formType: 'DATABASE',
      databaseId: database.id,
      viewId: (await database.firstView()).id,
    });
    await database.createField(user, {
      type: 'SINGLE_TEXT',
      name: 'new field',
    });
    const form = await formNode.toResourceSO<FormSO>();
    await form.update(user, {
      resourceType: 'FORM',
      name: 'new form name',
    });
    const updatedForm = await FormSO.init(form.id);
    expect(updatedForm.model.name).to.equal('new form name');
  });
});
