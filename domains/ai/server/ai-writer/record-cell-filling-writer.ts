import assert from 'assert';
import _ from 'lodash';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { DatabaseRecordModel } from '@bika/server-orm';
import { AIWriterResponse } from '@bika/types/ai/bo';
import { BaseAIWriter } from './base-ai-writer';
import { AISO } from '../ai-so';

export class RecordCellFillingAIWriter extends BaseAIWriter {
  async write(userPrompt: string): Promise<AIWriterResponse> {
    assert(this._writerOptions.type === 'RECORD_CELL_FILLING', 'type must be RECORD_CELL_FILLING');
    const { recordId, databaseId, fieldId, field, cells } = this._writerOptions;
    const database = await DatabaseSO.init(databaseId);
    const getRecord = async () => {
      let record: Pick<DatabaseRecordModel, 'data' | 'values' | 'computed'> | undefined;
      if (cells) {
        record = {
          data: cells,
          values: {},
          computed: {},
        };
      } else if (recordId) {
        record = (await database.getRecord(recordId)).model;
      } else {
        record = (await database.getFirstRecord())?.model;
      }
      return record;
    };
    const record = await getRecord();
    if (!record) {
      throw new ServerError(errors.database.ai_writer_no_record_found);
    }
    const fields = database.getFields();
    const fieldMap = _.keyBy(fields, 'id');
    const fieldBO = fieldId ? fieldMap[fieldId]?.toBO() : field;
    if (!fieldBO) {
      throw new ServerError(errors.database.field_not_found, {
        key: fieldId,
        databaseName: database.getName(),
      });
    }
    if (fieldBO.type === 'AI_TEXT') {
      assert(fieldBO.type === 'AI_TEXT');

      const { result } = await AISO.streamText(
        {
          user: this._user,
          prompt: fieldBO.property.prompt,
        },
        {
          model: fieldBO.property.model,
        },
      );
      // 等待 stream 完成
      for await (const _ of result.fullStream) {
        // 消费 stream
      }
      return {
        data: result.text,
        success: true,
        message: 'success',
      };

      // deprecated field property handling
      // if (
      //   fieldBO.property.model &&
      //   fieldBO.property.model.kind === 'custom' &&
      //   fieldBO.property.model.custom.type === 'integration'
      // ) {
      //   const integrationId = fieldBO.property.model.custom.integrationId;
      //   const integration = await IntegrationSO.initMaybeNull(integrationId!);
      //   if (!integration) {
      //     throw new ServerError(errors.database.ai_writer_no_integration_found, {
      //       fieldId,
      //     });
      //   }

      //   const prompt = replaceVariablesInString(fieldBO.property.prompt || '', (fldId: string) => {
      //     const replaceField = fieldMap[fldId];
      //     if (!replaceField) {
      //       return '';
      //     }
      //     const data = record.values?.[fldId] || record.data?.[fldId] || record.computed?.[fldId] || '';
      //     if (data) {
      //       return data;
      //     }
      //     return iStringParse(replaceField.name, this._reqContext.locale);
      //   }).replace(/<%=|%>/g, '');

      //   console.log('record cell filling prompt:', prompt);

      //   if (integration.type === 'OPENAI' || integration.type === 'DEEPSEEK') {
      //     const handler = integration.getHandler<OpenAIHandler | DeepSeekHandler>();
      //     const { response } = await handler.action({
      //       prompt: prompt + userPrompt,
      //       userId: this._reqContext.userId!,
      //       model: fieldBO.property.modelId,
      //     });
      //     return {
      //       data: response,
      //       success: true,
      //       message: 'success',
      //     };
      //   }
      // } else {
      //   //  TODO: not integration?
      // }
    }
    throw new ServerError(errors.database.ai_writer_no_integration_found, {
      fieldId,
    });
  }
}
