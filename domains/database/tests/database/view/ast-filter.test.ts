import { describe, expect, test } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { ViewFilter } from '@bika/types/database/bo';
import { DatabaseSO } from '../../../server/database-so';
import { AstFilter } from '../../../server/views/filter/ast-filter';
import { FilterSO } from '../../../server/views/filter-so';
import { allFieldKeys, allFields } from '../field-util';

describe('AST Filter Tests', async () => {
  const { user, member, rootFolder } = await MockContext.initUserContext();

  // 创建全字段的测试表
  const databaseNode = await rootFolder.createChildSimple(user, {
    name: 'database',
    resourceType: 'DATABASE',
    fields: allFields,
  });
  const database = await databaseNode.toResourceSO<DatabaseSO>();

  // 初始化一些数据
  await database.createRecords(user, member, [
    {
      [allFieldKeys.SINGLE_TEXT]: 'text1',
      [allFieldKeys.LONG_TEXT]: 'long text 1',
      [allFieldKeys.URL]: 'https://example.com',
      [allFieldKeys.EMAIL]: '<EMAIL>',
      [allFieldKeys.PHONE]: '1234567890',
      [allFieldKeys.NUMBER]: 1,
      [allFieldKeys.CURRENCY]: 100.5,
      [allFieldKeys.PERCENT]: 50,
      [allFieldKeys.RATING]: 4,
      [allFieldKeys.SINGLE_SELECT]: ['opt-s-1'],
      [allFieldKeys.MULTI_SELECT]: ['opt-m-1', 'opt-m-2'],
      [allFieldKeys.CHECKBOX]: true,
      [allFieldKeys.DATETIME]: '2023-09-30T16:00:00.000Z', // 等于用户在东八区输入了 2023-10-01
      [allFieldKeys.MEMBER]: [member.id],
    },
    {
      [allFieldKeys.SINGLE_TEXT]: 'text2',
      [allFieldKeys.LONG_TEXT]: 'long text 2',
      [allFieldKeys.URL]: 'https://example.org',
      [allFieldKeys.EMAIL]: '<EMAIL>',
      [allFieldKeys.PHONE]: '0987654321',
      [allFieldKeys.NUMBER]: 2,
      [allFieldKeys.CURRENCY]: 200.75,
      [allFieldKeys.PERCENT]: 75,
      [allFieldKeys.RATING]: 5,
      [allFieldKeys.SINGLE_SELECT]: ['opt-s-2'],
      [allFieldKeys.MULTI_SELECT]: ['opt-m-2', 'M Option 3'],
      [allFieldKeys.CHECKBOX]: false,
      [allFieldKeys.DATETIME]: '2023-11-19T16:00:00.000Z', // 等于用户在东八区输入了 2023-11-20
      [allFieldKeys.MEMBER]: [member.id],
    },
    // 空行
    {},
  ]);

  // 检查记录数
  const records = await database.getRecordsAsPage();
  expect(records).toHaveLength(3);

  /**
   * 文本字段过滤语法测试
   */
  test('AST Filter Tests - text field filter', async () => {
    // 字段匹配
    const singleText = database.getFieldByFieldKey(allFieldKeys.SINGLE_TEXT);
    const longText = database.getFieldByFieldKey(allFieldKeys.LONG_TEXT);
    const url = database.getFieldByFieldKey(allFieldKeys.URL);
    const email = database.getFieldByFieldKey(allFieldKeys.EMAIL);
    const phone = database.getFieldByFieldKey(allFieldKeys.PHONE);

    // 测试: 为空 -> `==null` | `==NULL`
    const isEmptyTest = async () => {
      const filterAsRSql = 'single_text==null,long_text==NULL';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'Or',
        conditions: [],
        conds: [
          {
            fieldId: singleText.id,
            fieldType: 'SINGLE_TEXT',
            clause: {
              operator: 'IsEmpty',
            },
          },
          {
            fieldId: longText.id,
            fieldType: 'LONG_TEXT',
            clause: {
              operator: 'IsEmpty',
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该只有一条空记录
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(1);
      expect(filteredRecords[0].getCellData(singleText.id)).toBeUndefined();
    };

    // 测试: 不为空 -> `!=null` | `!=NULL`
    const isNotEmptyTest = async () => {
      const filterAsRSql = 'single_text!=null,long_text!=NULL';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'Or',
        conditions: [],
        conds: [
          {
            fieldId: singleText.id,
            fieldType: 'SINGLE_TEXT',
            clause: {
              operator: 'IsNotEmpty',
            },
          },
          {
            fieldId: longText.id,
            fieldType: 'LONG_TEXT',
            clause: {
              operator: 'IsNotEmpty',
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);
      // 检查过滤后的结果, 应该有两条非空记录
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(2);
      expect(filteredRecords[0].getCellData(singleText.id)).toBe('text1');
      expect(filteredRecords[1].getCellData(singleText.id)).toBe('text2');
    };

    // 测试: 等于 -> `==`
    const isTest = async () => {
      const filterAsRSql = 'single_text==text1;long_text=="long text 1";url==https://example.com';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'And',
        conditions: [],
        conds: [
          {
            fieldId: singleText.id,
            fieldType: 'SINGLE_TEXT',
            clause: {
              operator: 'Is',
              value: 'text1',
            },
          },
          {
            fieldId: longText.id,
            fieldType: 'LONG_TEXT',
            clause: {
              operator: 'Is',
              value: 'long text 1',
            },
          },
          {
            fieldId: url.id,
            fieldType: 'URL',
            clause: {
              operator: 'Is',
              value: 'https://example.com',
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该只有一条
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(1);
      expect(filteredRecords[0].getCellData(singleText.id)).toBe('text1');
    };

    // 测试: 不等于 -> `!=`
    const isNotTest = async () => {
      const filterAsRSql = 'single_text!=text1;long_text!="long text 2"';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'And',
        conditions: [],
        conds: [
          {
            fieldId: singleText.id,
            fieldType: 'SINGLE_TEXT',
            clause: {
              operator: 'IsNot',
              value: 'text1',
            },
          },
          {
            fieldId: longText.id,
            fieldType: 'LONG_TEXT',
            clause: {
              operator: 'IsNot',
              value: 'long text 2',
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该有1条
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(1);
      expect(filteredRecords[0].getCellData(singleText.id)).toBeUndefined();
    };

    // 测试: 包含 -> `=c=`, 只能输入字符串
    const containsTest = async () => {
      const filterAsRSql = 'single_text=c=text;long_text=c="long text"';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'And',
        conditions: [],
        conds: [
          {
            fieldId: singleText.id,
            fieldType: 'SINGLE_TEXT',
            clause: {
              operator: 'Contains',
              value: 'text',
            },
          },
          {
            fieldId: longText.id,
            fieldType: 'LONG_TEXT',
            clause: {
              operator: 'Contains',
              value: 'long text',
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该只有2条
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(2);
      expect(filteredRecords[0].getCellData(singleText.id)).toBe('text1');
    };

    // 测试: 不包含 -> `=nc=`
    const doesNotContainTest = async () => {
      // single_text不包含"text"和long_text不包含"long text"
      const filterAsRSql = 'single_text=nc=text;long_text=nc="long text"';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'And',
        conditions: [],
        conds: [
          {
            fieldId: singleText.id,
            fieldType: 'SINGLE_TEXT',
            clause: {
              operator: 'DoesNotContain',
              value: 'text',
            },
          },
          {
            fieldId: longText.id,
            fieldType: 'LONG_TEXT',
            clause: {
              operator: 'DoesNotContain',
              value: 'long text',
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);
    };

    await isEmptyTest();
    await isNotEmptyTest();
    await isTest();
    await isNotTest();
    await containsTest();
    await doesNotContainTest();
  });

  /**
   * 数字字段过滤语法测试
   */
  test('AST Filter Tests - number field filter', async () => {
    // 数字字段匹配
    const number = database.getFieldByFieldKey(allFieldKeys.NUMBER);
    const currency = database.getFieldByFieldKey(allFieldKeys.CURRENCY);
    const percent = database.getFieldByFieldKey(allFieldKeys.PERCENT);
    const rating = database.getFieldByFieldKey(allFieldKeys.RATING);
    const autoNumber = database.getFieldByFieldKey(allFieldKeys.AUTO_NUMBER);

    // 测试: 为空 -> `==null` | `==NULL`
    const isEmptyTest = async () => {
      const filterAsRSql = 'number==null;currency==NULL';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'And',
        conditions: [],
        conds: [
          {
            fieldId: number.id,
            fieldType: 'NUMBER',
            clause: {
              operator: 'IsEmpty',
            },
          },
          {
            fieldId: currency.id,
            fieldType: 'CURRENCY',
            clause: {
              operator: 'IsEmpty',
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该只有一条空记录
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(1);
    };

    // 测试: 不为空 -> `!=null` | `!=NULL`
    const isNotEmptyTest = async () => {
      const filterAsRSql = 'number!=null,currency!=NULL';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'Or',
        conditions: [],
        conds: [
          {
            fieldId: number.id,
            fieldType: 'NUMBER',
            clause: {
              operator: 'IsNotEmpty',
            },
          },
          {
            fieldId: currency.id,
            fieldType: 'CURRENCY',
            clause: {
              operator: 'IsNotEmpty',
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该有两条非空记录
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(2);
    };

    // 测试: 等于 -> `==`
    const isTest = async () => {
      const filterAsRSql = 'number==1,currency=="100.5",percent==75';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'Or',
        conditions: [],
        conds: [
          {
            fieldId: number.id,
            fieldType: 'NUMBER',
            clause: {
              operator: 'Is',
              value: 1,
            },
          },
          {
            fieldId: currency.id,
            fieldType: 'CURRENCY',
            clause: {
              operator: 'Is',
              value: 100.5,
            },
          },
          {
            fieldId: percent.id,
            fieldType: 'PERCENT',
            clause: {
              operator: 'Is',
              value: 75,
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该只有2条
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(2);
      expect(filteredRecords[0].getCellData(number.id)).toBe(1);
      expect(filteredRecords[1].getCellData(number.id)).toBe(2);
    };

    // 测试: 不等于 -> `!=`
    const isNotTest = async () => {
      const filterAsRSql = 'number!=1';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'And',
        conditions: [],
        conds: [
          {
            fieldId: number.id,
            fieldType: 'NUMBER',
            clause: {
              operator: 'IsNot',
              value: 1,
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该有3条
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(2);
      expect(filteredRecords[0].getCellData(number.id)).toBe(2);
      expect(filteredRecords[1].getCellData(number.id)).toBeUndefined();
    };

    // 测试: 大于 -> `>`, 小于 -> `<`, 大于等于 -> `>=`, 小于等于 -> `<=`
    const comparisonTest = async () => {
      const filterAsRSql = 'number>1;currency<"200.75";percent>=50;rating<=5';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'And',
        conditions: [],
        conds: [
          {
            fieldId: number.id,
            fieldType: 'NUMBER',
            clause: {
              operator: 'IsGreater',
              value: 1,
            },
          },
          {
            fieldId: currency.id,
            fieldType: 'CURRENCY',
            clause: {
              operator: 'IsLess',
              value: 200.75,
            },
          },
          {
            fieldId: percent.id,
            fieldType: 'PERCENT',
            clause: {
              operator: 'IsGreaterEqual',
              value: 50,
            },
          },
          {
            fieldId: rating.id,
            fieldType: 'RATING',
            clause: {
              operator: 'IsLessEqual',
              value: 5,
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该有0条
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(0);
    };

    await isEmptyTest();
    await isNotEmptyTest();
    await isTest();
    await isNotTest();
    await comparisonTest();
  });

  /**
   * 复选框字段过滤语法测试
   */
  test('AST Filter Tests - checkbox field filter', async () => {
    // 复选框字段匹配
    const checkbox = database.getFieldByFieldKey(allFieldKeys.CHECKBOX);

    // 测试: 等于true -> `==`
    const isTrueTest = async () => {
      const filterAsRSql = 'checkbox==true';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'And',
        conditions: [],
        conds: [
          {
            fieldId: checkbox.id,
            fieldType: 'CHECKBOX',
            clause: {
              operator: 'Is',
              value: true,
            },
          },
        ],
      };

      expect(viewFilter).toEqual(expectedFilter);
      // 检查过滤后的结果, 应该有1条
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(1);
      expect(filteredRecords[0].getCellData(checkbox.id)).toBe(true);
    };

    // 测试: 等于false -> `==`
    const isFalseTest = async () => {
      const filterAsRSql = 'checkbox==false,checkbox==0';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'Or',
        conditions: [],
        conds: [
          {
            fieldId: checkbox.id,
            fieldType: 'CHECKBOX',
            clause: {
              operator: 'Is',
              value: false,
            },
          },
          {
            fieldId: checkbox.id,
            fieldType: 'CHECKBOX',
            clause: {
              operator: 'Is',
              value: false,
            },
          },
        ],
      };

      expect(viewFilter).toEqual(expectedFilter);
      // 检查过滤后的结果, 应该有1条
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(2);
      expect(filteredRecords[0].getCellData(checkbox.id)).toBe(false);
      expect(filteredRecords[1].getCellData(checkbox.id)).toBeUndefined();
    };

    await isTrueTest();
    await isFalseTest();
  });

  /**
   * 单选字段过滤语法测试
   */
  test('AST Filter Tests - single select field filter', async () => {
    const singleText = database.getFieldByFieldKey(allFieldKeys.SINGLE_TEXT);
    const singleSelect = database.getFieldByFieldKey(allFieldKeys.SINGLE_SELECT);

    // 测试: 为空 -> `==null` | `==NULL`
    const isEmptyTest = async () => {
      const filterAsRSql = 'single_select==null,single_select==NULL';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'Or',
        conditions: [],
        conds: [
          {
            fieldId: singleSelect.id,
            fieldType: 'SINGLE_SELECT',
            clause: {
              operator: 'IsEmpty',
            },
          },
          {
            fieldId: singleSelect.id,
            fieldType: 'SINGLE_SELECT',
            clause: {
              operator: 'IsEmpty',
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该只有一条空记录
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(1);
      expect(filteredRecords[0].getCellData(singleText.id)).toBeUndefined();
      expect(filteredRecords[0].getCellData(singleSelect.id)).toBeUndefined();
    };

    // 测试: 不为空 -> `!=null` | `!=NULL`
    const isNotEmptyTest = async () => {
      const filterAsRSql = 'single_select!=null,single_select!=NULL';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'Or',
        conditions: [],
        conds: [
          {
            fieldId: singleSelect.id,
            fieldType: 'SINGLE_SELECT',
            clause: {
              operator: 'IsNotEmpty',
            },
          },
          {
            fieldId: singleSelect.id,
            fieldType: 'SINGLE_SELECT',
            clause: {
              operator: 'IsNotEmpty',
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该有2条非空记录
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(2);
    };

    // 测试: 等于 -> `==`
    const isTest = async () => {
      const filterAsRSql = 'single_select=="S Option 1",single_select=="S Option 2"';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'Or',
        conditions: [],
        conds: [
          {
            fieldId: singleSelect.id,
            fieldType: 'SINGLE_SELECT',
            clause: {
              operator: 'Is',
              value: 'opt-s-1',
            },
          },
          {
            fieldId: singleSelect.id,
            fieldType: 'SINGLE_SELECT',
            clause: {
              operator: 'Is',
              value: 'opt-s-2',
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该有2条
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(2);
      expect(filteredRecords[0].getCellData(singleText.id)).toBe('text1');
      expect(filteredRecords[0].getCellData(singleSelect.id)).toEqual(['opt-s-1']);
    };

    // 测试: 不等于 -> `!=`
    const isNotTest = async () => {
      const filterAsRSql = 'single_select!="S Option 1"';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'And',
        conditions: [],
        conds: [
          {
            fieldId: singleSelect.id,
            fieldType: 'SINGLE_SELECT',
            clause: {
              operator: 'IsNot',
              value: 'opt-s-1',
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该有1条
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(2);
      expect(filteredRecords[0].getCellData(singleText.id)).toBe('text2');
      expect(filteredRecords[0].getCellData(singleSelect.id)).toEqual(['opt-s-2']);
      expect(filteredRecords[1].getCellData(singleText.id)).toBeUndefined();
      expect(filteredRecords[1].getCellData(singleSelect.id)).toBeUndefined();
    };

    // 测试: 包含 -> `=c=`
    const containsTest = async () => {
      const filterAsRSql = 'single_select=c=("S Option 1","S Option 2")';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'And',
        conditions: [],
        conds: [
          {
            fieldId: singleSelect.id,
            fieldType: 'SINGLE_SELECT',
            clause: {
              operator: 'Contains',
              value: ['opt-s-1', 'opt-s-2'],
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);
      // 检查过滤后的结果, 应该有2条
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(2);
      expect(filteredRecords[0].getCellData(singleText.id)).toBe('text1');
      expect(filteredRecords[0].getCellData(singleSelect.id)).toEqual(['opt-s-1']);
      expect(filteredRecords[1].getCellData(singleText.id)).toBe('text2');
      expect(filteredRecords[1].getCellData(singleSelect.id)).toEqual(['opt-s-2']);
    };

    // 测试: 不包含 -> `=nc=`
    const doesNotContainTest = async () => {
      const filterAsRSql = 'single_select=nc=("S Option 1","S Option 2")';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'And',
        conditions: [],
        conds: [
          {
            fieldId: singleSelect.id,
            fieldType: 'SINGLE_SELECT',
            clause: {
              operator: 'DoesNotContain',
              value: ['opt-s-1', 'opt-s-2'],
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该有2条
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(1);
      expect(filteredRecords[0].getCellData(singleText.id)).toBeUndefined();
      expect(filteredRecords[0].getCellData(singleSelect.id)).toBeUndefined();
    };

    await isEmptyTest();
    await isNotEmptyTest();
    await isTest();
    await isNotTest();
    await containsTest();
    await doesNotContainTest();
  });

  /**
   * 多选字段过滤语法测试
   */
  test('AST Filter Tests - multi select field filter', async () => {
    const singleText = database.getFieldByFieldKey(allFieldKeys.SINGLE_TEXT);
    const multiSelect = database.getFieldByFieldKey(allFieldKeys.MULTI_SELECT);

    // 测试: 为空 -> `==null` | `==NULL`
    const isEmptyTest = async () => {
      const filterAsRSql = 'multi_select==null,multi_select==NULL';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'Or',
        conditions: [],
        conds: [
          {
            fieldId: multiSelect.id,
            fieldType: 'MULTI_SELECT',
            clause: {
              operator: 'IsEmpty',
            },
          },
          {
            fieldId: multiSelect.id,
            fieldType: 'MULTI_SELECT',
            clause: {
              operator: 'IsEmpty',
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该只有一条空记录
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(1);
      expect(filteredRecords[0].getCellData(singleText.id)).toBeUndefined();
      expect(filteredRecords[0].getCellData(multiSelect.id)).toBeUndefined();
    };

    // 测试: 不为空 -> `!=null` | `!=NULL`
    const isNotEmptyTest = async () => {
      const filterAsRSql = 'multi_select!=null,multi_select!=NULL';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'Or',
        conditions: [],
        conds: [
          {
            fieldId: multiSelect.id,
            fieldType: 'MULTI_SELECT',
            clause: {
              operator: 'IsNotEmpty',
            },
          },
          {
            fieldId: multiSelect.id,
            fieldType: 'MULTI_SELECT',
            clause: {
              operator: 'IsNotEmpty',
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该有2条非空记录
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(2);
      expect(filteredRecords[0].getCellData(singleText.id)).toBe('text1');
      expect(filteredRecords[0].getCellValue(multiSelect.id)).toEqual(['M Option 1', 'M Option 2']);
      expect(filteredRecords[1].getCellData(singleText.id)).toBe('text2');
      expect(filteredRecords[1].getCellValue(multiSelect.id)).toEqual(['M Option 2', 'M Option 3']);
    };

    // 测试: 等于 -> `==`
    const isTest = async () => {
      const filterAsRSql = 'multi_select==("M Option 1","M Option 2")';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'And',
        conditions: [],
        conds: [
          {
            fieldId: multiSelect.id,
            fieldType: 'MULTI_SELECT',
            clause: {
              operator: 'Is',
              value: ['opt-m-1', 'opt-m-2'],
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该有1条
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(1);
      expect(filteredRecords[0].getCellData(singleText.id)).toBe('text1');
      expect(filteredRecords[0].getCellValue(multiSelect.id)).toEqual(['M Option 1', 'M Option 2']);
    };

    // 测试: 不等于 -> `!=`
    const isNotTest = async () => {
      const filterAsRSql = 'multi_select!=("M Option 1","M Option 2")';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'And',
        conditions: [],
        conds: [
          {
            fieldId: multiSelect.id,
            fieldType: 'MULTI_SELECT',
            clause: {
              operator: 'IsNot',
              value: ['opt-m-1', 'opt-m-2'],
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该有2条
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(2);
      expect(filteredRecords[0].getCellData(singleText.id)).toBe('text2');
      expect(filteredRecords[0].getCellValue(multiSelect.id)).toEqual(['M Option 2', 'M Option 3']);
      expect(filteredRecords[1].getCellData(singleText.id)).toBeUndefined();
      expect(filteredRecords[1].getCellData(multiSelect.id)).toBeUndefined();
    };

    // 测试: 包含 -> `=c=`
    const containsTest = async () => {
      const filterAsRSql = 'multi_select=c=("M Option 2")';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'And',
        conditions: [],
        conds: [
          {
            fieldId: multiSelect.id,
            fieldType: 'MULTI_SELECT',
            clause: {
              operator: 'Contains',
              value: ['opt-m-2'],
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该有2条
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(2);
      expect(filteredRecords[0].getCellData(singleText.id)).toBe('text1');
      expect(filteredRecords[0].getCellValue(multiSelect.id)).toEqual(['M Option 1', 'M Option 2']);
      expect(filteredRecords[1].getCellData(singleText.id)).toBe('text2');
      expect(filteredRecords[1].getCellValue(multiSelect.id)).toEqual(['M Option 2', 'M Option 3']);
    };

    // 测试: 不包含 -> `=nc=`
    const doesNotContainTest = async () => {
      const filterAsRSql = 'multi_select=nc=("M Option 2")';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'And',
        conditions: [],
        conds: [
          {
            fieldId: multiSelect.id,
            fieldType: 'MULTI_SELECT',
            clause: {
              operator: 'DoesNotContain',
              value: ['opt-m-2'],
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该有1条
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(1);
      expect(filteredRecords[0].getCellData(singleText.id)).toBeUndefined();
      expect(filteredRecords[0].getCellData(multiSelect.id)).toBeUndefined();
    };

    await isEmptyTest();
    await isNotEmptyTest();
    await isTest();
    await isNotTest();
    await containsTest();
    await doesNotContainTest();
  });

  /**
   * 日期时间字段过滤语法测试
   */
  test('AST Filter Tests - datetime field filter', async () => {
    const singleText = database.getFieldByFieldKey(allFieldKeys.SINGLE_TEXT);
    const datetime = database.getFieldByFieldKey(allFieldKeys.DATETIME);

    // 测试: 为空 -> `==null` | `==NULL`
    const isEmptyTest = async () => {
      const filterAsRSql = 'datetime==null,datetime==NULL';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'Or',
        conditions: [],
        conds: [
          {
            fieldId: datetime.id,
            fieldType: 'DATETIME',
            clause: {
              operator: 'IsEmpty',
            },
          },
          {
            fieldId: datetime.id,
            fieldType: 'DATETIME',
            clause: {
              operator: 'IsEmpty',
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该只有一条空记录
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(1);
      expect(filteredRecords[0].getCellData(singleText.id)).toBeUndefined();
      expect(filteredRecords[0].getCellData(datetime.id)).toBeUndefined();
    };

    // 测试: 不为空 -> `!=null` | `!=NULL`
    const isNotEmptyTest = async () => {
      const filterAsRSql = 'datetime!=null,datetime!=NULL';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql);
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'Or',
        conditions: [],
        conds: [
          {
            fieldId: datetime.id,
            fieldType: 'DATETIME',
            clause: {
              operator: 'IsNotEmpty',
            },
          },
          {
            fieldId: datetime.id,
            fieldType: 'DATETIME',
            clause: {
              operator: 'IsNotEmpty',
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该有2条非空记录
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(2);
      expect(filteredRecords[0].getCellData(singleText.id)).toBe('text1');
      expect(filteredRecords[0].getCellData(datetime.id)).toBe('2023-09-30T16:00:00.000Z');
      expect(filteredRecords[1].getCellData(singleText.id)).toBe('text2');
      expect(filteredRecords[1].getCellData(datetime.id)).toBe('2023-11-19T16:00:00.000Z');
    };

    // 测试: 等于 -> `==`
    const isExactTest = async () => {
      const filterAsRSql = 'datetime=="2023-10-01",datetime=="2023-11-20"';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql, { timeZone: 'Asia/Shanghai' });
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'Or',
        conditions: [],
        conds: [
          {
            fieldId: datetime.id,
            fieldType: 'DATETIME',
            clause: {
              operator: 'Is',
              value: ['ExactDate', '2023-09-30T16:00:00.000Z'],
            },
          },
          {
            fieldId: datetime.id,
            fieldType: 'DATETIME',
            clause: {
              operator: 'Is',
              value: ['ExactDate', '2023-11-19T16:00:00.000Z'],
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该有2条
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(2);
    };
    const isDateRangeTest = async () => {
      const filterAsRSql = 'datetime>=2023-10-01;datetime<=2023-11-20';
      const viewFilter = new AstFilter(user, database).toViewFilter(filterAsRSql, { timeZone: 'Asia/Shanghai' });
      // 转换后的过滤器
      const expectedFilter: ViewFilter = {
        conjunction: 'And',
        conditions: [],
        conds: [
          {
            fieldId: datetime.id,
            fieldType: 'DATETIME',
            clause: {
              operator: 'IsGreaterEqual',
              value: ['ExactDate', '2023-09-30T16:00:00.000Z'],
            },
          },
          {
            fieldId: datetime.id,
            fieldType: 'DATETIME',
            clause: {
              operator: 'IsLessEqual',
              value: ['ExactDate', '2023-11-19T16:00:00.000Z'],
            },
          },
        ],
      };
      expect(viewFilter).toEqual(expectedFilter);

      // 检查过滤后的结果, 应该有2条
      const filterQuery = await new FilterSO(database, viewFilter).buildQuery();
      const filteredRecords = await database.getRecordsAsPage({ filterQuery });
      expect(filteredRecords).toHaveLength(2);
      expect(filteredRecords[0].getCellData(singleText.id)).toBe('text1');
      expect(filteredRecords[0].getCellData(datetime.id)).toBe('2023-09-30T16:00:00.000Z');
      expect(filteredRecords[1].getCellData(singleText.id)).toBe('text2');
      expect(filteredRecords[1].getCellData(datetime.id)).toBe('2023-11-19T16:00:00.000Z');
    };

    await isEmptyTest();
    await isNotEmptyTest();
    await isExactTest();
    await isDateRangeTest();
  });
});
