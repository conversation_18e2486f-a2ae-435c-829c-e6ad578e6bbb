/**
 * BGrid是AG Grid的封装
 */
import type { ColTypeDef, FirstDataRenderedEvent, PaginationChangedEvent } from '@ag-grid-community/core';
import { CellValueChangedEvent } from '@ag-grid-community/core';
import React from 'react';
import z from 'zod';
import type { ViewField, ViewFilter, ViewGroupArray, ViewSortArray } from '@bika/types/database/bo';
import { RecordRenderVOSchema, type RecordRenderVO, type ViewVO, CellRenderVO } from '@bika/types/database/vo';
import { RowHeight } from '@bika/types/system/remote-storage';

export interface IColumnSortState {
  colId: string;
  sort?: 'asc' | 'desc' | null;
}

const batchUpdateEventSchema = z.object({
  type: z.literal('batchUpdate'),
  data: z.object({
    databaseId: z.string(),
    viewId: z.string().optional(),
    recordIds: z.array(z.string()),
  }),
});

const copyRowEventSchema = z.object({
  type: z.literal('copyRow'),
  data: RecordRenderVOSchema,
});

const BGridMenuEventSchema = z.discriminatedUnion('type', [batchUpdateEventSchema, copyRowEventSchema]);

export type BGridMenuEvent = z.infer<typeof BGridMenuEventSchema>;

export interface IBGridProps {
  spaceId: string;
  databaseId: string;
  // viewId: string;
  hasSequenceColumn?: boolean;

  view?: ViewVO;
  pagination?: boolean;
  mirrorId?: string;
  className?: string;
  style?: React.CSSProperties;
  rowGroup?: ViewGroupArray;

  customFieldList?: ViewField[];
  // 本地 临时状态，处理列宽数据状态问题
  tempFieldList?: ViewField[];

  handleUpdateFieldViewsSeq?: (newColIdLists: string[]) => void;
  handleUpdateViewFieldsWidth?: (width: number, colId: string, isReload?: boolean) => void;
  disableEditing: boolean;
  disableOperateColumn?: boolean;
  onMenuClick?: (data: BGridMenuEvent) => void;
  // 这个是resize之后，更新view的宽度事件
  onUpdateCustomFields: (fields: ViewField[]) => void;
  onUpdateViewFields?: (fields: ViewField[]) => void;
  viewSort?: ViewSortArray;
  rowHeight?: RowHeight;

  // columns: ViewFieldVO[];
  tipSharingLogin: () => void;

  // toolbar: React.ReactNode;

  // 做排序
  sorts: IColumnSortState[] | undefined;
  onSort: (term: string) => void;
  customViewFields?: ViewField[];
  onPaginationChanged?: (event: PaginationChangedEvent<RecordRenderVO>) => void;
  onFirstDataRendered?: (event: FirstDataRenderedEvent<RecordRenderVO>) => void;
  // 翻页行为
  pageSize?: number;
  setPageSize?: (pageSize: number) => void;
  cachedBlockSize?: number;
  onPaging: (curPage: number) => void;

  // 单元格值变化时间
  onCellValueChanged?: (event: CellValueChangedEvent) => void;
  page?: number;
  // initFilter: ViewFilter;
  // initSearchQuery: string;
  // 过滤器发生变化
  onFilterChanged?: (filter: ViewFilter) => void;
  // 初始化filter
  filter?: ViewFilter;

  // 搜索字符串
  searchKeywords?: string;

  isTemplatePreview?: boolean;

  // timezone: string | undefined;
}

export const customColumnTypes: Record<string, ColTypeDef<RecordRenderVO>> = {
  ATTACHMENT: {},
  SINGLE_TEXT: {},
  LONG_TEXT: {},
  DATETIME: {},
  CREATED_TIME: {},
  CREATED_BY: {},
  LOOKUP: {},
  ONE_WAY_LINK: {},
  AI_TEXT: {},
  NUMBER: {},
  MEMBER: {},
  CHECKBOX: {},
  SINGLE_SELECT: {},
  PHONE: {},
  EMAIL: {},
  RATING: {},
  URL: {},
  MULTI_SELECT: {},
  LINK: {},
  CURRENCY: {},
  PERCENT: {},
  FORMULA: {},
};

export type CellRenderVOWithIndex = CellRenderVO & {
  index: number;
};

export type { DatabaseImportStatus, CollaborationStatus, RecordCreatedStatus } from '@bika/ui/snackbar';
