// eslint-disable-next-line max-classes-per-file
import { OpenAPICellRenderOpts, StandardCellValue } from '@bika/types/database/vo';
import { CellSO } from './cell-so';
import { FieldSO } from '../fields/field-so';
import { EmailFieldSO, LongTextFieldSO, PhoneFieldSO, SingleTextFieldSO, URLFieldSO } from '../fields/text-field';

/**
 * 文本单元格对象基类
 */
abstract class BaseTextCellSO<T extends FieldSO> extends CellSO<T, string, string> {
  override doValue(): string | undefined {
    // TODO: 此处会导致存储空间翻倍，待移除
    return this.originDataValue;
  }

  override getValue(): string | undefined {
    const data = this.getData();
    return data ? `${data}` : undefined;
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string | null {
    const text = this.getValue();
    return text ? `${text}` : null;
  }
}

export class SingleTextCellSO extends BaseTextCellSO<SingleTextFieldSO> {
  override getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }
}

export class LongTextCellSO extends BaseTextCellSO<LongTextFieldSO> {
  override getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }
}

export class URLCellSO extends BaseTextCellSO<URLFieldSO> {
  override getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }
}

export class EmailCellSO extends BaseTextCellSO<EmailFieldSO> {
  override getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }
}

export class PhoneCellSO extends BaseTextCellSO<PhoneFieldSO> {
  override getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }
}
