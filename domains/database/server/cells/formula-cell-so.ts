import _ from 'lodash';
import { FieldT<PERSON><PERSON>elper, FormulaBaseError, FormulaHelper } from '@bika/domains/database/shared';
import { DatabaseRecordModel } from '@bika/server-orm';
import { DatabaseFieldWithId, DatabaseFormulaField, CellValue } from '@bika/types/database/bo';
import { CellRenderOpts, CellRenderVO, OpenAPICellRenderOpts, StandardCellValue } from '@bika/types/database/vo';
import { CellSO } from './cell-so';
import { FieldSO } from '../fields/field-so';
import { FormulaInterpreter } from '../fields/formula';
import { FormulaFieldSO } from '../fields/formula-field';

/**
 * 公式单元格
 */
export class FormulaCellSO extends CellSO<FormulaFieldSO, CellValue, string | string[]> {
  temporaryValue: string | string[] | undefined;

  override getStdCellValue(): StandardCellValue {
    return {
      type: 'FORMULA',
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override doCompute() {
    const helper = FieldTypeHelper.create(this._field.toBO(), { fields: this._otherFields.map((f) => f.toBO()) });
    if (!helper.isAvailable()) {
      // 让计算不抛异常, 只返回是否可用
      // TODO: 但是这个方法跟下面evaluate方法执行有重复解析动作, 需要合并操作
      this.temporaryValue = undefined;
      return undefined;
    }

    // console.log(
    //   `公式字段重新计算: id=${this._field.id}, name=${this._field.name}, 单元格数据: ${JSON.stringify(this._model)}`,
    // );

    const result = this.evaluate(
      this._field.toBO() as DatabaseFieldWithId<DatabaseFormulaField>,
      this._otherFields,
      this._model as DatabaseRecordModel,
    );

    if (Array.isArray(result)) {
      this.temporaryValue = result.map((item) => String(item));
    } else {
      this.temporaryValue = String(result);
    }
    // TODO: 修复类型差异
    return result as CellValue;
  }

  override doValue() {
    return this.temporaryValue;
  }

  /**
   * 计算单元格的值
   */
  private evaluate(
    formulaFieldBO: DatabaseFormulaField,
    databaseFields: FieldSO[],
    record: DatabaseRecordModel,
  ): CellValue {
    const expr = formulaFieldBO.property.expression;
    if (!expr) {
      // 公式表达式不存在, 返回空
      return null;
    }
    // 当前的字段BO
    const currentFieldBO = { id: this._field.id, ...formulaFieldBO };
    const fExpr = FormulaHelper.parse(expr, {
      field: currentFieldBO,
      fields: databaseFields.map((field) => field.toBO()),
    });
    if (!fExpr.success) {
      // 默认错误返回 null
      return null;
    }

    try {
      // 公式翻译
      const interpreter = new FormulaInterpreter({
        field: currentFieldBO,
        fields: databaseFields.map((field) => field.toBO()),
        record,
      });
      // 解释结果值
      const result = interpreter.visit(fExpr.ast);
      // 处理结果值
      if (_.isNumber(result) && !Number.isFinite(result)) {
        throw new FormulaBaseError('NaN');
      }
      // TODO: 修复类型差异
      return result as CellValue;
    } catch (error) {
      if (error instanceof FormulaBaseError) {
        // 解析错误展示到单元格
        return error.message;
      }
    }

    return null;
  }

  override toRenderVO(opts?: CellRenderOpts): Promise<CellRenderVO> {
    // 不可用的公式表达式全部输出为#Error!
    const fieldBOList = this._otherFields.map((field) => field.toBO());
    const helper = FieldTypeHelper.create(this._field.toBO(), { fields: fieldBOList });
    const isAvailable = helper.isAvailable();
    const data = this.getData() ?? null;
    const value = this.getValue() ?? null;
    return Promise.resolve({
      id: this._field.id,
      name: this._field.getName(opts?.locale),
      data: isAvailable ? data : '#Error!',
      value: isAvailable ? value : '#Error!',
    });
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string | string[] | null {
    // 公式单元格值, 只返回计算结果
    const value = this.getValue(opts);
    if (opts?.cellFormat === 'string') {
      // 返回字符串格式
      if (Array.isArray(value)) {
        return value.join(', ');
      }
      return value?.toString() ?? null;
    }
    return value ?? null;
  }
}
