// eslint-disable-next-line max-classes-per-file
import { isArrayOfType } from '@bika/domains/shared/shared';
import { DatabaseFieldConfigSelectOption } from '@bika/types/database/bo';
import { CellRenderOpts, CellValueVO, CellVO, OpenAPICellRenderOpts, StandardCellValue } from '@bika/types/database/vo';
import { iString, iStringParse } from '@bika/types/system';
import { CellSO } from './cell-so';
import { MultiSelectFieldSO, SingleSelectFieldSO } from '../fields/select-field';

abstract class BaseSelectCellSO<T extends SingleSelectFieldSO | MultiSelectFieldSO> extends CellSO<
  T,
  string[],
  iString[]
> {
  doValue(): iString[] | undefined {
    const { originDataValue } = this;
    if (originDataValue) {
      const arr: iString[] = [];
      originDataValue.forEach((val) => {
        const option = this.findOptionById(val);
        if (option) {
          arr.push(option.name);
        }
      });
      return arr;
    }
    return undefined;
  }

  protected findOptionById(id: string): DatabaseFieldConfigSelectOption | null {
    const { options } = this._field.property;
    if (!options.length) {
      return null;
    }
    return options.find((option) => option.id === id) ?? null;
  }

  override getCellValueVO(opts?: CellRenderOpts): CellValueVO {
    // 这里竟然在分组时, 违反行记录数据上下文一致性, 强制覆盖value值, 错误的做法
    // TODO: 先临时解决, 后续在分组加载记录时重构删除掉
    const data = this.getModelData();
    const value = this.getModelValue();
    if (isArrayOfType(data, (v) => typeof v === 'string')) {
      return data.map((optId, index) => {
        const option = this.findOptionById(optId);
        if (option) {
          return iStringParse(option.name, opts?.locale);
        }
        return value?.[index] ?? String(optId);
      });
    }
    return data ?? null;
  }

  override toVO(opts?: CellRenderOpts): CellVO {
    const vo = super.toVO(opts);

    if (isArrayOfType(vo.data, (v) => typeof v === 'string')) {
      vo.value = vo.data.map((v) => {
        const option = this.findOptionById(v);
        if (option) {
          return iStringParse(option.name, opts?.locale);
        }
        return String(v);
      });
    }

    return vo;
  }
}

/**
 * 单选字段，Option IDs(string)
 */
export class SingleSelectCellSO extends BaseSelectCellSO<SingleSelectFieldSO> {
  override getStdCellValue(): StandardCellValue {
    return {
      type: 'SINGLE_SELECT',
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string | null {
    // 输出为选项名称
    const data = this.getModelData();
    if (data && data.length === 1) {
      const option = this.findOptionById(data[0]);
      if (option) {
        return iStringParse(option.name, opts?.locale);
      }
      return null;
    }
    return null;
  }
}

/**
 * 多选字段，Option IDs(string)
 */
export class MultiSelectCellSO extends BaseSelectCellSO<MultiSelectFieldSO> {
  override getStdCellValue(): StandardCellValue {
    return {
      type: 'MULTI_SELECT',
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string[] | string | null {
    // 输出为选项名称数组
    const data = this.getModelData();
    if (data && data.length > 0) {
      const values = data
        .map((optId) => {
          const option = this.findOptionById(optId);
          if (option) {
            return iStringParse(option.name, opts?.locale);
          }
          return null;
        })
        .filter((v) => v !== null);
      if (opts?.cellFormat === 'string') {
        return values.join(', ');
      }
      return values;
    }
    return null;
  }
}
