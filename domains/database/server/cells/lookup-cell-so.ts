import assert from 'assert';
import { isArray, isArrayOfType } from '@bika/domains/shared';
import { db } from '@bika/server-orm';
import {
  LookupFieldFormattingProperty,
  NumberFieldProperty,
  AttachmentCellDataSchema,
  CellValues,
  LookUpCellValue,
  DocCellDataSchema,
  AttachmentCellData,
} from '@bika/types/database/bo';
import { CellRenderOpts, CellValueVO, OpenAPICellRenderOpts, StandardCellValue } from '@bika/types/database/vo';
import { OpenAPIFieldCellValue } from '@bika/types/openapi/vo';
import { DateTimeFieldProperty, iString, iStringParse } from '@bika/types/system';
import {
  formatDateRangeCellValue,
  formatDateTimeCellValue,
  formatCurrencyCellValue,
  formatNumberCellValue,
  formatPercentCellValue,
} from '@bika/types/system/formatting';
import { CellSO } from './cell-so';
import { DatabaseSO } from '../database-so';
import { Aggregate } from '../fields/aggregate/aggregate';
import { LookupRecord } from '../fields/aggregate/types';
import { FieldSOFactory } from '../fields/field-factory';
import { LinkFieldSO } from '../fields/link-field';
import { LookupFieldSO } from '../fields/lookup-field';
import { FieldCellRenderModel } from '../types';

/**
 * 查找引用单元格
 */
export class LookupCellSO extends CellSO<LookupFieldSO, LookUpCellValue, CellValues> {
  // TODO: 临时解决, 下一步重构
  dataChanged: boolean = false;

  temporaryData: LookUpCellValue | undefined;

  temporaryValue: CellValues | undefined;

  async doCompute(): Promise<LookUpCellValue | undefined> {
    const {
      relatedLinkFieldId,
      lookupTargetFieldId,
      rollUpType = 'VALUES',
      lookUpLimit = 'ALL',
    } = this._field.property;
    assert(
      relatedLinkFieldId && lookupTargetFieldId,
      'LookupFieldSO must have relatedLinkFieldId and lookupTargetFieldId',
    );

    // 拿到对应关联字段
    const linkFieldSO = this._otherFields.find((field) => field.id === relatedLinkFieldId) as LinkFieldSO;
    // 关联字段所选的值
    const linkRecordIds = this._model.data[relatedLinkFieldId!];
    if (!isArrayOfType(linkRecordIds, (v) => typeof v === 'string') || !linkRecordIds.length) {
      // 没有选中任何值, 或者者选中的值不是数组
      // console.log(`==================> 当前行ID: ${this._model.id}, 没有任何记录`);
      // 执行计算, 没有值, 则默认清空当前lookup单元格值
      this.dataChanged = true;
      return undefined;
    }
    const { foreignDatabaseId } = linkFieldSO.property;
    const foreignDatabase = await DatabaseSO.init(foreignDatabaseId!);
    // 引用的关联表字段
    const lookupTargetField = foreignDatabase.findFieldByFieldKey(lookupTargetFieldId);
    if (!lookupTargetField) {
      // 引用的关联表字段不存在
      return undefined;
    }
    // 引用的关联表字段的记录
    const linkedRecords = await foreignDatabase.getRecords(linkRecordIds, db.mongo.currentSession() ?? undefined);
    // 按照原来的顺序构造记录
    const lookupRecords: LookupRecord[] = await Promise.all(
      linkRecordIds.map(async (linkRecordId) => {
        const linkedRecord = linkedRecords.find((record) => record.id === linkRecordId);
        const cellRenderModel = await linkedRecord?.getCellRenderModel(lookupTargetFieldId);
        const fieldCellModel: FieldCellRenderModel = {
          bo: lookupTargetField.toBO(),
          cell: cellRenderModel ?? {},
        };
        return [linkRecordId, lookupTargetFieldId, fieldCellModel];
      }),
    );
    // const database = await this._field.getDatabase();
    // console.log(
    //   `==================> 当前行ID: ${this._model.id}, 引用字段单元格更新: ${database.name} - ${this._field.name} - ${this._field.id}`,
    // );
    // console.log(JSON.stringify(lookupRecords));
    const aggregate = new Aggregate(lookupTargetField.toBO(), { rollUpType, lookUpLimit });
    const cellModel = aggregate.compute(lookupRecords);
    // console.log(`==================> 当前行ID: ${this._model.id}, 计算结果: ${JSON.stringify(cellModel)}`);
    this.dataChanged = true;
    this.temporaryData = cellModel.data as LookUpCellValue;
    this.temporaryValue = cellModel.values;
    return cellModel.computed as LookUpCellValue;
  }

  public getData(): LookUpCellValue | undefined {
    // TODO 重构记录操作代码时会删掉
    if (this.dataChanged) {
      return this.temporaryData;
    }
    return super.getData();
  }

  doValue(): (iString | null)[] | undefined {
    if (isArray(this.temporaryValue)) {
      return this.temporaryValue;
    }
    return undefined;
  }

  getStdCellValue(): StandardCellValue {
    return {
      type: 'LOOKUP',
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  private lookupCellValueToCellValue(value: LookUpCellValue): string | string[] | null {
    if (Array.isArray(value)) {
      // 数组类型
      return value
        .map((v) => {
          if (typeof v === 'string') {
            // 字符串类型
            return v;
          }
          if (typeof v === 'number') {
            // 数字类型
            return String(v);
          }
          if (typeof v === 'boolean') {
            // 布尔类型
            return `${v}`;
          }
          return null;
        })
        .filter((v) => v !== null);
    }
    if (typeof value === 'string') {
      // 字符串类型
      return value;
    }
    if (typeof value === 'number') {
      // 数字类型
      return String(value);
    }
    if (typeof value === 'boolean') {
      // 布尔类型
      return `${value}`;
    }
    return null;
  }

  /**
   * 格式化聚合计算值
   * 开启聚合计算后, 计算结果数据类型符合条件的, 可按用户配置来格式化
   */
  private formatComputedValue(): string | string[] | null {
    const { formatting } = this._field.property;
    const computed = this.getModelComputed();
    if (!formatting) {
      // 结果不可格式化, 直接返回结果
      return computed ? this.lookupCellValueToCellValue(computed) : null;
    }
    if (typeof computed === 'number') {
      // 数字类型, 直接格式化
      if (formatting.type === 'NUMBER') {
        return formatNumberCellValue(computed, formatting.property);
      }
      if (formatting.type === 'CURRENCY') {
        return formatCurrencyCellValue(computed, formatting.property);
      }
      if (formatting.type === 'PERCENT') {
        return formatPercentCellValue(computed, formatting.property);
      }
    }
    if (typeof computed === 'boolean') {
      // 布尔类型, 直接返回字符串
      return computed ? 'true' : 'false';
    }
    return null;
  }

  private mergeNumberFormatting(
    source: NumberFieldProperty,
    formatting?: LookupFieldFormattingProperty,
  ): NumberFieldProperty {
    // 只有引用数字类型的字段自定义引用后格式化可合并
    if (formatting?.type === 'NUMBER' || formatting?.type === 'CURRENCY' || formatting?.type === 'PERCENT') {
      return formatting.property;
    }
    return source;
  }

  private mergeDateFormatting(
    source: DateTimeFieldProperty,
    formatting?: LookupFieldFormattingProperty,
  ): DateTimeFieldProperty {
    // 只有引用日期类型的字段自定义引用后格式化可合并
    if (formatting?.type === 'DATETIME') {
      return formatting.property;
    }
    return source;
  }

  override async getCellValueVO(opts?: CellRenderOpts): Promise<CellValueVO> {
    const { lookupTargetFieldId, rollUpType = 'VALUES', formatting } = this._field.property;
    if (!lookupTargetFieldId) {
      // 没有引用的字段, 直接返回null
      return null;
    }
    if (rollUpType !== 'VALUES') {
      // 原值引用才需要实时加载数据, 不然都是计算完的结果存储, 直接取computed值作为value
      return this.formatComputedValue();
    }
    // 下面都是原始值引用, 忽略formatting属性, 都是以对方的格式来显示
    const lookupTargetField = await FieldSOFactory.getField(lookupTargetFieldId);
    if (!lookupTargetField) {
      // 引用的字段不存在
      return null;
    }
    const lookupTargetFieldBO = lookupTargetField.toBO();
    // 日期/数字, 单元格值需实时格式化, 以防每次修改配置触发整列单元格更新从而导致放大更新
    if (
      lookupTargetFieldBO.type === 'DATETIME' ||
      lookupTargetFieldBO.type === 'CREATED_TIME' ||
      lookupTargetFieldBO.type === 'MODIFIED_TIME'
    ) {
      const dateTimeISOStrings = this.getModelData();
      if (isArrayOfType(dateTimeISOStrings, (v) => typeof v === 'string')) {
        const cellValue: string[] = [];
        // 空值忽略
        for (const isoString of dateTimeISOStrings) {
          // 如果不是零时区的ISO字符串, 输出也要保持转换成零时区的ISO字符串
          const safeDateIsoString = this.safeConvertToUtcIsoString(isoString);
          const dateCellValue = formatDateTimeCellValue(
            safeDateIsoString,
            this.mergeDateFormatting(lookupTargetFieldBO.property, formatting),
            opts,
          );
          if (dateCellValue) {
            cellValue.push(dateCellValue);
          }
        }

        return cellValue;
      }
    }

    if (lookupTargetFieldBO.type === 'DATERANGE') {
      const dateTimeISOStrings = this.getModelData();
      if (isArrayOfType(dateTimeISOStrings, (v) => typeof v === 'string')) {
        const cellValue: string[] = [];
        // 空值忽略
        for (const isoString of dateTimeISOStrings) {
          const dateCellValue = formatDateRangeCellValue(
            isoString,
            this.mergeDateFormatting(lookupTargetFieldBO.property, formatting),
            opts,
          );
          if (dateCellValue) {
            cellValue.push(dateCellValue);
          }
        }

        return cellValue;
      }
    }

    if (lookupTargetFieldBO.type === 'NUMBER') {
      const numbers = this.getModelData();
      if (isArrayOfType(numbers, (v) => typeof v === 'number')) {
        const cellValue: string[] = [];
        // 空值忽略
        for (const number of numbers) {
          const numberCellValue = formatNumberCellValue(
            number,
            this.mergeNumberFormatting(lookupTargetFieldBO.property, formatting),
          );
          if (numberCellValue) {
            cellValue.push(numberCellValue);
          }
        }
        return cellValue;
      }
    }

    if (lookupTargetFieldBO.type === 'CURRENCY') {
      const numbers = this.getModelData();
      if (isArrayOfType(numbers, (v) => typeof v === 'number')) {
        const cellValue: string[] = [];
        // 空值忽略
        for (const number of numbers) {
          const currencyCellValue = formatCurrencyCellValue(
            number,
            this.mergeNumberFormatting(lookupTargetFieldBO.property, formatting),
          );
          if (currencyCellValue) {
            cellValue.push(currencyCellValue);
          }
        }
        return cellValue;
      }
    }

    if (lookupTargetFieldBO.type === 'PERCENT') {
      const numbers = this.getModelData();
      if (isArrayOfType(numbers, (v) => typeof v === 'number')) {
        const cellValue: string[] = [];
        // 空值忽略
        for (const number of numbers) {
          const percentCellValue = formatPercentCellValue(
            number,
            this.mergeNumberFormatting(lookupTargetFieldBO.property, formatting),
          );
          if (percentCellValue) {
            cellValue.push(percentCellValue);
          }
        }
        return cellValue;
      }
    }

    // 目标字段是成员/创建人/修改人/附件/文档, 需要实时加载数据替换value
    if (lookupTargetFieldBO.type === 'MEMBER') {
      // 得到最新的成员信息
      const unitIds = this.getModelData();
      if (isArrayOfType(unitIds, (v) => typeof v === 'string')) {
        const units = await this.loadUnitCellValues(Array.from(new Set(unitIds)), opts);
        // 按原来的顺序返回
        return unitIds
          .map((unitId) => {
            const unit = units.find((u) => u.id === unitId);
            return unit ?? null;
          })
          .filter((v) => v !== null);
      }
    }
    if (lookupTargetFieldBO.type === 'CREATED_BY' || lookupTargetFieldBO.type === 'MODIFIED_BY') {
      // 引用了创建人/修改人, data就存了userId
      const userIds = this.getModelData();
      if (isArrayOfType(userIds, (v) => typeof v === 'string')) {
        const users = await this.loadUserCellValues(Array.from(new Set(userIds)));
        return userIds
          .map((userId) => {
            const user = users.find((u) => u.id === userId);
            return user ?? null;
          })
          .filter((v) => v !== null);
      }
    }
    if (lookupTargetFieldBO.type === 'ATTACHMENT') {
      // 引用了附件, 动态得到附件的各个附件的临时下载地址
      const attachments = this.getModelData();
      if (Array.isArray(attachments) && attachments.length > 0) {
        const correctAttachments: AttachmentCellData[] = [];
        for (const attachment of attachments) {
          const attachmentParse = AttachmentCellDataSchema.safeParse(attachment);
          if (attachmentParse.success) {
            correctAttachments.push(attachmentParse.data);
          }
        }
        return this.loadAttachmentCellValues(correctAttachments);
      }
    }
    if (lookupTargetFieldBO.type === 'WORK_DOC') {
      // 文档引用
      const docs = this.getModelData();
      if (Array.isArray(docs) && docs.length > 0) {
        const docIds: string[] = [];
        for (const doc of docs) {
          const docParse = DocCellDataSchema.safeParse(doc);
          if (docParse.success) {
            docIds.push(docParse.data.docId);
          }
        }
        return this.loadDocCellValues(docIds, opts);
      }
    }

    // 其他类型直接返回
    return super.getCellValueVO(opts);
  }

  override async toOpenAPICellValue(opts?: OpenAPICellRenderOpts): Promise<OpenAPIFieldCellValue> {
    const { lookupTargetFieldId, rollUpType = 'VALUES', formatting } = this._field.property;
    if (!lookupTargetFieldId) {
      // 没有引用的字段, 直接返回null
      return null;
    }
    if (rollUpType !== 'VALUES') {
      // 原值引用才需要实时加载数据, 不然都是计算完的结果存储, 直接取computed值作为value
      return this.formatComputedValue();
    }
    // 下面都是原始值引用, 忽略formatting属性, 都是以对方的格式来显示
    const lookupTargetField = await FieldSOFactory.getField(lookupTargetFieldId);
    if (!lookupTargetField) {
      // 引用的字段不存在
      return null;
    }
    const lookupTargetFieldBO = lookupTargetField.toBO();
    // 日期/数字, 单元格值需实时格式化, 以防每次修改配置触发整列单元格更新从而导致放大更新
    if (
      lookupTargetFieldBO.type === 'DATETIME' ||
      lookupTargetFieldBO.type === 'CREATED_TIME' ||
      lookupTargetFieldBO.type === 'MODIFIED_TIME'
    ) {
      const dateTimeISOStrings = this.getModelData();
      if (isArrayOfType(dateTimeISOStrings, (v) => typeof v === 'string')) {
        if (opts?.cellFormat === 'json') {
          // 返回源数据
          return dateTimeISOStrings;
        }
        const cellValue: string[] = [];
        // 空值忽略
        for (const isoString of dateTimeISOStrings) {
          // 如果不是零时区的ISO字符串, 输出也要保持转换成零时区的ISO字符串
          const safeDateIsoString = this.safeConvertToUtcIsoString(isoString);
          const dateCellValue = formatDateTimeCellValue(
            safeDateIsoString,
            this.mergeDateFormatting(lookupTargetFieldBO.property, formatting),
            opts,
          );
          if (dateCellValue) {
            cellValue.push(dateCellValue);
          }
        }

        return cellValue.join(', ');
      }
    }

    if (lookupTargetFieldBO.type === 'DATERANGE') {
      const dateTimeISOStrings = this.getModelData();
      if (isArrayOfType(dateTimeISOStrings, (v) => typeof v === 'string')) {
        if (opts?.cellFormat === 'json') {
          // 返回源数据
          return dateTimeISOStrings;
        }
        const cellValue: string[] = [];
        // 空值忽略
        for (const isoString of dateTimeISOStrings) {
          const dateCellValue = formatDateRangeCellValue(
            isoString,
            this.mergeDateFormatting(lookupTargetFieldBO.property, formatting),
            opts,
          );
          if (dateCellValue) {
            cellValue.push(dateCellValue);
          }
        }

        return cellValue.join(', ');
      }
    }

    if (lookupTargetFieldBO.type === 'NUMBER') {
      const numbers = this.getModelData();
      if (isArrayOfType(numbers, (v) => typeof v === 'number')) {
        if (opts?.cellFormat === 'json') {
          // 返回源数据
          return numbers;
        }
        const cellValue: string[] = [];
        // 空值忽略
        for (const number of numbers) {
          const numberCellValue = formatNumberCellValue(
            number,
            this.mergeNumberFormatting(lookupTargetFieldBO.property, formatting),
          );
          if (numberCellValue) {
            cellValue.push(numberCellValue);
          }
        }
        return cellValue.join(', ');
      }
    }

    if (lookupTargetFieldBO.type === 'CURRENCY') {
      const numbers = this.getModelData();
      if (isArrayOfType(numbers, (v) => typeof v === 'number')) {
        if (opts?.cellFormat === 'json') {
          // 返回源数据
          return numbers;
        }
        const cellValue: string[] = [];
        // 空值忽略
        for (const number of numbers) {
          const currencyCellValue = formatCurrencyCellValue(
            number,
            this.mergeNumberFormatting(lookupTargetFieldBO.property, formatting),
          );
          if (currencyCellValue) {
            cellValue.push(currencyCellValue);
          }
        }
        return cellValue.join(', ');
      }
    }

    if (lookupTargetFieldBO.type === 'PERCENT') {
      const numbers = this.getModelData();
      if (isArrayOfType(numbers, (v) => typeof v === 'number')) {
        if (opts?.cellFormat === 'json') {
          // 返回源数据
          return numbers;
        }
        const cellValue: string[] = [];
        // 空值忽略
        for (const number of numbers) {
          const percentCellValue = formatPercentCellValue(
            number,
            this.mergeNumberFormatting(lookupTargetFieldBO.property, formatting),
          );
          if (percentCellValue) {
            cellValue.push(percentCellValue);
          }
        }
        return cellValue.join(', ');
      }
    }

    // 目标字段是成员/创建人/修改人/附件/文档, 需要实时加载数据替换value
    if (lookupTargetFieldBO.type === 'MEMBER') {
      // 得到最新的成员信息
      const unitIds = this.getModelData();
      if (isArrayOfType(unitIds, (v) => typeof v === 'string')) {
        const units = await this.loadOpenAPIMemberCellValues(Array.from(new Set(unitIds)), opts);
        if (opts?.cellFormat === 'string') {
          return units.map((unit) => unit.name).join(', ');
        }
        // 按原来的顺序返回
        return units;
      }
    }
    if (lookupTargetFieldBO.type === 'CREATED_BY' || lookupTargetFieldBO.type === 'MODIFIED_BY') {
      // 引用了创建人/修改人, data就存了userId
      const userIds = this.getModelData();
      if (isArrayOfType(userIds, (v) => typeof v === 'string')) {
        const users = await this.loadOpenAPIUserCellValues(Array.from(new Set(userIds)));
        if (opts?.cellFormat === 'string') {
          return users.map((user) => user.name).join(', ');
        }
        return users;
      }
    }
    if (lookupTargetFieldBO.type === 'ATTACHMENT') {
      // 引用了附件, 动态得到附件的各个附件的临时下载地址
      const attachments = this.getModelData();
      if (Array.isArray(attachments) && attachments.length > 0) {
        const correctAttachments: AttachmentCellData[] = [];
        for (const attachment of attachments) {
          const attachmentParse = AttachmentCellDataSchema.safeParse(attachment);
          if (attachmentParse.success) {
            correctAttachments.push(attachmentParse.data);
          }
        }
        const attaches = await this.loadOpenAPIAttachmentCellValues(correctAttachments);
        if (opts?.cellFormat === 'string') {
          return attaches.map((attachment) => `${attachment.name}(${attachment.url})`).join(', ');
        }
        return attaches;
      }
    }
    if (lookupTargetFieldBO.type === 'WORK_DOC') {
      // 文档引用
      const docs = this.getModelData();
      if (Array.isArray(docs) && docs.length > 0) {
        const docIds: string[] = [];
        for (const doc of docs) {
          const docParse = DocCellDataSchema.safeParse(doc);
          if (docParse.success) {
            docIds.push(docParse.data.docId);
          }
        }
        const docNames = await this.loadDocCellValues(docIds, opts);
        if (opts?.cellFormat === 'string') {
          return docNames.join(', ');
        }
        return docNames;
      }
    }

    // 其他类型直接返回
    const value = this.getValue(opts);
    if (Array.isArray(value)) {
      // 数组类型
      const cellValue = value.map((v) => (typeof v === 'string' ? v : iStringParse(v, opts?.locale)));
      if (opts?.cellFormat === 'string') {
        return cellValue.join(', ');
      }
      return cellValue;
    }
    return iStringParse(value, opts?.locale);
  }
}
