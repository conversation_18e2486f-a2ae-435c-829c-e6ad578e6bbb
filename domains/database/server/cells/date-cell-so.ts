// eslint-disable-next-line max-classes-per-file
import _ from 'lodash';
import { DatabaseDateTimeField } from '@bika/types/database/bo';
import { CellRenderOpts, CellValueVO, CellVO, OpenAPICellRenderOpts, StandardCellValue } from '@bika/types/database/vo';
import { OpenAPIFieldCellValue } from '@bika/types/openapi/vo';
import { DateTimeSO } from '@bika/types/system';
import { formatDateRangeCellValue, formatDateTimeCellValue } from '@bika/types/system/formatting';
import { CellSO } from './cell-so';
import { CellValueConvertorFactory } from './cellvalue-convertor';
import { DateRangeFieldSO } from '../fields/date-range-field';
import { CreatedTimeFieldSO, DateTimeFieldSO, ModifiedTimeFieldSO } from '../fields/date-time-field';

/**
 * 日期时间单元格
 */
export class DateTimeCellSO extends CellSO<DateTimeFieldSO, string, string> {
  getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  doValue() {
    const { property } = this._field;

    // 处理识别时区
    if (property.timeZone === 'AUTO') {
      // TODO: 获取用户
      property.timeZone = undefined;
    }

    const cellValue = this.getData(); // ISO string
    if (cellValue) {
      const datetime = DateTimeSO.fromISOString(cellValue);
      const fmt = datetime.format(property);
      return fmt;
    }
    return undefined;
  }

  override getValue(opts?: CellRenderOpts): string | null {
    const modelData = this.getModelData();
    if (!modelData) {
      return null;
    }
    // 按配置格式
    const safeDateIsoString = this.safeConvertToUtcIsoString(modelData);
    return formatDateTimeCellValue(safeDateIsoString, this._field.property, opts);
  }

  override getCellValueVO(opts?: CellRenderOpts): CellValueVO {
    // 日期格式化
    const modelData = this.getModelData(); // ISO string
    if (!modelData) {
      return null;
    }
    const safeDateIsoString = this.safeConvertToUtcIsoString(modelData);
    return formatDateTimeCellValue(safeDateIsoString, this._field.property, opts);
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string | null {
    const modelData = this.getModelData(); // ISO string
    if (!modelData) {
      return null;
    }
    const safeDateIsoString = this.safeConvertToUtcIsoString(modelData);
    if (opts?.cellFormat === 'string') {
      // 返回格式化好的
      return formatDateTimeCellValue(safeDateIsoString, this._field.property, opts);
    }
    // 返回 ISO 字符串
    return safeDateIsoString;
  }

  /**
   * 由于字段更改时区，format时候没有修改values的值，所以需要覆写这个方法
   * todo: 修改日期字段的时候，需要更新values的值
   */
  public override toVO(opts?: CellRenderOpts): CellVO {
    const vo = super.toVO(opts);
    vo.data = this.getData();
    if (vo.data && (_.isString(vo.data) || _.isNumber(vo.data))) {
      const fieldBO = this._field.toBO() as DatabaseDateTimeField & { id: string };
      const { timeZone } = fieldBO.property;
      if ((timeZone === 'AUTO' || !timeZone) && opts?.timeZone) {
        // 当前用户时区
        fieldBO.property = {
          ...fieldBO.property,
          timeZone: opts.timeZone,
        };
      }
      const cvt = CellValueConvertorFactory.create(fieldBO, {
        fields: this._otherFields.map((f) => f.toBO()),
      });
      vo.value = cvt.cellValueToString(vo.data);
    }
    return vo;
  }
}

/**
 * 创建时间单元格
 */
export class CreatedTimeCellSO extends CellSO<CreatedTimeFieldSO, string, string> {
  getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  doValue() {
    const { createdAt } = this._model;
    const { property } = this._field;

    // 处理识别时区
    if (property.timeZone === 'AUTO') {
      // TODO: 获取用户
      property.timeZone = undefined;
    }

    const datetime = new DateTimeSO(createdAt);
    return datetime.format(property);
  }

  override getData(): string | undefined {
    // 以防没存, 如果没有, 取记录的创建时间
    const data = super.getData();
    if (data) {
      return data;
    }
    return this._model.createdAt?.toISOString();
  }

  override getValue(): string | undefined {
    // 以防没存, 如果没有, 取记录的创建时间
    const data = this.getModelData();
    if (data) {
      const datetime = DateTimeSO.fromISOString(data);
      return datetime.format(this._field.property);
    }
    // 按配置格式
    const datetime = new DateTimeSO(this._model.createdAt);
    return datetime.format(this._field.property);
  }

  override getCellValueVO(opts?: CellRenderOpts): string | null {
    // 日期格式化
    const modelData = this.getData(); // ISO string/ISO string
    if (!modelData) {
      return null;
    }
    const safeDateIsoString = this.safeConvertToUtcIsoString(modelData);
    return formatDateTimeCellValue(safeDateIsoString, this._field.property, opts);
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string | null {
    const modelData = this.getModelData();
    if (!modelData) {
      return null;
    }
    const safeDateIsoString = this.safeConvertToUtcIsoString(modelData);
    if (opts?.cellFormat === 'string') {
      // 返回格式化好的
      return formatDateTimeCellValue(safeDateIsoString, this._field.property, opts);
    }
    // 返回 ISO 字符串
    return safeDateIsoString;
  }

  override toVO(opts?: CellRenderOpts): CellVO {
    const vo = super.toVO(opts);

    vo.data = this._model.createdAt?.toISOString() ?? null;

    const cvt = CellValueConvertorFactory.create(this._field.toBO(), {
      fields: this._otherFields.map((f) => f.toBO()),
    });
    vo.value = cvt.cellValueToString(vo.data);

    return vo;
  }
}

/**
 * 修改时间单元格
 */
export class ModifiedTimeCellSO extends CellSO<ModifiedTimeFieldSO, string, string> {
  getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override getData(): string | undefined {
    // 以防没存, 如果没有, 取记录的创建时间
    const data = super.getData();
    if (data) {
      return data;
    }
    return this._model.updatedAt?.toISOString();
  }

  override getValue(): string | undefined {
    // 以防没存, 如果没有, 取记录的创建时间
    const { timeZone } = this._field.property;
    const newTimeZone: string | undefined = timeZone === 'AUTO' ? undefined : timeZone;
    const data = this.getModelData();
    if (data) {
      const datetime = DateTimeSO.fromISOString(data);
      return datetime.format({ ...this._field.property, timeZone: newTimeZone });
    }
    if (!this._model.updatedAt) {
      // 没有更新时间? 那没办法了, 直接返回空
      return undefined;
    }
    // 按配置格式
    const datetime = new DateTimeSO(this._model.updatedAt);
    return datetime.format({ ...this._field.property, timeZone: newTimeZone });
  }

  override getCellValueVO(opts?: CellRenderOpts): string | null {
    // 日期格式化
    const modelData = this.getData(); // ISO string
    if (!modelData) {
      return null;
    }
    const safeDateIsoString = this.safeConvertToUtcIsoString(modelData);
    return formatDateTimeCellValue(safeDateIsoString, this._field.property, opts);
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): OpenAPIFieldCellValue | Promise<OpenAPIFieldCellValue> {
    const modelData = this.getModelData(); // ISO string
    if (!modelData) {
      return null;
    }
    const safeDateIsoString = this.safeConvertToUtcIsoString(modelData);
    if (opts?.cellFormat === 'string') {
      // 按配置格式
      return formatDateTimeCellValue(safeDateIsoString, this._field.property, opts);
    }
    // 返回 ISO 字符串
    return safeDateIsoString;
  }

  doValue() {
    const { updatedAt } = this._model;
    const { property } = this._field;

    // 处理识别时区
    if (property.timeZone === 'AUTO') {
      // TODO: 获取用户
      property.timeZone = undefined;
    }

    const datetime = new DateTimeSO(updatedAt);
    return datetime.format(property);
  }

  override toVO(opts?: CellRenderOpts): CellVO {
    const vo = super.toVO(opts);

    vo.data = this._model.updatedAt?.toISOString() ?? null;

    const cvt = CellValueConvertorFactory.create(this._field.toBO(), {
      fields: this._otherFields.map((f) => f.toBO()),
    });
    vo.value = cvt.cellValueToString(vo.data);

    return vo;
  }
}

/**
 * 日期范围单元格
 */
export class DateRangeCellSO extends CellSO<DateRangeFieldSO, string, string> {
  getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  doValue() {
    const { property } = this._field;
    const cellValue = this.getData(); // `{ISO-String}/{ISO-String}`
    if (cellValue) {
      const [[startDate, endDate], err] = this._field.parseDateRangeString(cellValue);
      if (err) {
        // 日期范围格式不正确
        throw new Error('Invalid date range format');
      }

      const start = DateTimeSO.fromISOString(startDate).format(property);
      const end = DateTimeSO.fromISOString(endDate).format(property);
      return `${start} -> ${end}`;
    }
    return undefined;
  }

  override getCellValueVO(opts?: CellRenderOpts): string | null {
    // 日期范围格式化
    const modelData = this.getModelData(); // ISO string/ISO string
    if (!modelData) {
      return null;
    }
    return formatDateRangeCellValue(modelData, this._field.property, opts);
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string | null {
    const modelData = this.getModelData(); // ISO string/ISO string
    if (!modelData) {
      return null;
    }
    if (opts?.cellFormat === 'string') {
      // 返回格式化好的
      return formatDateRangeCellValue(modelData, this._field.property, opts);
    }
    return modelData;
  }
}
