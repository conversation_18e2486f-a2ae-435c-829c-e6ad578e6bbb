/* eslint-disable max-classes-per-file */
import _ from 'lodash';
import { AttachmentFieldSO } from '@bika/domains/database-fields/attachment/server';
import { diffArray } from '@bika/domains/shared/shared';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { UnitFactory } from '@bika/domains/unit/server/unit-factory';
import { CellValue, AttachmentCellData, DocCellData } from '@bika/types/database/bo';
import {
  MemberCellValue,
  StandardCellValue,
  CellRenderOpts,
  UserCellValue,
  AttachmentCellValue,
  OpenAPICellRenderOpts,
} from '@bika/types/database/vo';
import { OpenAPIAttachmentCellValue, OpenAPIMemberCellValue, OpenAPIUserCellValue } from '@bika/types/openapi/vo';
import { CellSO } from './cell-so';
import { CheckBoxFieldSO } from '../fields/checkbox-field';
import {
  AIPhotoFieldSO,
  AIVideoFieldSO,
  AIVoiceFieldSO,
  APIFieldSO,
  ButtonFieldSO,
  CascaderFieldSO,
  CutVideoFieldSO,
  JSONFieldSO,
  PhotoFieldSO,
  VideoFieldSO,
  VoiceFieldSO,
} from '../fields/default-field';
import { WorkDocFieldSO } from '../fields/doc-field';
import { MemberFieldSO } from '../fields/member-field';
import { AiTextFieldSO } from '../fields/text-field';
import { CreatedByFieldSO, ModifiedByFieldSO } from '../fields/user-field';

/**
 * 创建人
 */
export class CreatedByCellSO extends CellSO<CreatedByFieldSO, string, string> {
  getData(): string {
    if (this._model.createdBy) {
      return this._model.createdBy;
    }
    const modelData = this._model.data?.[this._field.id];
    // 判断是否存的是数组
    if (_.isArray(modelData)) {
      return modelData[0];
    }
    if (typeof modelData === 'string') {
      return modelData;
    }
    return '';
  }

  getValue(): string {
    const modelValue = this._model.values?.[this._field.id];
    // 判断是否存的是数组
    if (_.isArray(modelValue)) {
      return modelValue[0];
    }
    if (typeof modelValue === 'string') {
      return modelValue;
    }
    return '';
  }

  async getStdCellValue(): Promise<StandardCellValue> {
    // 创建人昵称不会存储在数据中, 只会实时拉取最新的成员昵称
    let newValue: string | undefined = this.getModelValue();
    if (this._model.createdBy) {
      const name = await MemberSO.getNameByUserId(this._model.createdBy, this._model.spaceId);
      newValue = name ?? undefined;
    }
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: newValue,
    };
  }

  override async doValue(): Promise<string | undefined> {
    const value = this.getModelValue();
    if (value) {
      return value;
    }
    if (this._model.createdBy) {
      const name = await MemberSO.getNameByUserId(this._model.createdBy, this._model.spaceId);
      return name ?? undefined;
    }
    return undefined;
  }

  override async getCellValueVO(_opts?: CellRenderOpts): Promise<UserCellValue | null> {
    const { createdBy } = this._model;
    if (createdBy) {
      return this.loadUserCellValue(createdBy);
    }
    return null;
  }

  override async toOpenAPICellValue(opts?: OpenAPICellRenderOpts): Promise<OpenAPIUserCellValue | string | null> {
    const { createdBy } = this._model;
    if (createdBy) {
      const userCellValue = await this.loadOpenAPIUserCellValue(createdBy, opts?.locale);
      if (opts?.cellFormat === 'string') {
        return userCellValue.name;
      }
      return userCellValue;
    }
    return null;
  }
}

/**
 * 修改人
 */
export class ModifiedByCellSO extends CellSO<ModifiedByFieldSO, string, string> {
  override getData(): string {
    if (this._model.updatedBy) {
      return this._model.updatedBy;
    }
    const modelData = this._model.data?.[this._field.id];
    // 判断是否存的是数组
    if (_.isArray(modelData)) {
      return modelData[0];
    }
    if (typeof modelData === 'string') {
      return modelData;
    }
    return '';
  }

  override getValue(): string {
    const modelValue = this._model.values?.[this._field.id];
    // 判断是否存的是数组
    if (_.isArray(modelValue)) {
      return modelValue[0];
    }
    if (typeof modelValue === 'string') {
      return modelValue;
    }
    return '';
  }

  override async doValue(): Promise<string | undefined> {
    const value = this.getModelValue();
    if (value) {
      return value;
    }
    if (this._model.updatedBy) {
      const name = await MemberSO.getNameByUserId(this._model.updatedBy, this._model.spaceId);
      return name ?? undefined;
    }
    return undefined;
  }

  override async getStdCellValue(): Promise<StandardCellValue> {
    // 修改人昵称不会存储在数据中, 只会实时拉取最新的成员昵称
    let newValue: string | undefined = this.getModelValue();
    if (this._model.updatedBy) {
      const name = await MemberSO.getNameByUserId(this._model.updatedBy, this._model.spaceId);
      newValue = name ?? undefined;
    }
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: newValue,
    };
  }

  override async getCellValueVO(_opts?: CellRenderOpts): Promise<UserCellValue | null> {
    const { updatedBy } = this._model;
    if (updatedBy) {
      return this.loadUserCellValue(updatedBy);
    }
    return null;
  }

  override async toOpenAPICellValue(opts?: OpenAPICellRenderOpts): Promise<OpenAPIUserCellValue | string | null> {
    const { updatedBy } = this._model;
    if (updatedBy) {
      const userCellValue = await this.loadOpenAPIUserCellValue(updatedBy, opts?.locale);
      if (opts?.cellFormat === 'string') {
        return userCellValue.name;
      }
      return userCellValue;
    }
    return null;
  }
}

/**
 * 成员
 */
export class MemberCellSO extends CellSO<MemberFieldSO, string[], string[]> {
  override async doValue(): Promise<string[] | undefined> {
    // TODO: 传入不正确的 member id，没有抛出异常
    const { originDataValue } = this;
    if (!originDataValue) {
      return undefined;
    }
    const unitIds = originDataValue;
    if (!Array.isArray(originDataValue)) {
      return undefined;
    }
    const units = await UnitFactory.getUnitsOnSpace(this._model.spaceId, unitIds, true);
    return units.map((unit) => unit.getName());
  }

  async getStdCellValue(): Promise<StandardCellValue> {
    // 成员昵称不会存储在数据中, 只会实时拉取最新的昵称
    const unitIds = this.getModelData();
    let newValue: string[] | undefined = this.getModelValue();
    if (unitIds && unitIds.length > 0) {
      // 可能是空数组
      const units = await this.loadUnitCellValues(Array.from(new Set(unitIds)));
      // 按原来的顺序返回
      newValue = await Promise.all(
        unitIds.map((id) => {
          const unit = units.find((u) => u.id === id);
          if (unit) {
            return unit.name;
          }
          return '';
        }),
      );
    }
    return {
      type: this._field.type,
      property: this._field.property,
      data: unitIds,
      value: newValue,
    };
  }

  override async getCellValueVO(opts?: CellRenderOpts): Promise<MemberCellValue[]> {
    const modelData = this.getModelData();
    if (!modelData || modelData.length === 0) {
      return [];
    }
    const unitIds = new Set(modelData);
    // const units = await UnitFactory.getUnitsByUnitIds(this._model.spaceId, Array.from(unitIds));
    const units = await this.loadUnitCellValues(Array.from(unitIds), opts);
    const value: MemberCellValue[] = [];
    for (const id of unitIds) {
      const unit = units.find((u) => u.id === id);
      if (unit) {
        value.push(unit);
      } else {
        value.push({
          id,
          name: '',
          avatar: undefined,
          deleted: true,
        });
      }
    }
    return value;
  }

  override async toOpenAPICellValue(opts?: OpenAPICellRenderOpts): Promise<OpenAPIMemberCellValue[] | string | null> {
    const modelData = this.getModelData();
    if (!modelData || modelData.length === 0) {
      return [];
    }
    const unitIds = new Set(modelData);
    // const units = await UnitFactory.getUnitsByUnitIds(this._model.spaceId, Array.from(unitIds));
    const units = await this.loadOpenAPIMemberCellValues(Array.from(unitIds), opts);
    if (opts?.cellFormat === 'string') {
      return units.map((unit) => unit.name).join(', ');
    }
    return units;
  }
}

/**
 * 复选框
 */
export class CheckboxCellSO extends CellSO<CheckBoxFieldSO, boolean, string> {
  doValue() {
    return this.originDataValue ? '1' : '0';
  }

  getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): boolean | string | null {
    const cellValue = this.getData();
    if (opts?.cellFormat === 'string') {
      return cellValue ? 'true' : 'false';
    }
    return !!cellValue;
  }
}

/**
 * 附件
 */
export class AttachmentCellSO extends CellSO<AttachmentFieldSO, AttachmentCellData[], string[]> {
  doValue() {
    if (!this.originDataValue) {
      return undefined;
    }
    return this.originDataValue.map((attachment) => attachment.name);
  }

  getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  /**
   * 获取单元格数据的差异
   * 什么情况会发生差异呢? 可参考`diffArray`的单测
   * 1. 数组元素发生变化
   * 2. 数组顺序发生变化
   * @param newAttachmentIds 新的附件ID列表
   * @returns 返回差异
   */
  getDiffAttachmentIds(newAttachmentIds: string[]): {
    added: string[];
    removed: string[];
    changed: boolean;
  } {
    const oldCellData = this.getData();
    if (!oldCellData) {
      // 原数据没有值, 取决于新数据是否也为空, 为空则代表没有任何变动
      return { added: newAttachmentIds, removed: [], changed: newAttachmentIds.length > 0 };
    }
    // 提取原附件ID列表
    const oldAttachmentIds = oldCellData.map((attachment) => attachment.id);
    return diffArray(oldAttachmentIds, newAttachmentIds);
  }

  override async getCellValueVO(_opts?: CellRenderOpts): Promise<AttachmentCellValue[]> {
    const attachments = this.getModelData();
    if (!attachments) {
      return [];
    }
    return this.loadAttachmentCellValues(attachments);
  }

  override async toOpenAPICellValue(
    opts?: OpenAPICellRenderOpts,
  ): Promise<OpenAPIAttachmentCellValue[] | string | null> {
    const attachments = this.getModelData();
    if (!attachments || attachments.length === 0) {
      return null;
    }
    const cellValues = await this.loadOpenAPIAttachmentCellValues(attachments);
    if (opts?.cellFormat === 'string') {
      return cellValues.map((attachment) => `${attachment.name}(${attachment.url})`).join(', ');
    }
    return cellValues;
  }
}

/**
 * 文档
 */
export class WorkDocCellSO extends CellSO<WorkDocFieldSO, DocCellData, string> {
  getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override async getCellValueVO(opts?: CellRenderOpts): Promise<string | null> {
    const doc = this.getModelData();
    if (!doc) {
      return null;
    }
    const docName = await this.loadDocCellValue(doc.docId, opts);
    // 强制覆盖文档名称
    if (docName) {
      this._model.data[this._field.id] = {
        ...doc,
        name: docName,
      };
    }
    return docName;
  }

  override async toOpenAPICellValue(opts?: OpenAPICellRenderOpts): Promise<DocCellData | string | null> {
    const doc = this.getModelData();
    if (!doc) {
      return null;
    }
    const docName = await this.loadDocCellValue(doc.docId, opts);
    if (opts?.cellFormat === 'string') {
      return docName ?? '';
    }
    return { ...doc, name: docName ?? '' };
  }
}
export class VideoCellSO extends CellSO<VideoFieldSO, string, string> {
  override getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string | null {
    return this.getValue(opts) ?? null;
  }
}

export class VoiceCellSO extends CellSO<VoiceFieldSO, string, string> {
  override getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string | null {
    return this.getValue(opts) ?? null;
  }
}
export class PhotoCellSO extends CellSO<PhotoFieldSO, string, string> {
  override getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string | null {
    return this.getValue(opts) ?? null;
  }
}
export class APICellSO extends CellSO<APIFieldSO, string, string> {
  override getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string | null {
    return this.getValue(opts) ?? null;
  }
}
export class AITextCellSO extends CellSO<AiTextFieldSO, CellValue, string> {
  override doValue(): string {
    const cellValue = String(this.getData());
    let newCellValue = JSON.stringify(cellValue);
    if (newCellValue[0] === '"' && newCellValue[newCellValue.length - 1] === '"') {
      // 去掉首尾的双引号
      newCellValue = newCellValue.slice(1, -1);
    }
    return newCellValue;
  }

  override getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string | null {
    return this.getValue(opts) ?? null;
  }
}
export class AIVoiceCellSO extends CellSO<AIVoiceFieldSO, string, string> {
  override getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string | null {
    return this.getValue(opts) ?? null;
  }
}
export class AIPhotoCellSO extends CellSO<AIPhotoFieldSO, string, string> {
  override getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string | null {
    return this.getValue(opts) ?? null;
  }
}
export class AIVideoCellSO extends CellSO<AIVideoFieldSO, string, string> {
  override getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string | null {
    return this.getValue(opts) ?? null;
  }
}
export class CutVideoCellSO extends CellSO<CutVideoFieldSO, string, string> {
  override getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string | null {
    return this.getValue(opts) ?? null;
  }
}
export class JSONCellSO extends CellSO<JSONFieldSO, string, string> {
  override getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string | null {
    return this.getValue(opts) ?? null;
  }
}
export class CascaderCellSO extends CellSO<CascaderFieldSO, string, string> {
  override getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string | null {
    return this.getValue(opts) ?? null;
  }
}
export class ButtonCellSO extends CellSO<ButtonFieldSO, string, string> {
  override getStdCellValue(): StandardCellValue {
    return {
      type: this._field.type,
      property: this._field.property,
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): string | null {
    return this.getValue(opts) ?? null;
  }
}
