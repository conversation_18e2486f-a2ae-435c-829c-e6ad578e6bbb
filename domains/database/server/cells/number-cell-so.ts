/* eslint-disable max-classes-per-file */
import { isNullOrUndefined } from '@bika/domains/shared/shared';
import {
  AutoNumberFieldPropertySchema,
  NumberFieldPropertySchema,
  RatingFieldPropertySchema,
} from '@bika/types/database/bo';
import { CellRenderOpts, OpenAPICellRenderOpts, StandardCellValue } from '@bika/types/database/vo';
import { formatCurrencyCellValue, formatNumberCellValue, formatPercentCellValue } from '@bika/types/system/formatting';
import { CellSO } from './cell-so';
import {
  AutoIncrementFieldSO,
  CurrencyFieldSO,
  NumberFieldSO,
  PercentFieldSO,
  RatingFieldSO,
} from '../fields/number-field';

abstract class AbstractNumberCellSO<
  T extends AutoIncrementFieldSO | NumberFieldSO | CurrencyFieldSO | PercentFieldSO | RatingFieldSO,
> extends CellSO<T, number, string> {}

export class AutoNumberCellSO extends AbstractNumberCellSO<AutoIncrementFieldSO> {
  doValue(): string | undefined {
    const cellValue = this.getData();
    if (!cellValue) {
      return undefined;
    }
    return cellValue.toString();
  }

  getStdCellValue(): StandardCellValue {
    return {
      type: 'AUTO_NUMBER',
      property: AutoNumberFieldPropertySchema.parse(this._field.property),
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): number | string | null {
    if (opts?.cellFormat === 'string') {
      return this.getValue() ?? null;
    }
    const cellData = this.getModelData();
    return isNullOrUndefined(cellData) ? null : cellData;
  }
}

/**
 * 货币
 */
export class CurrencyCellSO extends AbstractNumberCellSO<CurrencyFieldSO> {
  doValue() {
    const cellData = this.getData();
    if (isNullOrUndefined(cellData)) {
      return undefined;
    }
    return formatCurrencyCellValue(cellData, this._field.property) ?? undefined;
  }

  override getValue(): string | null | undefined {
    // 有可能数字没有格式化, 强制格式化返回
    const cellData = this.getData();
    if (isNullOrUndefined(cellData)) {
      return null;
    }
    return formatCurrencyCellValue(cellData, this._field.property);
  }

  getStdCellValue(): StandardCellValue {
    return {
      type: 'CURRENCY',
      property: NumberFieldPropertySchema.parse(this._field.property),
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override getCellValueVO(_opts?: CellRenderOpts): string | null {
    // 货币格式化
    const modelData = this.getModelData();
    if (modelData === undefined) {
      return null;
    }
    return formatCurrencyCellValue(modelData, this._field.property);
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): number | string | null {
    const cellData = this.getModelData();
    if (isNullOrUndefined(cellData)) {
      return null;
    }
    if (opts?.cellFormat === 'string') {
      return formatCurrencyCellValue(cellData, this._field.property);
    }

    return cellData;
  }
}

export class NumberCellSO extends AbstractNumberCellSO<NumberFieldSO> {
  override doValue(): string | undefined {
    const cellData = this.getData();
    if (isNullOrUndefined(cellData)) {
      return undefined;
    }
    return formatNumberCellValue(cellData, this._field.property) ?? undefined;
  }

  override getValue(): string | null | undefined {
    // 有可能数字没有格式化, 强制格式化返回
    const cellData = this.getData();
    if (isNullOrUndefined(cellData)) {
      return null;
    }
    return formatNumberCellValue(cellData, this._field.property);
  }

  override getStdCellValue(): StandardCellValue {
    return {
      type: 'NUMBER',
      property: NumberFieldPropertySchema.parse(this._field.property),
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override getCellValueVO(_opts?: CellRenderOpts): string | null {
    // 数字格式化
    const modelData = this.getModelData();
    if (modelData === undefined) {
      return null;
    }
    return formatNumberCellValue(modelData, this._field.property);
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): number | string | null {
    const cellData = this.getModelData();
    if (isNullOrUndefined(cellData)) {
      return null;
    }
    if (opts?.cellFormat === 'string') {
      return formatNumberCellValue(cellData, this._field.property);
    }

    return cellData;
  }
}

export class PercentCellSO extends AbstractNumberCellSO<PercentFieldSO> {
  override doValue(): string | undefined {
    const cellData = this.getData();
    if (isNullOrUndefined(cellData)) {
      return undefined;
    }
    return formatPercentCellValue(cellData, this._field.property) ?? undefined;
  }

  override getValue(): string | null | undefined {
    // 有可能数字没有格式化, 强制格式化返回
    const cellData = this.getData();
    if (isNullOrUndefined(cellData)) {
      return null;
    }
    return formatPercentCellValue(cellData, this._field.property);
  }

  override getStdCellValue(): StandardCellValue {
    return {
      type: 'PERCENT',
      property: NumberFieldPropertySchema.parse(this._field.property),
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override getCellValueVO(_opts?: CellRenderOpts): string | null {
    // 百分比格式化
    const modelData = this.getModelData();
    if (modelData === undefined) {
      return null;
    }
    return formatPercentCellValue(modelData, this._field.property);
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): number | string | null {
    const cellData = this.getModelData();
    if (isNullOrUndefined(cellData)) {
      return null;
    }
    if (opts?.cellFormat === 'string') {
      return formatPercentCellValue(cellData, this._field.property);
    }

    return cellData;
  }
}

export class RatingCellSO extends AbstractNumberCellSO<RatingFieldSO> {
  override doValue(): string | undefined {
    const cellValue = this.getData();
    if (!cellValue) {
      return undefined;
    }
    return cellValue.toString();
  }

  override getStdCellValue(): StandardCellValue {
    return {
      type: 'RATING',
      property: RatingFieldPropertySchema.parse(this._field.property),
      data: this.getData(),
      value: this.getValue(),
    };
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): number | string | null {
    const cellData = this.getModelData();
    if (isNullOrUndefined(cellData)) {
      return null;
    }
    if (opts?.cellFormat === 'string') {
      return cellData.toString();
    }

    return cellData;
  }
}
