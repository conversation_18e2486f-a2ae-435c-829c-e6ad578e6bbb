// eslint-disable-next-line max-classes-per-file
import { db } from '@bika/server-orm';
import { OneWayLinkFieldPropertySchema } from '@bika/types/database/bo';
import { OpenAPICellRenderOpts, StandardCellValue } from '@bika/types/database/vo';
import { OpenAPIFieldCellValue } from '@bika/types/openapi/vo';
import { CellSO } from './cell-so';
import { diffArray } from '../../../shared/shared';
import { DatabaseSO } from '../database-so';
import { LinkFieldSO, OneWayLinkFieldSO } from '../fields/link-field';
import { RecordSO } from '../record-so';

abstract class BaseLinkCellSO<T extends LinkFieldSO | OneWayLinkFieldSO> extends CellSO<
  T,
  string[],
  (string | null)[]
> {
  override async doValue(): Promise<(string | null)[] | undefined> {
    if (!this.originDataValue || this.originDataValue.length === 0) {
      return undefined;
    }
    const recordIds = this.getData();
    if (!recordIds || recordIds.length === 0) {
      return undefined;
    }
    const linkProperty = this._field.property;
    const databaseSO = await DatabaseSO.init(linkProperty.foreignDatabaseId!);
    const records = await databaseSO.getRecords(recordIds, db.mongo.currentSession() ?? undefined);
    const idToRecordMap = new Map(records.map((record: RecordSO) => [record.id, record]));

    // 按照recordIds的顺序获取主要单元格值
    const primaryCellValues = recordIds.map((id) => {
      const record = idToRecordMap.get(id);
      const primaryCellValue = record?.getPrimaryCellValue();
      return primaryCellValue ? (primaryCellValue as string) : null;
    });
    return primaryCellValues;
  }

  override toOpenAPICellValue(opts?: OpenAPICellRenderOpts): OpenAPIFieldCellValue | Promise<OpenAPIFieldCellValue> {
    if (opts?.cellFormat === 'string') {
      // 如果是字符串格式，返回逗号分隔的字符串
      return this.getValue(opts)?.join(', ') ?? null;
    }
    // 否则返回记录ID数组
    return this.getData() ?? [];
  }
}

/**
 * 双向关联单元格
 */
export class LinkCellSO extends BaseLinkCellSO<LinkFieldSO> {
  override getStdCellValue(): StandardCellValue {
    const value = this.getValue();
    return {
      type: 'LINK',
      property: this._field.property,
      data: this.getData(),
      // 过滤掉null值
      value: value && value.filter((v) => v !== null),
    };
  }

  /**
   * 获取单元格数据的差异
   * 什么情况会发生差异呢? 可参考`diffArray`的单测
   * 1. 数组元素发生变化
   * 2. 数组顺序发生变化
   * @param newCellData 新单元格数据
   * @returns 返回差异
   */
  getDiffCellData(newCellData: string[]): {
    added: string[];
    removed: string[];
    changed: boolean;
  } {
    const oldCellData = this.getData();
    if (!oldCellData) {
      // 原数据没有值, 取决于新数据是否也为空, 为空则代表没有任何变动
      return { added: newCellData, removed: [], changed: newCellData.length > 0 };
    }
    return diffArray(oldCellData, newCellData);
  }
}

/**
 * 单向关联单元格
 */
export class OneWayLinkCellSO extends BaseLinkCellSO<OneWayLinkFieldSO> {
  override getStdCellValue(): StandardCellValue {
    const value = this.getValue();
    return {
      type: 'ONE_WAY_LINK',
      property: OneWayLinkFieldPropertySchema.parse(this._field.property),
      data: this.getData(),
      // 过滤掉null值
      value: value && value.filter((v) => v !== null),
    };
  }
}
