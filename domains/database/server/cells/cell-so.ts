import assert from 'assert';
import { AttachmentSO } from '@bika/domains/attachment/server';
import { DocSO } from '@bika/domains/doc/server/doc-so';
import { MemberSO, UnitFactory } from '@bika/domains/unit/server';
import type { AttachmentCellData, CellValue, CellValues } from '@bika/types/database/bo';
import type {
  CellRenderOpts,
  CellVO,
  CellRenderVO,
  StandardCellValue,
  CellValueVO,
  MemberCellValue,
  UserCellValue,
  AttachmentCellValue,
  OpenAPICellRenderOpts,
} from '@bika/types/database/vo';
import {
  OpenAPIAttachmentCellValue,
  OpenAPIFieldCellValue,
  OpenAPIMemberCellValue,
  OpenAPIUserCellValue,
} from '@bika/types/openapi/vo';
import { DateTimeSO, Locale } from '@bika/types/system';
import { CellRecordModel } from './types';
import { FieldSO } from '../fields/field-so';

/**
 * 单元格对象基类
 */
export abstract class CellSO<TField extends FieldSO, TData extends CellValue, TValue extends CellValues> {
  protected _model: CellRecordModel;

  // 单元格对应的字段对象
  protected _field: TField;

  /**
   * 指同个Record的其它字段(Fields)
   */
  protected _otherFields: FieldSO[];

  constructor(model: CellRecordModel, field: TField, otherFields: FieldSO[]) {
    this._model = model;
    this._field = field;
    this._otherFields = otherFields;
  }

  protected get originDataValue(): TData | undefined {
    assert(this._model);
    return this.getModelData();
  }

  /**
   * 获取 Model 的 data
   */
  public getModelData(): TData | undefined {
    return this._model.data?.[this._field.id];
  }

  /**
   * 获取 Model 的 computed
   */
  public getModelComputed(): TData | undefined {
    return this._model.computed?.[this._field.id];
  }

  /**
   * 获取 Model 的 values
   */
  public getModelValue(): TValue | undefined {
    return this._model.values?.[this._field.id];
  }

  /**
   * Get Data = 获取computed或data的值
   * 优先获取 computed
   */
  public getData(): TData | undefined {
    const modelComputed = this.getModelComputed();
    // console.log(`获取指定字段单元格: ${this._field.id}, modelComputed: ${modelComputed}`);
    if (modelComputed !== undefined) {
      // 如果有computed, 则使用computed
      return modelComputed;
    }
    return this.getModelData();
  }

  /**
   * Get Value = 获取model.values的值
   */
  public getValue(_opts?: CellRenderOpts): TValue | undefined | null {
    return this.getModelValue() ?? null;
  }

  /**
   * 输出标准单元格值对象
   * 原封不动把data/value组装出来供转换,注意事项:
   * 1. 关联字段的data是一个记录ID数组, value是对应的记录的首列单元格数据(也可能没有)
   * 2. Lookup字段的data是实际值, value跟其一致
   * 3. 公式字段的data是实际值, value跟其一致
   * 4. 其他字段的data是非渲染值(例如单多选的optionId), value是实际值, 而且都是单值或者数组, 数据类型应该匹配
   * 5. 触发IO操作的字段单元格有: 成员/创建人/修改人
   */
  abstract getStdCellValue(): StandardCellValue | Promise<StandardCellValue>;

  /**
   * 进行计算，默认不进行
   * computed 只存储公式和查找引用聚合计算的值
   * 如果查找引用没设置聚合计算, 则computed为空
   * 如果公式有效, 则computed的值可能是数组, 也可能非数组
   * 目前看起来, 只有引用字段需要IO操作, 公式字段不需要跨表, 只用本行依赖直接计算
   * @deprecated 要废弃
   */
  public doCompute(): TData | undefined | Promise<TData | undefined> {
    return undefined;
  }

  /**
   * 如果这个字段，渲染值，与存储值不一样，则需要覆写这个方法
   * 目前在看起来, 只有 创建人/修改人/成员字段/关联字段 需要IO操作
   * @deprecated 要废弃
   */
  public doValue(): TValue | undefined | Promise<TValue | undefined> {
    return undefined;
  }

  /**
   * 内部业务单元格值VO, 不可用于接口返回前端渲染
   * @param opts 渲染选项
   * @returns CellVO
   */
  public toVO(opts?: CellRenderOpts): CellVO {
    return {
      id: this._field.id,
      name: this._field.getName(opts?.locale),
      fieldType: this._field.type,
      data: this.getData() ?? null,
      value: this.getValue(opts) ?? null,
    };
  }

  /**
   * 单元格渲染值VO
   * @param opts 渲染选项
   */
  getCellValueVO(opts?: CellRenderOpts): CellValueVO | Promise<CellValueVO> {
    // 默认使用values值
    return this.getValue(opts) ?? null;
  }

  /**
   * 单元格渲染视图
   * 通常需要实时加载数据的字段单元格值才需要覆盖
   */
  async toRenderVO(opts?: CellRenderOpts): Promise<CellRenderVO> {
    const cellValueVO = await this.getCellValueVO(opts);
    return {
      id: this._field.id,
      name: this._field.getName(opts?.locale),
      data: this.getData() ?? null,
      value: cellValueVO,
    };
  }

  abstract toOpenAPICellValue(opts?: OpenAPICellRenderOpts): OpenAPIFieldCellValue | Promise<OpenAPIFieldCellValue>;

  protected safeConvertToUtcIsoString(value: string) {
    if (DateTimeSO.isStrictUtcIsoString(value)) {
      return value;
    }
    const dateTime = DateTimeSO.safeParseDateTimeString(value);
    return dateTime.toISOString();
  }

  protected async loadUnitCellValues(unitIds: string[], opts?: CellRenderOpts): Promise<MemberCellValue[]> {
    const units = await UnitFactory.getUnitsByUnitIds(this._model.spaceId, Array.from(unitIds));
    return Promise.all(units.map((unit) => unit.toCellValue({ locale: opts?.locale })));
  }

  protected async loadOpenAPIMemberCellValues(
    unitIds: string[],
    opts?: CellRenderOpts,
  ): Promise<OpenAPIMemberCellValue[]> {
    const units = await UnitFactory.getUnitsByUnitIds(this._model.spaceId, Array.from(unitIds));
    return Promise.all(units.map((unit) => unit.toOpenAPICellValue({ locale: opts?.locale })));
  }

  protected async loadUserCellValue(userId: string): Promise<UserCellValue> {
    return MemberSO.buildUserCellValue(userId, this._model.spaceId);
  }

  protected async loadUserCellValues(userIds: string[]): Promise<UserCellValue[]> {
    // 可能已离职的成员的情况, 自动使用匿名
    return MemberSO.buildUserCellValues(userIds, this._model.spaceId);
  }

  protected async loadOpenAPIUserCellValue(userId: string, locale?: Locale): Promise<OpenAPIUserCellValue> {
    return MemberSO.buildOpenAPIUserCellValue(userId, this._model.spaceId, locale);
  }

  protected async loadOpenAPIUserCellValues(userIds: string[], locale?: Locale): Promise<OpenAPIUserCellValue[]> {
    // 可能已离职的成员的情况, 自动使用匿名
    return MemberSO.buildOpenAPIUserCellValues(userIds, this._model.spaceId, locale);
  }

  protected async loadAttachmentCellValues(attachments: AttachmentCellData[]): Promise<AttachmentCellValue[]> {
    // 需要加载附件的临时下载地址
    const attachmentIds = attachments.map((att) => att.id);
    const attachmentSOs = await AttachmentSO.findMany(attachmentIds);
    const attachmentCells: AttachmentCellValue[] = await Promise.all(
      attachmentSOs.map(async (attachmentSO) => {
        const attachment = attachments.find((att) => att.id === attachmentSO.id);
        const thumbnailUrl = attachmentSO.thumbnailFullUrl;
        const downloadUrl = await attachmentSO.getPresignedGetUrl(attachment?.name);
        const previewUrl = attachmentSO.fullPath;
        const attachCellValue: AttachmentCellValue = { thumbnailUrl, previewUrl, downloadUrl };
        return attachCellValue;
      }),
    );
    return attachmentCells;
  }

  protected async loadOpenAPIAttachmentCellValues(
    attachments: AttachmentCellData[],
  ): Promise<OpenAPIAttachmentCellValue[]> {
    const attachmentIds = attachments.map((att) => att.id);
    const attachmentSOs = await AttachmentSO.findMany(attachmentIds);
    const attachmentCells: OpenAPIAttachmentCellValue[] = attachmentSOs.map((attachmentSO) => {
      const attachment = attachments.find((att) => att.id === attachmentSO.id);
      const thumbnailUrl = attachmentSO.thumbnailFullUrl;
      const previewUrl = attachmentSO.fullPath;
      const attachCellValue: OpenAPIAttachmentCellValue = {
        id: attachmentSO.id,
        name: attachment!.name,
        size: attachmentSO.size,
        mimeType: attachment!.mimeType,
        url: previewUrl,
        thumbnailUrl,
      };
      return attachCellValue;
    });
    return attachmentCells;
  }

  protected async loadDocCellValue(docId: string, opts?: CellRenderOpts): Promise<string | null> {
    const doc = await DocSO.getDocumentDataById(docId);
    return doc && doc.getName(opts?.locale);
  }

  protected async loadDocCellValues(docIds: string[], opts?: CellRenderOpts): Promise<string[]> {
    const docSOs = await DocSO.getDocumentsByIds([...docIds]);
    return docSOs.map((docSO) => docSO.getName(opts?.locale));
  }
}
