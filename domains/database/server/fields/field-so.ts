import _ from 'lodash';
import { generateNanoID } from 'sharelib/nano-id';
import { z } from 'zod';
import { ServerError, errors } from '@bika/contents/config/server/error';
import { EventSO } from '@bika/domains/event/server/event/event-so';
import { DatabaseFieldAclSO } from '@bika/domains/permission/server/field-acl-so';
import { isNullOrUndefined } from '@bika/domains/shared/shared';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import {
  $Enums,
  DatabaseRecordModel,
  db,
  DistributedOperation,
  mongoose,
  MongoTransactionCB,
  newDistributedOperation,
  Prisma,
  PrismaPromise,
} from '@bika/server-orm';
import {
  DatabaseField,
  DatabaseFieldProperty,
  DatabaseFieldType,
  DatabaseMultiSelectFieldSchema,
  DatabaseSingleSelectFieldSchema,
  FieldEditPrivilege,
  LookupDataType,
  MultiSelectFieldProperty,
  SingleSelectFieldProperty,
  Validator,
  ViewField,
  DatabaseFieldWithId,
  LookupFieldProperty,
  CellValue,
  SupportPrimaryFieldTypes,
} from '@bika/types/database/bo';
import { DatabaseFieldRO } from '@bika/types/database/ro';
import {
  FieldVO,
  CellValueLabelVO,
  FieldRenderOpts,
  CONST_PREFIX_OPTION,
  StandardCellValue,
} from '@bika/types/database/vo';
import { ExportBOOptions, ToTemplateOptions } from '@bika/types/node/bo';
import { DateTimeSO, iString, iStringMatch, iStringParse, LocaleType } from '@bika/types/system';
import type {
  BuildCellModelOptions,
  CreateRecordContext,
  DatabaseFieldModel,
  RecordChanges,
  RecordSortLocation,
  UpdateCellModelOptions,
  UpdateFieldRecordCell,
  UpdateRecordContext,
} from './types';
import { CellValueConvertorFactory } from '../cells/cellvalue-convertor/factory';
import { CellKey, CellModel } from '../cells/types';
import { DatabaseSO } from '../database-so';
import { RecordSO } from '../record-so';
import { FieldBOProcessorFactory } from './bo/factory';
import { FieldAdjacencyTable } from './field-adjacency-table';
import { FieldDirectedGraph } from './field-directed-graph';
import { FieldCellModelMap, RecordCellModelMap } from '../types';

const { isEqual, omit } = _;

type RefreshCellType = { need: true; action: 'rebuild' | 'convert' } | { need: false };

/**
 * 抽象FieldSO，请通过FieldSOFactory创建
 */
export abstract class FieldSO<T extends DatabaseField = DatabaseField> {
  // 字段模型
  private readonly _model: DatabaseFieldModel;

  private database?: DatabaseSO;

  private space?: SpaceSO;

  // 缓存的字段依赖链路图, 防止重复加载
  private directedGraph?: FieldDirectedGraph;

  public hasPropertyChanged: boolean = false;

  constructor(model: DatabaseFieldModel, database?: DatabaseSO) {
    this._model = model;
    this.database = database;
  }

  get model() {
    return this._model;
  }

  get spaceId(): string {
    return this.model.spaceId;
  }

  get databaseId(): string {
    return this.model.databaseId;
  }

  get id(): string {
    return this.model.id;
  }

  get templateId(): string | undefined {
    return this._model?.templateId || undefined;
  }

  get name(): iString {
    return this.model.name as iString;
  }

  getName(locale?: LocaleType): string {
    return iStringParse(this.name, locale);
  }

  get description(): iString | null {
    return this.model.description as iString | null;
  }

  getDescription(locale?: LocaleType): string {
    return iStringParse(this.description, locale);
  }

  get required(): boolean | null {
    return this.model.required;
  }

  get unique(): boolean | null {
    return this.model.unique;
  }

  get primary(): boolean | null {
    return this.model.primary;
  }

  get validators(): Validator[] {
    const jsonValue = this.model.validators;
    let validators: Validator[] = [];
    if (jsonValue !== null && Array.isArray(jsonValue)) {
      validators = jsonValue as unknown as Validator[];
    }
    return validators;
  }

  get property(): T['property'] {
    return this.model.property as T['property'];
  }

  get type(): T['type'] {
    return this.model.type as T['type'];
  }

  get privilege(): FieldEditPrivilege {
    if (this._model?.primary) {
      return 'TYPE_EDIT';
    }
    // TODO: fixed，在bo层定义
    if (this._model?.templateId) {
      // || this.property.fixed) {
      return 'NAME_EDIT';
    }

    return 'FULL_EDIT';
  }

  get createdAt(): Date {
    return this.model.createdAt;
  }

  get sequenceId() {
    return this.model.sequenceId;
  }

  /**
   * 是否字段属性发生了变化, 内存变化
   */
  isPropertyChanged(other: T['property']): boolean {
    return !isEqual(this.property, other);
  }

  get isDateTimeField(): boolean {
    return false;
  }

  /**
   * 输出记录单元格的字段标识
   * @param returnFieldName 是否返回字段名称作为标识
   * @param locale 语言环境
   * @returns ID或名称
   */
  toCellKey(returnFieldName: boolean, locale?: LocaleType): string {
    // 返回字段ID或名称
    if (returnFieldName) {
      return this.getName(locale);
    }
    return this.id;
  }

  /**
   * 唯一性校验
   * 当开启了 unique，需要检查 value 是否尚未存在 database 中
   * @deprecated 批量添加/更新时会严重影响性能问题, 而且只实现了Text字段的唯一性检查
   */
  protected async validateUnique(_value: CellValue): Promise<boolean> {
    if (!this.unique) {
      return true;
    }
    return true;
  }

  /**
   * 当前字段被引用时，推断引用的数据类型
   */
  inferLookupedDataType(): LookupDataType {
    // TODO: 从别处搬迁过来, 待重构
    const TEXT_TYPES: DatabaseFieldType[] = ['SINGLE_TEXT', 'LONG_TEXT', 'PHONE', 'EMAIL', 'URL'];
    const NUMBER_TYPES: DatabaseFieldType[] = ['NUMBER', 'PERCENT', 'CURRENCY', 'RATING', 'AUTO_NUMBER'];
    const DATETIME_TYPES: DatabaseFieldType[] = ['DATETIME', 'DATERANGE', 'CREATED_TIME', 'MODIFIED_TIME'];
    const BOOLEAN_TYPE: DatabaseFieldType = 'CHECKBOX';
    const SELECT_TYPES: DatabaseFieldType[] = ['SINGLE_SELECT', 'MULTI_SELECT'];
    const MEMBER_TYPES: DatabaseFieldType[] = ['MEMBER', 'CREATED_BY', 'MODIFIED_BY'];
    const ATTACHMENT_TYPE: DatabaseFieldType = 'ATTACHMENT';
    const LINK_TYPES: DatabaseFieldType[] = ['ONE_WAY_LINK', 'LINK'];
    const LOOKUP_TYPES: DatabaseFieldType[] = ['LOOKUP', 'FORMULA'];

    const targetType = this.type;

    if (
      TEXT_TYPES.includes(targetType) ||
      SELECT_TYPES.includes(targetType) ||
      MEMBER_TYPES.includes(targetType) ||
      ATTACHMENT_TYPE === targetType ||
      LINK_TYPES.includes(targetType) ||
      LOOKUP_TYPES.includes(targetType)
    ) {
      return 'STRING';
    }
    if (NUMBER_TYPES.includes(targetType)) {
      return 'NUMBER';
    }
    if (DATETIME_TYPES.includes(targetType)) {
      return 'DATETIME';
    }
    if (targetType === BOOLEAN_TYPE) {
      return 'BOOLEAN';
    }

    throw new Error(`Unsupports lookup for type [${targetType}]`);
  }

  /**
   * 获取字段的权限ACL对象
   * 还没施工, 用了也没用
   */
  async toAclSO(): Promise<DatabaseFieldAclSO> {
    return DatabaseFieldAclSO.init(this);
  }

  toVO(opts?: FieldRenderOpts): FieldVO {
    const { locale, viewFields } = opts || {};
    const viewField = viewFields?.find(
      (item: ViewField) => item?.id === this.id || (item?.templateId && item?.templateId === this.templateId),
    );
    const viewFieldConfig = viewField ? { ...viewField } : {};
    return {
      id: this.id,
      templateId: this.templateId,
      databaseId: this.databaseId,
      name: this.getName(locale),
      description: this.getDescription(locale),
      type: this.model.type as DatabaseFieldType,
      property: this.model.property || undefined,
      primary: this.model.primary!,
      required: this.model.required ?? undefined,
      privilege: this.privilege,
      ...viewFieldConfig,
    } as FieldVO;
  }

  toBO(): DatabaseFieldWithId {
    return {
      id: this.id,
      name: this.name as iString,
      description: (this.model.description as iString) || undefined,
      type: this.model.type as DatabaseFieldType,
      property: (this.model.property as DatabaseFieldProperty) || undefined,
      required: this.model.required ?? undefined,
      privilege: this.privilege,
      templateId: this.templateId || undefined,
      primary: !!this.primary,
    } as DatabaseFieldWithId;
  }

  public async toRO(): Promise<DatabaseFieldRO> {
    return this.toBO() as DatabaseFieldRO;
  }

  getPublishOperations(): PrismaPromise<Prisma.BatchPayload>[] {
    const operations: PrismaPromise<Prisma.BatchPayload>[] = [];
    if (!this.templateId) {
      operations.push(db.prisma.databaseField.updateMany({ where: { id: this.id }, data: { templateId: this.id } }));
    }
    return operations;
  }

  toTemplate(_opts?: ToTemplateOptions): DatabaseField {
    const field = omit(this.toBO(), 'id') as DatabaseField;
    if (!field.templateId) {
      field.templateId = this.id;
    }
    return field;
  }

  exportBO(_opts?: ExportBOOptions): DatabaseFieldWithId {
    return this.toBO();
  }

  /**
   * 是否匹配到字段
   */
  isMatchKey(fieldKey: string): boolean {
    // 优化匹配字段ID
    if (this.id === fieldKey) {
      return true;
    }
    // 其次匹配字段模板ID
    if (this.templateId && this.templateId === fieldKey) {
      return true;
    }
    // 最后匹配字段名
    return iStringMatch(this.name, fieldKey);
  }

  /**
   * 获取当前字段的所属数据表对象
   * 调用过于重叠, 缓存
   */
  async getDatabase(): Promise<DatabaseSO> {
    if (!this.database) {
      this.database = await DatabaseSO.init(this.databaseId);
    }
    return this.database;
  }

  /**
   * 获取当前字段的所属空间对象
   */
  async getSpace(): Promise<SpaceSO> {
    if (this.space) {
      return this.space;
    }
    if (this.database) {
      return this.database.getSpace();
    }
    return SpaceSO.init(this.spaceId);
  }

  /**
   * 获取更新字段配置DB操作
   */
  getUpdatePropertyOperation(): PrismaPromise<DatabaseFieldModel> {
    return db.prisma.databaseField.update({
      where: {
        id: this.id,
      },
      data: {
        property: this.model.property ?? undefined,
        revision: {
          increment: 1,
        },
      },
    });
  }

  /**
   * 获取更新字段DB操作
   */
  getUpdateModelOperation(userId: string, field: Omit<DatabaseField, 'id'>): PrismaPromise<DatabaseFieldModel> {
    const updateInput: Prisma.DatabaseFieldUpdateInput = {
      name: field.name,
      templateId: field.templateId,
      type: field.type.toString() as $Enums.DatabaseFieldType,
      property: field.property ? (field.property as Prisma.InputJsonValue) : Prisma.NullableJsonNullValueInput.DbNull,
      description: field.description,
      required: field.required,
      revision: {
        increment: 1,
      },
      updatedBy: userId,
      updatedAt: new Date(),
    };

    return db.prisma.databaseField.update({
      where: {
        id: this.id,
        // revision: this.model.revision,
      },
      data: updateInput,
    });
  }

  /**
   * 构建转换字段操作
   */
  protected buildConvertOperation(_user: UserSO, _updateFieldBO: DatabaseField): Promise<DistributedOperation> {
    // 转换成其他字段类型, 这里是默认转换行为, 子类可以重写
    return Promise.resolve(newDistributedOperation());
  }

  /**
   * 构建字段属性更改操作
   * 只修改字段属性, Link/Lookup字段特殊关注
   */
  protected buildUpdatePropertyOperation(user: UserSO, updateFieldBO: DatabaseField): Promise<DistributedOperation> {
    const prismaOperations: PrismaPromise<unknown>[] = [];
    // 更新字段的基本操作, 改字段属性
    prismaOperations.push(this.getUpdateModelOperation(user.id, updateFieldBO));
    return Promise.resolve({ prismaOperations, mongoOperations: [] });
  }

  /**
   * 比较两个BO是否相同
   */
  private isSame(otherField: DatabaseField): boolean {
    // return _.isEqual(_.omit(this.toBO(), 'id'), _.omit(otherField, 'id'));
    return (
      this.type === otherField.type &&
      this.templateId === otherField.templateId &&
      this.name === otherField.name &&
      this.description === otherField.description &&
      this.required === otherField.required &&
      this.primary === otherField.primary &&
      isEqual(this.property, otherField.property)
    );
  }

  /**
   * 更新字段之前的一些操作, 例如: 公式字段/引用字段会检查循环引用
   */
  protected async updateBefore(_user: UserSO, updateFieldBO: DatabaseField): Promise<void> {
    // 字段是否可以编辑
    if (this.privilege === 'NO_EDIT' || this.privilege === 'READ_ONLY') {
      throw new Error('Field cannot be edited');
    }
    if (this.primary && this.type !== updateFieldBO.type) {
      // 首字段转换字段类型有限制, 具体看子类实现
      if (!SupportPrimaryFieldTypes.includes(updateFieldBO.type)) {
        throw new Error(`Primary field type [${updateFieldBO.type}] is not supported`);
      }
    }
    // 所在数据表对象
    const database = await this.getDatabase();
    // 检查字段名是否重复
    database.checkFieldNameUpdateDuplicate(this.id, updateFieldBO.name);
  }

  /**
   * 更新字段之后的操作, 实现类有特殊处理可以重写
   */
  protected updateAfter(_user: UserSO, _updateFieldBO: DatabaseField): Promise<void> | void {}

  /**
   * 根据修改字段BO判断是否需要重新刷新字段单元格
   * 不允许被重写
   */
  private shouldRefreshCells(other: DatabaseField): RefreshCellType {
    if (this.type === other.type) {
      // 看是否会导致整列单元格更新
      const shouldUpdateCells = this.shouldUpdateCells(other.property);
      // console.log(`========> 需要重建字段单元格数据, ID: ${this.id} 名称: ${this.name}(${this.databaseId})`);
      return !shouldUpdateCells ? { need: false } : { need: true, action: 'rebuild' };
    }
    // 类型发生改变, 必定需要转换重新计算整列
    return { need: true, action: 'convert' };
  }

  /**
   * 是否会导致整列单元格更新
   */
  protected shouldUpdateCells(_other: T['property']): boolean {
    // 默认不需要, 看子类实现
    return false;
  }

  /**
   * 是否应该检查循环引用
   */
  protected shouldCheckCircularReference(_other: T['property']): boolean {
    // 默认不检查, 特殊情况看子类实现
    return false;
  }

  /**
   * 更改字段属性时, 根据目标字段BO批量刷新单元格
   */
  protected refreshCells(
    _user: UserSO,
    // 行记录
    _records: RecordSO[],
    // 根据此字段BO来刷新
    _updateFieldBO: DatabaseField,
    _options?: { session?: mongoose.ClientSession },
  ): RecordCellModelMap | Promise<RecordCellModelMap> {
    // 批量重建单元格
    return {};
  }

  /**
   * 转换字段类型, 批量转换单元格
   * 有些单元格无需更新, 减少数据库IO
   * 不能覆盖, 这是转换, 原字段没有什么帮助
   */
  async convertCells(
    user: UserSO,
    records: RecordSO[],
    updateFieldBO: DatabaseField,
    options?: { recordStartIndex: number; session?: mongoose.ClientSession },
  ): Promise<RecordCellModelMap> {
    // console.log(`========> 转换单元格数据, 字段: ${this.name}(${this.databaseId})`);
    if (updateFieldBO.type === 'AUTO_NUMBER') {
      if (!options?.recordStartIndex) {
        // 没传入遍历索引, 很危险, 这里方法不记录递增开始值, 容易导致数据混乱
        throw new Error('fail to convert to Auto number field');
      }
      const recordIndex = options.recordStartIndex;
      const entries: [string, FieldCellModelMap][] = records.map((record, index) => {
        // 自动编号字段, 递增
        const newData = recordIndex + index;
        // console.log(`================> newData: ${newData}`);
        const cellModel: CellModel = {
          data: newData,
          values: newData.toString(),
        };
        return [record.id, { [this.id]: { bo: updateFieldBO, cell: cellModel } }];
      });
      return Object.fromEntries(entries);
    }
    if (
      updateFieldBO.type === 'CREATED_BY' ||
      updateFieldBO.type === 'MODIFIED_BY' ||
      updateFieldBO.type === 'CREATED_TIME' ||
      updateFieldBO.type === 'MODIFIED_TIME'
    ) {
      // TODO 要优雅处理一下, 但要保持records尽量不浅拷贝
      return RecordSO.initAutoInjectFieldCellModel(user, this.spaceId, this.id, records, updateFieldBO);
    }
    const database = await this.getDatabase();
    const databaseFields = database.getFields();
    if (updateFieldBO.type === 'FORMULA') {
      // 转换成公式字段时, 跟创建新的公式字段原理一样, 每一行都初始化
      return RecordSO.initFormulaCellModel(database, records, this.id, updateFieldBO);
    }

    if (updateFieldBO.type === 'LOOKUP') {
      // 转换成引用字段时, 跟创建新的引用字段原理一样, 每一行都初始化
    }

    // 批量转换单元格
    // 目标字段单元格值转换器
    const converter = CellValueConvertorFactory.create(
      { id: this.id, ...updateFieldBO },
      {
        fields: databaseFields.map((f) => f.toBO()),
      },
    );
    // 字段单元格标准值(有些值需要IO查询)
    const recordStdCellValueEntries: [string, StandardCellValue][] = await Promise.all(
      records.map(async (record) => {
        const cell = record.getCellSO(this.id);
        const stdCellValue = await cell.getStdCellValue();
        return [record.id, stdCellValue];
      }),
    );
    // 以下循环必须有序, 转换成单多选有奇效
    const recordCellModelMap: RecordCellModelMap = {};
    for (const [recordId, stdCellValue] of recordStdCellValueEntries) {
      // 转换单元格值
      const cellModel = converter.stdCellValueToCellModel(stdCellValue);
      recordCellModelMap[recordId] = { [this.id]: { bo: updateFieldBO, cell: cellModel } };
    }
    return recordCellModelMap;
  }

  /**
   * 修改字段
   */
  async update(user: UserSO, updateFieldBO: DatabaseField): Promise<FieldSO> {
    // 字段修改之前的检查
    await this.updateBefore(user, updateFieldBO);
    // 检查是否相同
    if (this.isSame(updateFieldBO)) {
      // 无更改
      return this;
    }
    // 所在数据表对象
    const database = await this.getDatabase();
    // const databaseFields = database.getFields();

    // DB 操作
    const operations: PrismaPromise<unknown>[] = [];
    const sessions: MongoTransactionCB[] = [];

    // 目标字段属性加工, 自动补充一些字段的属性(比如单双选项的ID填充)
    const targetFieldBOProcessor = FieldBOProcessorFactory.getProcessor({ id: this.id, ...updateFieldBO });

    // 更新操作类型
    if (this.type === updateFieldBO.type) {
      // 类型不变, 只更改字段属性, 让子类处理
      if (this.shouldCheckCircularReference(updateFieldBO.property)) {
        // 属性变更, 检查循环引用, 但是只有在 Link/Lookup/Formula 字段类型属性变更才需要检查
        await targetFieldBOProcessor.checkCircularReference(database);
      }
      // 加工后的修改字段BO
      const targetFieldBO = targetFieldBOProcessor.getBO();
      const { prismaOperations, mongoOperations } = await this.buildUpdatePropertyOperation(user, targetFieldBO);
      operations.push(...prismaOperations);
      sessions.push(...mongoOperations);
      // console.log(`=====> 未处理 field bo: ${JSON.stringify(updateFieldBO)}`);
      // console.log(`=====> 已处理 field bo: ${JSON.stringify(targetFieldBO)}`);
    } else {
      // 更新字段, 转换字段类型比较复杂
      // 检查循环引用, 转换Link/Lookup/Formula字段会有循环引用风险
      await targetFieldBOProcessor.checkCircularReference(database);
      // 获取目标字段转换的操作, 例如: 其他类型转换为双向关联字段时, 需要在对面表创建一个关联字段
      const boOperations = await targetFieldBOProcessor.getPrismaUpdateOperations(user.id, database);
      operations.push(...boOperations);
      // 加工后的修改字段BO, 上面处理器处理过此BO
      const targetFieldBO = targetFieldBOProcessor.getBO();
      // 构造转换字段操作
      const { prismaOperations, mongoOperations } = await this.buildConvertOperation(user, targetFieldBO);
      operations.push(...prismaOperations);
      sessions.push(...mongoOperations);
      // console.log(`=====> 未处理 field bo: ${JSON.stringify(updateFieldBO)}`);
      // console.log(`=====> 已处理 field bo: ${JSON.stringify(targetFieldBO)}`);
    }

    // 是否需要重建记录
    const updateCellType = this.shouldRefreshCells(targetFieldBOProcessor.getBO());

    // 一起执行事务
    await db.mongo.transaction(
      async (session) => {
        // 刷新整列单元格
        if (updateCellType.need) {
          const targetFieldBO = targetFieldBOProcessor.getBO();
          await this.refreshCellAsPage(
            user,
            { ...targetFieldBO, id: this.id },
            async (records, recordStartIndex) => {
              if (updateCellType.action === 'rebuild') {
                // 属性变更, 重建单元格(公式表达式改变/关联字段更改/引用字段更改/成员字段更改/单多选字段选项变化)
                return this.refreshCells(user, records, targetFieldBO, { session });
              }
              // 字段转换, 刷新单元格
              // 可能会改变修改字段BO的属性, 比如: 转为单多选时, 自动添加更多选项
              if (this.type === targetFieldBO.type) {
                // 再次确认这里是转换字段的操作
                throw new Error('Convert cells should be called when field type is changed');
              }
              return this.convertCells(user, records, targetFieldBO, { recordStartIndex, session });
            },
            {
              pageSize: 100, // 每批次100条处理
              session,
            },
          );
        }
        for (const mongoSession of sessions) {
          await mongoSession(session);
        }
        // PG 操作
        // console.log(`=====> 未处理 field bo: ${JSON.stringify(updateFieldBO)}`);
        // console.log(`=====> 已处理 field bo: ${JSON.stringify(targetFieldBOProcessor.getBO())}`);
        // 最后再使用处理过的字段BO来落库, 因为前面转换单元格会导致变化
        const updateModelOperation = this.getUpdateModelOperation(user.id, targetFieldBOProcessor.getBO());
        await db.prisma.$transaction([updateModelOperation, ...operations]);
      },
      {
        readPreference: 'primary',
        readConcern: { level: 'majority' },
        writeConcern: { w: 'majority' },
      },
    );

    // 更新字段之后的操作
    // 目前主要用于 Link 字段，当类型发生变化时，需要将 BrotherField 转换为 SingleText 字段
    await this.updateAfter(user, updateFieldBO);

    // 重新获取字段信息
    const updatedDatabase = await DatabaseSO.init(database.id);
    const updatedField = updatedDatabase.getFieldByFieldKey(this.id);

    // 触发字段更新事件
    EventSO.database.onFieldUpdated(database, this, updatedField);

    return updatedField;
  }

  /**
   * 构建删除字段的DB操作
   */
  protected async buildDeleteOperations(user: UserSO): Promise<DistributedOperation> {
    // 获取当前数据库对象
    const database = await this.getDatabase();
    const views = await database.getViews();
    const prismaOperations: PrismaPromise<unknown>[] = [];
    // 取消视图里字段的引用
    const viewUnlinkFieldOperations = views.map((view) => view.unlinkFieldOperation(user.id, this.id));
    prismaOperations.push(...viewUnlinkFieldOperations);
    // 依赖此字段的属性被修改, 只处理第一层被依赖的字段属性更新, 更深层的依赖字段不处理
    const dependentFields = await this.getDependent();
    for (const dependentField of dependentFields) {
      // 只更新引用字段的属性
      if (dependentField.type === 'LOOKUP') {
        const updateProperty: LookupFieldProperty = {
          ...dependentField.property,
          lookupTargetFieldId: undefined,
        };
        prismaOperations.push(
          db.prisma.databaseField.update({
            where: {
              id: dependentField.id,
            },
            data: {
              property: updateProperty,
            },
          }),
        );
      }
    }
    // 删除当前字段
    const currentFieldDeleteOperation = db.prisma.databaseField.delete({
      where: {
        id: this.id,
      },
    });
    prismaOperations.push(currentFieldDeleteOperation);
    return {
      prismaOperations,
      mongoOperations: [],
    };
  }

  /**
   * 删除字段之前的一些操作, 包括检查是否主字段
   */
  protected deleteBefore(_user: UserSO): Promise<void> | void {
    if (this.primary) {
      throw new ServerError(errors.database.field_primary_delete);
    }
  }

  /**
   * 删除字段之后的操作, 实现类有特殊处理可以重写
   */
  protected deleteAfter(_user: UserSO): Promise<void> | void {}

  /**
   * 删除字段
   */
  async delete(user: UserSO): Promise<void> {
    // 删除字段之前的一些操作
    await this.deleteBefore(user);

    // 获取当前数据库对象
    const database = await this.getDatabase();

    // 删除字段的所有操作
    const { prismaOperations, mongoOperations } = await this.buildDeleteOperations(user);

    // 执行数据库操作
    await db.mongo.transaction(
      async (session) => {
        // MG数据更新
        for (const mongoSession of mongoOperations) {
          await mongoSession(session);
        }
        // 重设依赖的单元格
        await this.unsetCellAsPage(user, { session });
        // PG数据更新
        await db.prisma.$transaction(prismaOperations);
      },
      // 事务配置
      {
        readPreference: 'primary',
        readConcern: { level: 'majority' },
        writeConcern: { w: 'majority' },
      },
    );

    // 执行删除字段后的操作, 注意: 双向关联字段删除时候, 转换会花费一定时间, 不可预估
    await this.deleteAfter(user);

    // 触发字段删除事件
    EventSO.database.onFieldDeleted(database, this);
  }

  /**
   * 依赖谁, 当前节点的右指向的邻接点
   * Tips: 只有Link/Lookup/Formula字段才会有依赖别的字段的情况
   * 1. Link字段类型: 依赖关联表的首字段(跨表)
   * 2. Lookup字段类型: 依赖关联表的指定字段(跨表)
   * 3. Formula字段类型: 依赖当前表的其他字段(本表)
   * tips: 以上三种字段类型重写该方法
   */
  getDependencies(): FieldSO[] | Promise<FieldSO[]> {
    // 默认没有依赖谁
    return [];
  }

  /**
   * 被谁依赖, 当前节点的左边被指向的邻接点
   * 被依赖的情况比较复杂, 现在没有特定表去存储, 只能去特殊查询
   * 情况:
   * 1. 当前表有关联字段存在时, 任何字段都可能被其他表查找引用, 特别是[首字段]必定会被对面关联字段依赖
   * 2. 当前表有公式字段存在时, 任何字段都可能被依赖
   * tips: 无需被重写
   */
  async getDependent(): Promise<FieldSO[]> {
    const database = await this.getDatabase();
    // 被依赖的字段列表
    const dependentFields: FieldSO[] = [];
    // 当前表的公式字段
    const formulaFields = database.getFormulaFields();
    if (formulaFields.length > 0) {
      // 看是否有公式字段依赖当前字段
      for (const formulaField of formulaFields) {
        // 公式里引用的字段
        const depFieldIds = formulaField.getDependFieldIds();
        if (depFieldIds.includes(this.id)) {
          dependentFields.push(formulaField);
        }
      }
    }
    // 当前表的关联字段
    const linkFields = database.getOnlyLinkFields();
    const foreignDatabaseIds = linkFields
      .map((f) => f.property.foreignDatabaseId)
      .filter((f) => f !== undefined && f !== null);
    // 所有双向关联表, 也可能自我关联, 减少一次查询
    const outsideDatabaseIds = foreignDatabaseIds.filter((f) => f !== this.databaseId);
    const foreignDatabases = await DatabaseSO.getDatabases(this.spaceId, Array.from(new Set(outsideDatabaseIds)));
    if (foreignDatabaseIds.includes(this.databaseId)) {
      foreignDatabases.push(database);
    }
    if (foreignDatabases.length > 0) {
      // 看关联表是否有引用当前字段
      for (const foreignDatabase of foreignDatabases) {
        if (this.primary) {
          // 当前字段是主字段, 必定被关联表兄弟字段所引用, 也可能被再次查找引用
          // 关联表的关联字段
          const linkFieldsOnForeignDatabase = foreignDatabase.getOnlyLinkFields();
          // 关联本表的双向关联字段
          const foreignLinkFields = linkFieldsOnForeignDatabase.filter(
            (f) => f.property.foreignDatabaseId === this.databaseId,
          );
          for (const foreignLinkField of foreignLinkFields) {
            dependentFields.push(foreignLinkField);
          }
        }
        // 关联表使用当前字段作为查找引用
        const lookupFields = foreignDatabase.getLookupFields();
        const lookupTargetCurrentFields = lookupFields.filter((f) => f.property.lookupTargetFieldId === this.id);
        if (lookupTargetCurrentFields.length > 0) {
          for (const lookupField of lookupTargetCurrentFields) {
            dependentFields.push(lookupField);
          }
        }
      }
    }
    return dependentFields;
  }

  /**
   * 递归双向收集当前字段的依赖关系
   * @param at 邻接表
   * @param visitedFieldIds 访问过的字段ID集合
   */
  async collectRelations(at: FieldAdjacencyTable, visitedFieldIds: Set<string>): Promise<void> {
    if (visitedFieldIds.has(this.id)) return;
    visitedFieldIds.add(this.id);

    // 收集右邻接点（依赖）
    const dependencies = await this.getDependencies();
    for (const dep of dependencies) {
      at.addField(this);
      at.addEdge(this.id, dep);
      await dep.collectRelations(at, visitedFieldIds);
    }

    // 收集左邻接点（被依赖）
    const dependents = await this.getDependent();
    for (const parent of dependents) {
      at.addField(parent);
      at.addEdge(parent.id, this);
      await parent.collectRelations(at, visitedFieldIds);
    }
  }

  /**
   * 构造邻接表
   */
  async buildAdjacencyTable(): Promise<FieldAdjacencyTable> {
    // 邻接表
    const adjacencyTable = new FieldAdjacencyTable();
    // 双向递归收集(DFS)
    await this.collectRelations(adjacencyTable, new Set<string>());
    // 递归收集完毕,返回邻接表
    return adjacencyTable;
  }

  /**
   * 从当前字段开始, 构造有向图
   */
  async toDirectedGraph(): Promise<FieldDirectedGraph> {
    if (!this.directedGraph) {
      const adjacencyTable = await this.buildAdjacencyTable();
      this.directedGraph = new FieldDirectedGraph(adjacencyTable);
    }
    return this.directedGraph;
  }

  /**
   * 重设其被依赖的字段单元格值
   * 规则:
   * 1. 由于首字段无法被删除, 不会触发关联表的兄弟字段更新, 只会触发公式(本表)和引用字段(跨表)更新
   * 2. 如果当前字段是关联字段, 那就会重置掉本表的相关引用字段的信息, 其他字段会使用规则1
   */
  async unsetCellAsPage(user: UserSO, options?: { session?: mongoose.ClientSession }) {
    const database = await this.getDatabase();
    // 如果是关联字段被删除, 本表的所有引用字段要被清空
    // 因为引用字段并不是直接依赖关联字段
    if (this.type === 'LINK') {
      // 获取使用当前字段作为查找引用的字段
      const lookupFields = database.getLookupFields().filter((f) => f.property.relatedLinkFieldId === this.id);
      for (const lookupField of lookupFields) {
        // console.log(
        //   `引用字段执行清理: ${lookupField.name}(${lookupField.databaseId}) 依赖了 ${this.name}(${this.databaseId})`,
        // );
        await lookupField.unsetCellAsPage(user, { session: options?.session });
      }
    }
    // 如果表格存在自动更新字段, 则要逐步更新
    const modifiedTimeFields = database.getModifiedTimeFields();
    const modifiedByFields = database.getModifiedByFields();
    if (modifiedTimeFields.length > 0 || modifiedByFields.length > 0) {
      // 逐个更新
      await database.getRecordsAsStream(
        async (records) => {
          // 批量构造
          const bulkUpdates = records
            .map((record) => {
              const updateOne = record.buildUpdateQuery(user, {
                [this.id]: { bo: this.toBO(), cell: {} },
              });
              return updateOne && { updateOne };
            })
            .filter((item) => item !== null);
          await db.mongo.databaseRecord(database.spaceId).bulkWrite(bulkUpdates, { session: options?.session });
        },
        { pageSize: 1000, session: options?.session },
      );
    } else {
      // 没有系统审计字段时, 直接清空当前字段的单元格的效率最佳
      const now = DateTimeSO.now();
      await db.mongo.databaseRecord(this.spaceId).updateMany(
        {
          databaseId: this.databaseId,
        },
        {
          $unset: {
            [`${CellKey.DATA}.${this.id}`]: '',
            [`${CellKey.COMPUTED}.${this.id}`]: '',
            [`${CellKey.VALUES}.${this.id}`]: '',
          },
          $set: {
            updatedBy: user.id,
            updatedAt: now.toDate(),
          },
          $inc: { revision: 1 },
        },
        { session: options?.session },
      );
    }

    // 被谁依赖
    const dependentFields = await this.getDependent();
    for (const dependentField of dependentFields) {
      // console.log(
      //   `依赖字段执行清理: ${dependentField.name}(${dependentField.databaseId}) 依赖了 ${this.name}(${this.databaseId})`,
      // );
      // 上面处理过本表的依赖字段单元格已经变得不可用, 直接更新它的依赖链路
      await dependentField.unsetCellAsPage(user, { session: options?.session });
    }
  }

  /**
   * 刷新整列单元格值
   * 1. 如果是转换, 每批次转换当前字段单元格值, 传递已转换的单元格值给外部依赖的字段继续更新
   * 2. 如果是重建, 每批次更新当前字段单元格值, 传递已计算的单元格值给外部依赖的字段继续更新
   */
  async refreshCellAsPage(
    user: UserSO,
    updateFieldBO: DatabaseFieldWithId,
    // 每批记录回调, 重建还是转换, 外面决定
    handleRecords: (records: RecordSO[], startIndex: number) => Promise<RecordCellModelMap>,
    options?: { pageSize?: number; session?: mongoose.ClientSession; ignoreDependentFieldIds?: string[] },
  ) {
    const { pageSize, session, ignoreDependentFieldIds } = options || {};
    // 当前字段是刷新, 依赖层是计算
    const database = await this.getDatabase();
    // 被谁依赖
    const dependentFields = await this.getDependent();
    // 流式获取记录更新
    let cursorIndex = 1;
    await database.getRecordsAsStream(
      async (records) => {
        // 当前字段计算好的单元格值(应该确保返回的数量跟记录数一致)
        const recordCellModelMap = await handleRecords(records, cursorIndex);
        // 批量更新操作集合
        const bulkUpdates: mongoose.AnyBulkWriteOperation<DatabaseRecordModel>[] = [];
        const updatedRecordModels: DatabaseRecordModel[] = [];
        // 这一批数据补充一些字段需要动态计算的单元格值
        for (const [recordId, cellModelMap] of Object.entries(recordCellModelMap)) {
          const record = records.find((r) => r.id === recordId);
          const updateOne = record?.buildUpdateQuery(user, cellModelMap);
          if (updateOne) {
            bulkUpdates.push({ updateOne });
            const newRecordModel = record?.mergeCellModelToNewModel(cellModelMap);
            if (newRecordModel) {
              updatedRecordModels.push(newRecordModel);
            }
          }
        }

        // 更新当前的记录
        await db.mongo.databaseRecord(this.spaceId).bulkWrite(bulkUpdates, { session });

        // 获取最新记录
        // const updatedRecordModels = await db.mongo
        //   .databaseRecord(this.spaceId)
        //   .find({
        //     databaseId: this.databaseId,
        //     id: { $in: toUpdateRecordIds },
        //   })
        //   .session(session ?? null);

        console.log(`我执行完了: ${this.type}, 下面执行依赖我的字段单元格更新`);

        // 递归更新依赖此字段的单元格(本表公式/外表引用/外表关联)
        for (const dependentField of dependentFields) {
          if (ignoreDependentFieldIds?.includes(dependentField.id)) {
            // 忽略依赖的字段
            continue;
          }
          await dependentField.updateCells(user, updateFieldBO, updatedRecordModels, { session });
        }

        // 更新游标批次开始索引
        cursorIndex += records.length;
      },
      { pageSize, session },
    );
  }

  /**
   * 更新字段的单元格
   * 前一批计算完后传递单元格值来更新
   */
  async updateCells(
    user: UserSO | null,
    dependField: DatabaseFieldWithId,
    dependRecords: DatabaseRecordModel[],
    options?: { session?: mongoose.ClientSession },
  ): Promise<void> {
    const { session } = options || {};
    console.log(
      `\n=== 依赖更新[1]: [${this.name} - ${this.type}(${this.databaseId})]依赖[${dependField.type}]的单元格值 ===`,
    );
    const calculatedRecords = await this.updateCellsWithDepend(user, dependField, dependRecords, { session });
    // 被谁依赖, 继续更新依赖此字段的单元格
    const dependentFields = await this.getDependent();
    for (const dependentField of dependentFields) {
      // 更新外部依赖的单元格, 忽略本表的字段
      console.log(`\n=== 依赖更新[2]: [${this.name}] -> [${dependentField.name}] ===`);
      await dependentField.updateCells(user, this.toBO(), calculatedRecords, { session });
    }
  }

  /**
   * 更新指定批次的单元格数据
   */
  protected updateCellsWithDepend(
    _user: UserSO | null,
    // 依赖的单元格值
    _dependField: DatabaseFieldWithId,
    _dependRecords: DatabaseRecordModel[],
    _option?: { session?: mongoose.ClientSession },
  ): DatabaseRecordModel[] | Promise<DatabaseRecordModel[]> {
    // 只有依赖别的字段才会触发更新, 目前只有Link/Lookup/Formula字段会依赖别的字段, 看实现
    return [];
  }

  /**
   * 更新指定行数的单元格值
   */
  async bulkUpdateCells(
    user: UserSO | null,
    updateRecordCells: UpdateFieldRecordCell[], // 更新的记录单元格值
    options?: { session?: mongoose.ClientSession },
  ): Promise<RecordChanges> {
    console.debug(`批量更新字段[${this.name}]记录单元格`);
    const database = await this.getDatabase();
    // 对应每一行的单元格值更新
    const recordIds = updateRecordCells.map((cell) => cell.recordId);
    // 需要更新的记录
    const toUpdatedRecords = await database.getRecords(recordIds, options?.session);
    const bulkUpdates: mongoose.AnyBulkWriteOperation<DatabaseRecordModel>[] = [];
    // const updatedRecordIds: string[] = [];
    const recordChanges: RecordChanges = [];
    for (const { recordId, cell } of updateRecordCells) {
      const toUpdatedRecord = toUpdatedRecords.find((record) => record.id === recordId);
      if (!toUpdatedRecord) {
        // 没有找到对应的记录, 跳过
        console.warn(`没有找到需要更新的记录: ${recordId}`);
        continue;
      }
      const oldCellData = toUpdatedRecord.getCellData(this.id);
      const updateOne = toUpdatedRecord.buildUpdateQuery(user, {
        [this.id]: { bo: this.toBO(), cell },
      });
      if (updateOne) {
        bulkUpdates.push({ updateOne });
        // updatedRecordIds.push(recordId);
        recordChanges.push({
          recordId,
          cellChanges: [{ field: this.toBO(), from: oldCellData ?? null, to: cell.data ?? null }],
        });
      }
    }
    // 执行更新
    await db.mongo.databaseRecord(this.spaceId).bulkWrite(bulkUpdates, { session: options?.session });

    return recordChanges;
  }

  /**
   * 根据输入值初始化单元格的Data存储值和更新字段配置
   *
   * @return new data
   * @deprecated 待重构
   */
  processCellDataOnCreate(data: CellValue, _ctx: CreateRecordContext): CellValue {
    return data;
  }

  /**
   * 根据输入值初始化单元格的Data存储值和更新字段配置
   *
   * @return new data
   * @deprecated 待重构
   */
  processCellDataOnUpdate(data: CellValue, _ctx?: CreateRecordContext): CellValue {
    return data;
  }

  /**
   * 获取排序时按照哪个字段进行排序
   * 默认 data
   *
   * @return data,value,computed 三者中的一个
   */
  public sortField(): RecordSortLocation {
    return 'data';
  }

  getQueryFieldKey(): string {
    return `${CellKey.DATA}.${this.id}`;
  }

  /**
   * 是否是自动注入单元格值的字段
   * 如：计算字段、自增字段、创建人/时间、最后修改人/时间等，这些字段不需要用户输入
   */
  get isAutoInject() {
    return false;
  }

  /**
   * 是否是主字段
   * @returns boolean
   */
  isPrimary(): boolean {
    return this.primary ?? false;
  }

  /**
   * 字段值是否必填
   */
  isRequired(): boolean {
    return this.required ?? false;
  }

  /**
   * 验证输入值数据类型是否合法
   */
  protected abstract assertValueType(_input: Exclude<CellValue, null>): boolean;

  /**
   * 验证输入值
   * 产生IO检查的场景:
   * 1. 字段: 关联字段/成员字段
   * 2. 单元格唯一值校验
   * 3. 验证器
   */
  async validateInputCellValue(cellValue?: CellValue): Promise<void> {
    // 1. 没输入值
    if (isNullOrUndefined(cellValue)) {
      // 如果是必填字段, 抛出错误
      if (this.isRequired()) {
        throw new ServerError(errors.database.field_value_required, { name: this.getName() });
      }
      // 非必填, 无需验证
      return;
    }
    // 2. 有输入值, 验证输入值是否合法
    const assertValueType = this.assertValueType(cellValue);
    if (!assertValueType) {
      throw new ServerError(errors.database.input_field_value_invalidate, { name: this.getName(), value: cellValue });
    }

    // 3. 验证唯一值
    const valid = await this.validateUnique(cellValue);
    if (!valid) {
      throw new ServerError(errors.database.field_value_unique, { name: this.getName(), value: cellValue });
    }
  }

  /**
   * 验证输入值是否为字符串数组, 支持元素单个或多个校验
   */
  protected isCellValueValidStringArray(input: CellValue, multi: boolean = false): boolean {
    // 1. 检查是否为数组
    if (!Array.isArray(input)) return false;

    // 2. 检查所有元素是否为字符串
    const isAllString = input.every((item) => typeof item === 'string');
    if (!isAllString) return false;

    // 3. 根据配置检查数组长度
    if (multi) {
      return true; // 允许多选，长度不限（但必须 ≥0，因数组本身允许空）
    }
    return input.length <= 1; // 单选：长度 ≤1
  }

  /**
   * 是否应该设置默认值
   * 创建记录时, 输入值为空, 根据字段配置检测是否需要设置默认值
   * 目前支持字段类型: 数字/货币/百分比/单多选/日期时间
   */
  hasDefaultValue(): boolean {
    return false;
  }

  /**
   * 填充默认单元格数据
   * 仅对部分字段类型有效: 数字/货币/百分比/单多选/日期时间
   */
  fillDefaultCellModel(_context: CreateRecordContext): CellModel {
    // 默认返回空对象
    throw new Error(`the type of field [${this.id}] does not support auto fill default cell value: ${this.type}`);
  }

  /**
   * 创建记录时, 是否自动初始化单元格
   * 仅针对字段类型有效: 自动编号/创建人/创建时间
   * @returns boolean
   */
  isAutoInitializeCellOnCreation(): boolean {
    return false;
  }

  /**
   * 在创建记录时, 初始化单元格值
   * 仅针对字段类型有效: 自动编号/创建人/创建时间
   * 其他字段一律抛出异常
   */
  initializeCellModel(_context: CreateRecordContext): CellModel {
    throw new Error(`the type of field [${this.id}] does not support auto initialization of cell value: ${this.type}`);
  }

  /**
   * 修改记录时, 是否自动初始化单元格
   * 仅针对字段类型有效: 修改人/修改时间
   * @returns boolean
   */
  isAutoInitializeCellOnUpdate(): boolean {
    return false;
  }

  /**
   * 在更新记录时, 自动初始化单元格值
   * 仅针对字段类型有效: 修改人/修改时间
   */
  initializeCellModelOnUpdate(_context: UpdateRecordContext): CellModel {
    return {};
  }

  /**
   * 根据输入值构建单元格初始值
   * 输入值不会为空, 只能有实际值才能构建
   * @returns CellModel
   */
  abstract buildCellModel(options: BuildCellModelOptions): CellModel;

  /**
   * 根据输入值初始化单元格更新值
   * @returns CellModel | undefined
   */
  buildUpdateCellModel(_options: UpdateCellModelOptions): CellModel | undefined {
    // 默认忽略
    return undefined;
  }

  /**
   * 重载单元格数据
   */
  reloadCellModel(
    _user: UserSO | null,
    recordModel: DatabaseRecordModel,
    _options?: { session?: mongoose.ClientSession },
  ): DatabaseRecordModel | Promise<DatabaseRecordModel> {
    return recordModel;
  }

  convertToCellValue(_value: string | string[]): CellValue {
    return null;
  }

  /**
   * 构造假数据供预览
   */
  abstract getFakeCellData(): { data: CellValue; value: CellValueLabelVO };

  /**
   * 从模板更新字段操作
   * @param userId user id
   * @param fieldTemplate field template
   * @deprecated 升级场景现在未开放,代码可能要重构
   */
  public updateOperation(userId: string, fieldTemplate: DatabaseFieldWithId): PrismaPromise<DatabaseFieldModel> {
    let processedField: DatabaseFieldWithId = fieldTemplate;
    // 前置处理
    if (this.type === 'SINGLE_SELECT' || this.type === 'MULTI_SELECT') {
      const DatabaseSelectFieldSchema = z.discriminatedUnion('type', [
        DatabaseSingleSelectFieldSchema,
        DatabaseMultiSelectFieldSchema,
      ]);
      // 从模板里更新字段属性操作, 模板升级目前只会增量,不会减量, 当前是(单/多)选择字段类型
      const cloneField = DatabaseSelectFieldSchema.parse(fieldTemplate);
      const cloneFieldProperty = cloneField.property;
      const fieldTemplateOptions = cloneFieldProperty.options;
      // 当前字段的options
      const property = this.property as SingleSelectFieldProperty | MultiSelectFieldProperty;
      const currentOptions = property.options ?? [];
      // 遍历模板的options, 如果当前字段的options中不存在, 则添加
      for (const option of fieldTemplateOptions) {
        if (option.templateId) {
          const match = currentOptions.find(
            (currentOption) => currentOption.templateId && currentOption.templateId === option.templateId,
          );
          if (match) {
            // 赋值
            option.id = match.id;
            option.color = match.color;
          } else {
            // 新建
            option.id = generateNanoID(CONST_PREFIX_OPTION);
          }
        }
      }
      cloneField.property.options = fieldTemplateOptions;
      processedField = cloneField as DatabaseFieldWithId;
    }
    return db.prisma.databaseField.update({
      where: {
        id: this.id,
      },
      data: {
        name: processedField.name,
        type: processedField.type.toString() as $Enums.DatabaseFieldType,
        required: processedField.required,
        property: processedField.property ?? undefined,
        validators: processedField.validators,
        description: processedField.description,
        updatedBy: userId,
      },
    });
  }
}
