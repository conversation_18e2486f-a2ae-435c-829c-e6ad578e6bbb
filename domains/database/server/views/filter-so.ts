import type { FilterQuery } from 'mongoose';
import { Logger } from '@bika/domains/shared/server';
import { FiltersSO } from '@bika/domains/shared/server/filters-so';
import { isNullOrUndefined, withTimeZone } from '@bika/domains/shared/shared';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { TeamSO } from '@bika/domains/unit/server/team-so';
import { UnitFactory } from '@bika/domains/unit/server/unit-factory';
import { UserSO } from '@bika/domains/user/server/user-so';
import { DatabaseRecordModel } from '@bika/server-orm';
import {
  FilterDateTime,
  ViewFilter,
  BasicValueType,
  FilterCondition,
  StringFilterConditionClause,
  NumberFilterConditionClause,
  BooleanFilterConditionClause,
  SingleStringArrayFilterConditionClause,
  StringArrayFilterConditionClause,
  PresentFilterConditionClause,
  DateTimeFilterConditionClause,
} from '@bika/types/database/bo';
import { RecordVO } from '@bika/types/database/vo';
import { FilterConjunction, FilterDuration, ActionFilterCondition } from '@bika/types/shared/filters-input';
import { DateRangeSO, DateTimeSO } from '@bika/types/system';
import { FilterConditionMigrateAdapter } from '../../shared/filter-condition-migrate-adapter';
import { CellValueConvertorFactory, FieldContext } from '../cells/cellvalue-convertor/factory';
import { DatabaseSO } from '../database-so';
import { FieldSO } from '../fields/field-so';
import { FieldUtils } from '../fields/utils';

export class FilterSO {
  private readonly filter: ViewFilter | null;

  private readonly database: DatabaseSO;

  private readonly _ctx: FieldContext;

  constructor(database: DatabaseSO, filter: ViewFilter | null) {
    this.filter = filter;
    this.database = database;

    // Init Field Context
    this._ctx = {
      fields: database.getFields().map((field) => field.toBO()),
    };
  }

  get conjunction(): FilterConjunction | undefined {
    return this.filter?.conjunction;
  }

  /**
   * 获取条件逻辑拼接符号
   */
  private getLogicQueryConnector(conjunction: FilterConjunction): '$and' | '$or' {
    if (conjunction === 'And') {
      return '$and';
    }
    if (conjunction === 'Or') {
      return '$or';
    }
    return '$and'; // 默认使用 And
  }

  get conditions(): FilterCondition[] {
    // 迁移兼容
    return this.filter?.conds && this.filter?.conds.length > 0
      ? this.filter.conds
      : FilterConditionMigrateAdapter.migrate(this.filter?.conditions ?? []);
  }

  toVO(): ViewFilter {
    const conds = this.conditions.map((cond) => {
      // field key maybe fieldId or fieldTemplateId
      const fieldKey = cond.fieldId || cond.fieldTemplateId;
      if (!fieldKey) {
        throw new Error('Conditions field key is missing');
      }
      const field = this.database.findFieldByFieldKey(fieldKey);
      if (!field) {
        throw new Error(`Conditions field not found: ${fieldKey}`);
      }
      return {
        ...cond,
        fieldId: field.id,
        fieldTemplateId: field.templateId,
      };
    });
    return {
      conditions: [],
      conds,
      conjunction: this.filter?.conjunction ?? 'And',
    };
  }

  toBO(): ViewFilter {
    return {
      conditions: [],
      conds: this.conditions,
      conjunction: this.filter?.conjunction || 'And',
    };
  }

  /**
   * 为空的通用查询条件
   */
  private buildIsEmptyQuery(fieldId: string): FilterQuery<DatabaseRecordModel> {
    return {
      $or: [{ [fieldId]: { $in: [null, ''] } }, { [fieldId]: { $exists: false } }, { [fieldId]: [] }],
    };
  }

  /**
   * 不为空的通用查询条件
   */
  private buildIsNotEmptyQuery(fieldId: string): FilterQuery<DatabaseRecordModel> {
    return {
      $nor: [{ [fieldId]: { $in: [null, ''] } }, { [fieldId]: { $exists: false } }, { [fieldId]: [] }],
    };
  }

  private hasEmptyConditions(): boolean {
    return !this.filter || !this.conjunction || this.conditions.length === 0;
  }

  public async buildQuery(user?: UserSO): Promise<FilterQuery<DatabaseRecordModel>> {
    if (this.hasEmptyConditions()) {
      return {};
    }
    if (!this.conjunction) {
      throw new Error(`Unsupported conjunction: ${this.conjunction}`);
    }

    const filterQueries: Array<FilterQuery<DatabaseRecordModel>> = [];
    // 开始遍历构建查询条件
    for (const condition of this.conditions) {
      const query = await this.buildFilterQuery(condition, { user });
      if (Object.keys(query).length > 0) {
        filterQueries.push(query);
      }
    }

    // no conditions
    if (filterQueries.length === 0) {
      return {};
    }
    // console.debug('filterQueries', JSON.stringify(filterQueries, null, 2));
    const symbol = this.getLogicQueryConnector(this.conjunction);
    return { [symbol]: filterQueries };
  }

  private async buildFilterQuery(
    condition: FilterCondition,
    options?: { user?: UserSO },
  ): Promise<FilterQuery<DatabaseRecordModel>> {
    const { fieldId, fieldTemplateId, fieldType, clause } = condition;
    const fieldKey = fieldId || fieldTemplateId;
    if (!fieldKey) {
      throw new Error('Field key is missing in condition');
    }
    const field = this.database.findFieldByFieldKey(fieldKey);
    // 可能字段已经被删除, 忽略即可
    if (!field) {
      console.warn(`Field not found for key: ${fieldKey}`);
      return {};
    }
    // 有可能原来的字段类型转换成其他类型, 过滤条件配置的字段没有及时更新和删除, 忽略即可
    if (fieldType !== field.type) {
      console.warn(`Field type mismatch: expected ${fieldType}, got ${field.type}`);
      return {};
    }
    switch (fieldType) {
      case 'SINGLE_TEXT':
      case 'LONG_TEXT':
      case 'EMAIL':
      case 'URL':
      case 'PHONE':
      case 'FORMULA': {
        // 处理文本类型的字段
        return this.buildStringFieldQuery(field, clause);
      }
      case 'NUMBER':
      case 'PERCENT':
      case 'CURRENCY':
      case 'RATING':
      case 'AUTO_NUMBER': {
        // 处理数字类型的字段
        return this.buildNumberFieldQuery(field, clause);
      }
      case 'CHECKBOX': {
        // 处理布尔类型的字段
        return this.buildBooleanFieldQuery(field, clause);
      }
      case 'SINGLE_SELECT': {
        // 处理单选类型的字段
        return this.buildSingleSelectFieldQuery(field, clause);
      }
      case 'MULTI_SELECT': {
        // 处理多选类型的字段
        return this.buildMultipleSelectFieldQuery(field, clause);
      }
      case 'MEMBER': {
        // 处理成员类型的字段
        return this.buildMemberFieldQuery(field, clause, options);
      }
      case 'CREATED_BY':
      case 'MODIFIED_BY': {
        // 处理创建人和修改人字段
        return this.buildUserFilterQuery(field, clause, options);
      }
      case 'LINK':
      case 'ONE_WAY_LINK': {
        return this.buildLinkFieldQuery(field, clause);
      }
      case 'DATETIME': {
        return this.buildDateTimeFieldQuery(field, clause, {
          user: options?.user,
        });
      }
      case 'CREATED_TIME':
      case 'MODIFIED_TIME': {
        // 处理创建时间和修改时间字段
        return this.buildDateTimeFieldQuery(field, clause, {
          user: options?.user,
          useDateComparison: true, // 使用日期比较
        });
      }
      default:
        return this.buildDefaultFilterQuery(field, clause);
    }
  }

  private buildDefaultFilterQuery(
    field: FieldSO,
    clause: PresentFilterConditionClause,
  ): FilterQuery<DatabaseRecordModel> {
    // 处理默认的 IsEmpty 和 IsNotEmpty 查询
    const { operator } = clause;
    const queryKey = field.getQueryFieldKey();
    switch (operator) {
      case 'IsEmpty': {
        return this.buildIsEmptyQuery(queryKey);
      }
      case 'IsNotEmpty': {
        return this.buildIsNotEmptyQuery(queryKey);
      }
      default: {
        return {};
      }
    }
  }

  private buildStringFieldQuery(field: FieldSO, clause: StringFilterConditionClause): FilterQuery<DatabaseRecordModel> {
    // 处理文本类型的字段查询
    const { operator, value } = clause;
    const queryKey = field.getQueryFieldKey();
    switch (operator) {
      case 'IsEmpty': {
        return this.buildIsEmptyQuery(queryKey);
      }
      case 'IsNotEmpty': {
        return this.buildIsNotEmptyQuery(queryKey);
      }
      case 'Is': {
        return value ? { [queryKey]: { $eq: value } } : {};
      }
      case 'IsNot': {
        return value ? { [queryKey]: { $ne: value } } : {};
      }
      case 'Contains': {
        return value ? { [queryKey]: { $regex: value, $options: 'i' } } : {};
      }
      case 'DoesNotContain': {
        return value ? { [queryKey]: { $not: { $regex: value, $options: 'i' } } } : {};
      }
      default: {
        return {};
      }
    }
  }

  private buildNumberFieldQuery(field: FieldSO, clause: NumberFilterConditionClause): FilterQuery<DatabaseRecordModel> {
    // 处理数字类型的字段查询
    const { operator, value } = clause;
    const queryKey = field.getQueryFieldKey();
    switch (operator) {
      case 'IsEmpty': {
        return this.buildIsEmptyQuery(queryKey);
      }
      case 'IsNotEmpty': {
        return this.buildIsNotEmptyQuery(queryKey);
      }
      case 'Is': {
        const numberValue = !isNullOrUndefined(value) ? Number(value) : null;
        return numberValue !== null ? { [queryKey]: { $eq: numberValue } } : {};
      }
      case 'IsNot': {
        const numberValue = !isNullOrUndefined(value) ? Number(value) : null;
        return numberValue !== null ? { [queryKey]: { $ne: numberValue } } : {};
      }
      case 'IsGreater': {
        const numberValue = !isNullOrUndefined(value) ? Number(value) : null;
        return numberValue !== null ? { [queryKey]: { $gt: numberValue } } : {};
      }
      case 'IsGreaterEqual': {
        const numberValue = !isNullOrUndefined(value) ? Number(value) : null;
        return numberValue !== null ? { [queryKey]: { $gte: numberValue } } : {};
      }
      case 'IsLess': {
        const numberValue = !isNullOrUndefined(value) ? Number(value) : null;
        return numberValue !== null ? { [queryKey]: { $lt: numberValue } } : {};
      }
      case 'IsLessEqual': {
        const numberValue = !isNullOrUndefined(value) ? Number(value) : null;
        return numberValue !== null ? { [queryKey]: { $lte: numberValue } } : {};
      }
      default: {
        return {};
      }
    }
  }

  private buildBooleanFieldQuery(
    field: FieldSO,
    clause: BooleanFilterConditionClause,
  ): FilterQuery<DatabaseRecordModel> {
    // 处理布尔类型的字段查询
    const { operator, value } = clause;
    const queryKey = field.getQueryFieldKey();
    switch (operator) {
      case 'Is': {
        if (value === true) {
          return { [queryKey]: { $eq: true } };
        }
        return { [queryKey]: { $ne: true } };
      }
      default: {
        return {};
      }
    }
  }

  private buildSingleSelectFieldQuery(
    field: FieldSO,
    clause: SingleStringArrayFilterConditionClause,
  ): FilterQuery<DatabaseRecordModel> {
    // 处理单选类型的字段查询
    const { operator, value } = clause;
    const queryKey = field.getQueryFieldKey();
    switch (operator) {
      case 'IsEmpty': {
        return this.buildIsEmptyQuery(queryKey);
      }
      case 'IsNotEmpty': {
        return this.buildIsNotEmptyQuery(queryKey);
      }
      case 'Is': {
        return value ? { [queryKey]: { $all: [value] } } : {};
      }
      case 'IsNot': {
        return value ? { [queryKey]: { $ne: [value] } } : {};
      }
      case 'Contains': {
        return value ? { [queryKey]: { $in: value } } : {};
      }
      case 'DoesNotContain': {
        return value ? { [queryKey]: { $nin: value } } : {};
      }
      default: {
        return {};
      }
    }
  }

  private buildMultipleSelectFieldQuery(
    field: FieldSO,
    clause: StringArrayFilterConditionClause,
  ): FilterQuery<DatabaseRecordModel> {
    // 处理多选类型的字段查询
    const { operator, value } = clause;
    const queryKey = field.getQueryFieldKey();
    switch (operator) {
      case 'IsEmpty': {
        return this.buildIsEmptyQuery(queryKey);
      }
      case 'IsNotEmpty': {
        return this.buildIsNotEmptyQuery(queryKey);
      }
      case 'Is': {
        return value ? { [queryKey]: { $eq: value } } : {};
      }
      case 'IsNot': {
        // 如果值为 null 或者空数组, 则查询所有不等于该值的记录
        return value
          ? { $or: [{ [queryKey]: { $ne: value } }, { [queryKey]: { $eq: [] } }, { [queryKey]: { $eq: null } }] }
          : {};
      }
      case 'Contains': {
        return value ? { [queryKey]: { $in: value } } : {};
      }
      case 'DoesNotContain': {
        return value ? { [queryKey]: { $nin: value } } : {};
      }
      default: {
        return {};
      }
    }
  }

  private async buildMemberFieldQuery(
    field: FieldSO,
    clause: StringArrayFilterConditionClause,
    options?: { user?: UserSO },
  ): Promise<FilterQuery<DatabaseRecordModel>> {
    // 处理成员类型的字段查询
    let { operator } = clause;
    const queryKey = field.getQueryFieldKey();
    if (clause.value && clause.value.length === 1) {
      if (clause.value[0] === 'TEAM_AND_SUBTEAM_MEMBERS' || clause.value[0] === 'DIRECT_TEAM_MEMBERS') {
        // 如果是判断 is team members，自动将 Is 转换为 Contains
        if (operator === 'Is') {
          operator = 'Contains';
        } else if (operator === 'IsNot') {
          operator = 'DoesNotContain';
        }
      }
    }
    const filterValue = clause.value && (await this.getUnitIds(clause.value, options?.user?.id));
    // console.debug(`member filter value`, filterValue);
    switch (operator) {
      case 'IsEmpty': {
        return this.buildIsEmptyQuery(queryKey);
      }
      case 'IsNotEmpty': {
        return this.buildIsNotEmptyQuery(queryKey);
      }
      case 'Is': {
        // $all注意, 相当于arrays.includes, 不是全匹配
        return filterValue
          ? { $and: [{ [queryKey]: { $all: filterValue } }, { [queryKey]: { $size: filterValue.length } }] }
          : {};
      }
      case 'IsNot': {
        // $not和$all注意, 相当于arrays.includes, 不是全匹配
        return filterValue
          ? {
              $or: [
                { [queryKey]: { $not: { $all: filterValue } } },
                { $expr: { $ne: [{ $size: `$${queryKey}` }, filterValue.length] } },
              ],
            }
          : {};
      }
      case 'Contains': {
        return filterValue ? { [queryKey]: { $in: filterValue } } : {};
      }
      case 'DoesNotContain': {
        return filterValue ? { [queryKey]: { $nin: filterValue } } : {};
      }
      default: {
        return {};
      }
    }
  }

  private async buildUserFilterQuery(
    field: FieldSO,
    clause: SingleStringArrayFilterConditionClause,
    options?: { user?: UserSO },
  ): Promise<FilterQuery<DatabaseRecordModel>> {
    // 处理用户类型的字段查询
    const { operator, value } = clause;
    const queryKey = field.getQueryFieldKey();
    switch (operator) {
      case 'IsEmpty': {
        return this.buildIsEmptyQuery(queryKey);
      }
      case 'IsNotEmpty': {
        return this.buildIsNotEmptyQuery(queryKey);
      }
      case 'Is':
      case 'Contains': {
        if (value) {
          const inputValue: string[] = Array.isArray(value) ? value : [value];
          const filterValue = value ? await this.convertUserFilterValue(inputValue, options?.user?.id) : null;
          return filterValue ? { [queryKey]: { $in: filterValue } } : {};
        }
        return {};
      }
      case 'IsNot':
      case 'DoesNotContain': {
        if (value) {
          const inputValue: string[] = Array.isArray(value) ? value : [value];
          const filterValue = value ? await this.convertUserFilterValue(inputValue, options?.user?.id) : null;
          return filterValue ? { [queryKey]: { $nin: filterValue } } : {};
        }
        return {};
      }
      default: {
        return {};
      }
    }
  }

  private buildLinkFieldQuery(
    field: FieldSO,
    clause: StringArrayFilterConditionClause,
  ): FilterQuery<DatabaseRecordModel> {
    // 关联字段查询
    const { operator, value } = clause;
    const queryKey = field.getQueryFieldKey();
    switch (operator) {
      case 'IsEmpty': {
        return this.buildIsEmptyQuery(queryKey);
      }
      case 'IsNotEmpty': {
        return this.buildIsNotEmptyQuery(queryKey);
      }
      case 'Is': {
        return value ? { [queryKey]: { $all: value } } : {};
      }
      case 'IsNot': {
        return value ? { [queryKey]: { $ne: value } } : {};
      }
      case 'Contains': {
        return value ? { [queryKey]: { $in: value } } : {};
      }
      case 'DoesNotContain': {
        return value ? { [queryKey]: { $nin: value } } : {};
      }
      default: {
        return {};
      }
    }
  }

  private buildDateTimeFieldQuery(
    field: FieldSO,
    clause: DateTimeFilterConditionClause,
    options?: { user?: UserSO; useDateComparison?: boolean },
  ): FilterQuery<DatabaseRecordModel> {
    // 处理日期时间类型的字段查询
    if (!FieldUtils.isDateTimeFieldSO(field)) {
      return {};
    }
    const getTimeZone = (): string => {
      const timeZone = field.getTimeZone();
      if (timeZone === 'AUTO' || !timeZone) {
        return options?.user?.timeZone || 'UTC';
      }
      return timeZone;
    };
    // 保证不为空
    const timeZone = getTimeZone();
    // console.debug(`with time zone`, timeZone);

    const { operator, value } = clause;
    const queryKey = field.getQueryFieldKey();
    switch (operator) {
      case 'IsEmpty': {
        return this.buildIsEmptyQuery(queryKey);
      }
      case 'IsNotEmpty': {
        return this.buildIsNotEmptyQuery(queryKey);
      }
      case 'Is': {
        const range = this.handleFilterDateTimeValue(value, timeZone);
        if (range) {
          return {
            [queryKey]: {
              $gte: options?.useDateComparison ? range.start : range.start.toISOString(),
              $lte: options?.useDateComparison ? range.end : range.end.toISOString(),
            },
          };
        }
        return {};
      }
      case 'IsNot': {
        const range = this.handleFilterDateTimeValue(value, timeZone);
        if (range) {
          return {
            $or: [
              { [queryKey]: null },
              { [queryKey]: { $lt: options?.useDateComparison ? range.start : range.start.toISOString() } },
              { [queryKey]: { $gt: options?.useDateComparison ? range.end : range.end.toISOString() } },
            ],
          };
        }
        return {};
      }
      case 'IsGreater': {
        const range = this.handleFilterDateTimeValue(value, timeZone);
        if (range) {
          return { [queryKey]: { $gt: options?.useDateComparison ? range.end : range.end.toISOString() } };
        }
        return {};
      }
      case 'IsGreaterEqual': {
        const range = this.handleFilterDateTimeValue(value, timeZone);
        if (range) {
          return { [queryKey]: { $gte: options?.useDateComparison ? range.start : range.start.toISOString() } };
        }
        return {};
      }
      case 'IsLess': {
        const range = this.handleFilterDateTimeValue(value, timeZone);
        if (range) {
          return { [queryKey]: { $lt: options?.useDateComparison ? range.start : range.start.toISOString() } };
        }
        return {};
      }
      case 'IsLessEqual': {
        const range = this.handleFilterDateTimeValue(value, timeZone);
        if (range) {
          return { [queryKey]: { $lte: options?.useDateComparison ? range.end : range.end.toISOString() } };
        }
        return {};
      }
      default: {
        return {};
      }
    }
  }

  private handleFilterDateTimeValue(value: FilterDateTime, timeZone: string): { start: Date; end: Date } | null {
    // 处理 FilterDateTime 的值
    const [filterDuration] = value;
    switch (filterDuration) {
      case 'ExactDate': {
        // 精确日期
        const durationValue = value[1];
        if (durationValue === null) {
          // 没有值
          return null;
        }
        // 处理查询匹配值, 是一个ISO字符串
        const dateTime = DateTimeSO.fromString(durationValue);
        return dateTime && dateTime.getDayRange(timeZone);
      }
      case 'DateRange': {
        // 日期范围
        const rangeValue = value[1];
        if (rangeValue === null || rangeValue.trim().length === 0) {
          // 没有值
          return null;
        }
        const dateRange = DateRangeSO.safeParseDateRangeString(rangeValue);
        return dateRange && dateRange.getRange(timeZone);
      }
      case 'SomeDayBefore': {
        // 天数
        const days = value[1];
        if (days === null) {
          // 没有值
          return null;
        }
        const dateTime = DateTimeSO.now();
        dateTime.offset(-days);
        return dateTime.getDayRange(timeZone);
      }
      case 'SomeDayAfter': {
        // 天数
        const days = value[1];
        if (days === null) {
          // 没有值
          return null;
        }
        const dateTime = DateTimeSO.now();
        dateTime.offset(days);
        return dateTime.getDayRange(timeZone);
      }
      case 'Today': {
        // 今天
        const dateTime = DateTimeSO.now();
        return dateTime.getDayRange(timeZone);
      }
      case 'Tomorrow': {
        // 明天
        const dateTime = DateTimeSO.now();
        dateTime.offset(1);
        return dateTime.getDayRange(timeZone);
      }
      case 'Yesterday': {
        // 昨天
        const dateTime = DateTimeSO.now();
        dateTime.offset(-1);
        return dateTime.getDayRange(timeZone);
      }
      case 'PreviousWeek': {
        // 上一周
        const dateTime = DateTimeSO.now();
        return dateTime.getPreviousWeekRange(timeZone);
        // return {
        //   start: withTimeZone(Date.now(), timeZone).add(-1, 'week').startOf('week').valueOf(),
        //   end: withTimeZone(Date.now(), timeZone).add(-1, 'week').endOf('week').valueOf(),
        // };
      }
      case 'ThisWeek': {
        // 本周
        const dateTime = DateTimeSO.now();
        return dateTime.getThisWeekRange(timeZone);
        // return {
        //   start: withTimeZone(Date.now(), timeZone).startOf('week').valueOf(),
        //   end: withTimeZone(Date.now(), timeZone).endOf('week').valueOf(),
        // };
      }
      case 'TheNextWeek': {
        // 未来 7 天
        const dateTime = DateTimeSO.now();
        return dateTime.getNextWeekRange(timeZone);
        //   return {
        //     start: withTimeZone(Date.now(), timeZone).add(1, 'day').startOf('day').valueOf(),
        //     end: withTimeZone(Date.now(), timeZone).add(7, 'day').endOf('day').valueOf(),
        //   };
      }
      case 'TheLastWeek': {
        // 过去 7 天
        const dateTime = DateTimeSO.now();
        return dateTime.getLastWeekRange(timeZone);
        // return {
        //   start: withTimeZone(Date.now(), timeZone).add(-7, 'day').startOf('day').valueOf(),
        //   end: withTimeZone(Date.now(), timeZone).add(-1, 'day').endOf('day').valueOf(),
        // };
      }
      // 1/29 plus one month equals March 1st
      case 'PreviousMonth': {
        // 上个月
        const dateTime = DateTimeSO.now();
        return dateTime.getPreviousMonthRange(timeZone);
        // return {
        //   start: withTimeZone(Date.now(), timeZone).add(-1, 'month').startOf('month').valueOf(),
        //   end: withTimeZone(Date.now(), timeZone).add(-1, 'month').endOf('month').valueOf(),
        // };
      }
      case 'ThisMonth': {
        // 本月
        const dateTime = DateTimeSO.now();
        return dateTime.getThisMonthRange(timeZone);
        // return {
        //   start: withTimeZone(Date.now(), timeZone).startOf('month').valueOf(),
        //   end: withTimeZone(Date.now(), timeZone).endOf('month').valueOf(),
        // };
      }
      case 'TheNextMonth': {
        // 未来 30 天
        const dateTime = DateTimeSO.now();
        return dateTime.getNextMonthRange(timeZone);
        // return {
        //   start: withTimeZone(Date.now(), timeZone).add(1, 'day').startOf('day').valueOf(),
        //   end: withTimeZone(Date.now(), timeZone).add(30, 'day').endOf('day').valueOf(),
        // };
      }
      case 'TheLastMonth': {
        // 过去 30 天
        const dateTime = DateTimeSO.now();
        return dateTime.getLastMonthRange(timeZone);
        // return {
        //   start: withTimeZone(Date.now(), timeZone).add(-30, 'day').startOf('day').valueOf(),
        //   end: withTimeZone(Date.now(), timeZone).add(-1, 'day').endOf('day').valueOf(),
        // };
      }
      case 'ThisQuarter': {
        // 本季度
        const dateTime = DateTimeSO.now();
        return dateTime.getThisQuarterRange(timeZone);
        // return {
        //   start: withTimeZone(Date.now(), timeZone).startOf('quarter').valueOf(),
        //   end: withTimeZone(Date.now(), timeZone).endOf('quarter').valueOf(),
        // };
      }
      case 'ThisYear': {
        // 今年
        const dateTime = DateTimeSO.now();
        return dateTime.getThisYearRange(timeZone);
        // return {
        //   start: withTimeZone(Date.now(), timeZone).startOf('year').valueOf(),
        //   end: withTimeZone(Date.now(), timeZone).endOf('year').valueOf(),
        // };
      }
      default: {
        // 其他情况, 没有value
        return null;
      }
    }
  }

  /**
   * 用于 CreatedBy 和 ModifiedBy 的特殊处理
   *
   * 这两个字段存储的是 `userId`，
   * 但是在 filter 中传递的是 `memberId` 或特殊条件
   *
   * @param filterValue memberId | 特殊条件
   * @param currentUserId 当前用户的 userId
   * @returns userId | userId[]
   */
  private async convertUserFilterValue(filterValue: string[], currentUserId?: string): Promise<string[]> {
    if (!filterValue?.length) {
      return filterValue;
    }

    const values = await Promise.all(
      filterValue.map(async (cur) => {
        if (cur.toUpperCase() === 'SELF') {
          return currentUserId || cur;
        }
        if (cur.toUpperCase() === 'DIRECT_TEAM_MEMBERS') {
          if (!currentUserId) {
            return cur;
          }
          return this.findAllUserIdsInUserTeam(currentUserId);
        }
        if (cur.toUpperCase() === 'TEAM_AND_SUBTEAM_MEMBERS') {
          if (!currentUserId) {
            return cur;
          }
          return this.findAllUserIdsInUserTeamAndSubTeams(currentUserId);
        }

        return this.tryConvertMemberIdToUserId(cur);
      }),
    );

    return [...new Set<string>(values.flat())];
  }

  /**
   * 用于 Member 的特殊处理
   *
   * @param filterValue unitId | 特殊条件
   * @param currentUserId 当前用户的 userId
   * @returns unitId | unitId[]
   */
  private async getUnitIds(filterValue: string[] | null, currentUserId?: string): Promise<string[]> {
    if (!filterValue) {
      return [];
    }

    const unitIds = await Promise.all(
      filterValue.map(async (cur) => {
        if (cur.toUpperCase() === 'SELF') {
          return currentUserId ? this.getSelfUnitId(currentUserId) : [];
        }
        if (cur.toUpperCase() === 'DIRECT_TEAM_MEMBERS') {
          return currentUserId ? this.findAllUnitIdsInUserTeam(currentUserId) : [];
        }
        if (cur.toUpperCase() === 'TEAM_AND_SUBTEAM_MEMBERS') {
          return currentUserId ? this.findAllUnitIdsInUserTeamAndSubTeams(currentUserId) : [];
        }
        return cur;
      }),
    );
    const flattenedUnitIds = unitIds.flat();

    return [...new Set<string>(flattenedUnitIds)];
  }

  private async tryConvertMemberIdToUserId(memberId: string): Promise<string> {
    try {
      const member = await MemberSO.init(memberId);
      return member.userId;
    } catch (_) {
      return memberId;
    }
  }

  private async getSelfUnitId(currentUserId: string): Promise<string> {
    try {
      const member = await UnitFactory.findMember(currentUserId, this.database.spaceId);
      return member ? member.id : 'Self';
    } catch (error) {
      // Handle error as per requirement
      Logger.error('find member error', error);
      return 'Self';
    }
  }

  private async findAllUnitIdsInUserTeam(currentUserId: string): Promise<string[]> {
    const unitIds = new Set<string>();
    const currentUnitId = await this.getSelfUnitId(currentUserId);
    const teams = await TeamSO.findTeamsByMemberId(currentUnitId);

    for (const team of teams) {
      const uniqueMembers = await team.getDirectMembers();
      uniqueMembers.map((member) => member.id).forEach((unitId) => unitIds.add(unitId));
    }

    return [...unitIds];
  }

  private async findAllUnitIdsInUserTeamAndSubTeams(currentUserId: string): Promise<string[]> {
    const unitIds = new Set<string>();
    const currentUnitId = await this.getSelfUnitId(currentUserId);
    const teams = await TeamSO.findTeamsByMemberId(currentUnitId);

    for (const team of teams) {
      const uniqueMembers = await team.getMembers(true);
      uniqueMembers.map((member) => member.id).forEach((unitId) => unitIds.add(unitId));
    }

    return [...unitIds];
  }

  private async findAllUserIdsInUserTeam(currentUserId: string): Promise<string[]> {
    const userIds = new Set<string>();
    const currentUnitId = await this.getSelfUnitId(currentUserId);
    const teams = await TeamSO.findTeamsByMemberId(currentUnitId);

    for (const team of teams) {
      const uniqueMembers = await team.getDirectMembers();
      uniqueMembers.map((member) => member.userId).forEach((userId) => userIds.add(userId));
    }

    return [...userIds];
  }

  private async findAllUserIdsInUserTeamAndSubTeams(currentUserId: string): Promise<string[]> {
    const userIds = new Set<string>();
    const currentUnitId = await this.getSelfUnitId(currentUserId);
    const teams = await TeamSO.findTeamsByMemberId(currentUnitId);

    for (const team of teams) {
      const uniqueMembers = await team.getMembers(true);
      uniqueMembers.map((member) => member.userId).forEach((userId) => userIds.add(userId));
    }

    return [...userIds];
  }

  async matchRecord(record: RecordVO): Promise<RecordVO | undefined> {
    if (!this.filter || !this.conjunction || this.conditions.length === 0) {
      console.warn('Filter.matchRecord: No filter or conditions defined');
      return record;
    }
    // 转换成 自动化动作过滤器
    const conditions: ActionFilterCondition[] = [];
    const fields = this.database.getFields();
    for (const { fieldId, fieldTemplateId, fieldType, clause } of this.conditions) {
      const { operator, value } = clause;
      // field key maybe fieldId or fieldTemplateId
      const fieldKey = fieldId || fieldTemplateId;
      // 未完成配置
      if (!fieldKey) {
        console.warn('Filter.matchRecord: No field key defined for condition', fieldId, fieldTemplateId);
        return undefined;
      }
      const field = fields.find((f) => f.id === fieldKey || f.templateId === fieldKey);
      // 字段不存在
      if (!field) {
        console.log('Filter.matchRecord: Field not found for condition', fieldId, fieldTemplateId);
        return undefined;
      }
      if (operator === 'IsRepeat') {
        return undefined;
      }
      if (operator === 'IsEmpty' || operator === 'IsNotEmpty') {
        conditions.push({
          input: record.cells[field.id].data,
          dataType: 'unknown',
          operator,
        });
        continue;
      }
      // 未完成配置
      if (value == null && field.type !== 'CHECKBOX') {
        return undefined;
      }
      const input = record.cells[field.id].data;
      if (!input) {
        return undefined;
      }

      const cvt = CellValueConvertorFactory.create(field.toBO(), this._ctx);
      const basicValueType = cvt.basicValueType();
      switch (basicValueType) {
        case BasicValueType.String: {
          const operators = ['Is', 'IsNot', 'Contains', 'DoesNotContain'];
          if (typeof input !== 'string' || !operators.includes(operator)) {
            return undefined;
          }
          if (typeof value !== 'string') {
            return undefined;
          }
          conditions.push({
            input,
            dataType: 'string',
            operator: operator as 'Is' | 'IsNot' | 'Contains' | 'DoesNotContain',
            value,
          });
          break;
        }
        case BasicValueType.Number: {
          const operators = ['Is', 'IsNot', 'IsGreater', 'IsGreaterEqual', 'IsLess', 'IsLessEqual'];
          if (typeof input !== 'number' || !operators.includes(operator)) {
            return undefined;
          }
          const conditionValue = value;
          const numberFilterValue: number | null =
            conditionValue?.toString().trim() === '' ? null : Number(conditionValue);
          if (!numberFilterValue) {
            return undefined;
          }
          conditions.push({
            input,
            dataType: 'number',
            operator: operator as 'Is' | 'IsNot' | 'IsGreater' | 'IsGreaterEqual' | 'IsLess' | 'IsLessEqual',
            value: numberFilterValue,
          });
          break;
        }
        case BasicValueType.Array: {
          const operators = ['Is', 'IsNot', 'Contains', 'DoesNotContain'];
          if (!Array.isArray(input) || !operators.includes(operator)) {
            return undefined;
          }
          // 过滤日期类型, 日期的过滤值也可能是数组类型
          if (fieldType === 'DATETIME' || fieldType === 'CREATED_TIME' || fieldType === 'MODIFIED_TIME') {
            return undefined;
          }
          const convertOp = () => {
            if (operator === 'Contains') {
              return 'ContainsAny';
            }
            if (operator === 'DoesNotContain') {
              return 'ContainsNone';
            }
            return operator as 'Is' | 'IsNot';
          };
          const compatibleConditionValue = (): (string | number)[] => {
            if (Array.isArray(clause.value)) {
              return clause.value;
            }
            if (typeof clause.value === 'string' || typeof clause.value === 'number') {
              return [clause.value];
            }
            return [];
          };
          conditions.push({
            input,
            dataType: 'array',
            operator: convertOp(),
            value: compatibleConditionValue(),
          });
          break;
        }
        case BasicValueType.DateTime: {
          const operators = ['Is', 'IsNot', 'IsGreater', 'IsGreaterEqual', 'IsLess', 'IsLessEqual'];
          if (typeof input !== 'string' || !operators.includes(operator)) {
            return undefined;
          }
          if (fieldType !== 'DATETIME' && fieldType !== 'CREATED_TIME' && fieldType !== 'MODIFIED_TIME') {
            return undefined;
          }
          const [filterDuration, durationValue] = clause.value;
          const convertDuration = () => {
            if (filterDuration === 'ExactDate' || filterDuration === 'DateRange') {
              if (filterDuration.length === 1 || typeof durationValue !== 'string') {
                return { success: false };
              }
              return { success: true, value: { mode: filterDuration, datetime: durationValue } };
            }
            if (filterDuration === 'SomeDayBefore' || filterDuration === 'SomeDayAfter') {
              if (filterDuration.length === 1 || typeof durationValue !== 'number') {
                return { success: false };
              }
              return { success: true, value: { mode: filterDuration, days: durationValue } };
            }
            return { success: true, value: { mode: filterDuration } };
          };
          const duration = convertDuration();
          if (!duration.success || !duration.value) {
            return undefined;
          }
          conditions.push({
            input,
            dataType: 'datetime',
            operator: operator as 'Is' | 'IsNot' | 'IsGreater' | 'IsGreaterEqual' | 'IsLess' | 'IsLessEqual',
            value: duration.value,
          });
          break;
        }
        case BasicValueType.Boolean: {
          if (typeof input !== 'boolean' || operator !== 'Is') {
            return undefined;
          }
          if (Array.isArray(value)) {
            return undefined;
          }
          conditions.push({
            input,
            dataType: 'boolean',
            operator,
            value: value || undefined,
          });
          break;
        }
        default:
          throw new Error(`Unsupported field type: ${basicValueType}`);
      }
    }
    const filtersSO = new FiltersSO({ conjunction: this.conjunction, conditions });
    const match = await filtersSO.calc();
    return match ? record : undefined;
  }

  /**
   * Assuming it is now Feb 8 01:56:55 UTC+8
   * Today: [Today 00:00, Tomorrow 23:59] UTC+8
   * Tomorrow: [Tomorrow 00:00, The day after tomorrow 23:59] UTC+8
   * Yesterday: [yesterday 00:00, today 23:59] UTC+8
   * Next 7 days: [Today 00:00, Feb 16 23:59] UTC+8
   * Last 7 days: [February 1st 00:00, today 23:59] UTC+8
   * In the next 30 days: [Today 00:00, March 9th 23:59] UTC+8
   * In the past 30 days: [January 8th 00:00, today 23:59] UTC+8
   * This week: Monday to Friday of the current week
   * Last week: Monday to Friday of the previous week
   * This month: [February 1st 00:00, February 28th 23:59] UTC+8
   * Last month: [January 1st 00:00, January 31st 23:59] UTC+8
   * This year: [January 1st 00:00, December 31st 23:59] UTC+8
   * @deprecated 即将删除代码, 禁止使用
   */
  static getTimeRange(
    filterDuration: FilterDuration,
    time: number | string | null | undefined,
    timeZone?: string,
  ): { start: number; end: number } {
    switch (filterDuration) {
      case 'ExactDate': {
        if (time !== undefined) {
          if (typeof time === 'string') {
            // time is a string, it is an iso string
            return {
              start: withTimeZone(new Date(time).getTime(), timeZone).startOf('day').valueOf(),
              end: withTimeZone(new Date(time).getTime(), timeZone).endOf('day').valueOf(),
            };
          }
          return {
            start: withTimeZone(time, timeZone).startOf('day').valueOf(),
            end: withTimeZone(time, timeZone).endOf('day').valueOf(),
          };
        }
        throw new Error('ExactDate has to calculate with timestamp');
      }
      case 'DateRange': {
        if (typeof time === 'string') {
          // Time intervals are separated by '/', iso string
          if (time.includes('/')) {
            const [startDate, endDate] = time.split('/');
            return {
              start: withTimeZone(new Date(startDate).getTime(), timeZone).startOf('day').valueOf(),
              end: withTimeZone(new Date(endDate).getTime(), timeZone).endOf('day').valueOf(),
            };
          }
          const [startDate, endDate] = time.split('-');
          return { start: Number(startDate), end: Number(endDate) };
        }
        throw new Error('ExactDate has to calculate with timestamp');
      }
      case 'Today': {
        return {
          start: withTimeZone(Date.now(), timeZone).startOf('day').valueOf(),
          end: withTimeZone(Date.now(), timeZone).endOf('day').valueOf(),
        };
      }
      case 'Tomorrow': {
        return {
          start: withTimeZone(Date.now(), timeZone).add(1, 'day').startOf('day').valueOf(),
          end: withTimeZone(Date.now(), timeZone).add(1, 'day').endOf('day').valueOf(),
        };
      }
      case 'Yesterday': {
        return {
          start: withTimeZone(Date.now(), timeZone).add(-1, 'day').startOf('day').valueOf(),
          end: withTimeZone(Date.now(), timeZone).add(-1, 'day').endOf('day').valueOf(),
        };
      }
      case 'TheNextWeek': {
        return {
          start: withTimeZone(Date.now(), timeZone).add(1, 'day').startOf('day').valueOf(),
          end: withTimeZone(Date.now(), timeZone).add(7, 'day').endOf('day').valueOf(),
        };
      }
      case 'TheLastWeek': {
        return {
          start: withTimeZone(Date.now(), timeZone).add(-7, 'day').startOf('day').valueOf(),
          end: withTimeZone(Date.now(), timeZone).add(-1, 'day').endOf('day').valueOf(),
        };
      }
      // 1/29 plus one month equals March 1st
      case 'TheNextMonth': {
        return {
          start: withTimeZone(Date.now(), timeZone).add(1, 'day').startOf('day').valueOf(),
          end: withTimeZone(Date.now(), timeZone).add(30, 'day').endOf('day').valueOf(),
        };
      }
      case 'TheLastMonth': {
        return {
          start: withTimeZone(Date.now(), timeZone).add(-30, 'day').startOf('day').valueOf(),
          end: withTimeZone(Date.now(), timeZone).add(-1, 'day').endOf('day').valueOf(),
        };
      }
      case 'ThisWeek': {
        return {
          start: withTimeZone(Date.now(), timeZone).startOf('week').valueOf(),
          end: withTimeZone(Date.now(), timeZone).endOf('week').valueOf(),
        };
      }
      case 'PreviousWeek': {
        return {
          start: withTimeZone(Date.now(), timeZone).add(-1, 'week').startOf('week').valueOf(),
          end: withTimeZone(Date.now(), timeZone).add(-1, 'week').endOf('week').valueOf(),
        };
      }
      case 'ThisMonth': {
        return {
          start: withTimeZone(Date.now(), timeZone).startOf('month').valueOf(),
          end: withTimeZone(Date.now(), timeZone).endOf('month').valueOf(),
        };
      }
      case 'PreviousMonth': {
        return {
          start: withTimeZone(Date.now(), timeZone).add(-1, 'month').startOf('month').valueOf(),
          end: withTimeZone(Date.now(), timeZone).add(-1, 'month').endOf('month').valueOf(),
        };
      }
      case 'ThisQuarter': {
        return {
          start: withTimeZone(Date.now(), timeZone).startOf('quarter').valueOf(),
          end: withTimeZone(Date.now(), timeZone).endOf('quarter').valueOf(),
        };
      }
      case 'ThisYear': {
        return {
          start: withTimeZone(Date.now(), timeZone).startOf('year').valueOf(),
          end: withTimeZone(Date.now(), timeZone).endOf('year').valueOf(),
        };
      }
      case 'SomeDayBefore': {
        if (typeof time === 'number') {
          return {
            start: withTimeZone(Date.now(), timeZone).add(-time, 'day').startOf('day').valueOf(),
            end: withTimeZone(Date.now(), timeZone).add(-time, 'day').endOf('day').valueOf(),
          };
        }
        throw new Error('SomeDayBefore has to calculate with number');
      }
      case 'SomeDayAfter': {
        if (typeof time === 'number') {
          return {
            start: withTimeZone(Date.now(), timeZone).add(time, 'day').startOf('day').valueOf(),
            end: withTimeZone(Date.now(), timeZone).add(time, 'day').endOf('day').valueOf(),
          };
        }
        throw new Error('SomeDayAfter has to calculate with number');
      }
      default: {
        throw new Error(`${filterDuration} is not a never type`);
      }
    }
  }
}
