import { parse } from '@rsql/parser';
import { ViewFilter } from '@bika/types/database/bo';
import { CustomAstNodeVisitor } from './ast-node-visitor';
import { AstNodeParser } from './ast-parser';
import { UserSO } from '../../../../user/server';
import { DatabaseSO } from '../../database-so';

export class AstFilter {
  private readonly user: UserSO;

  private database: DatabaseSO;

  constructor(user: UserSO, database: DatabaseSO) {
    this.user = user;
    this.database = database;
  }

  toViewFilter(filterAsRSql: string, options?: { timeZone?: string }): ViewFilter {
    const astNode = parse(filterAsRSql);
    const parser = new AstNodeParser(astNode);
    const visitor = new CustomAstNodeVisitor(this.user, this.database, options);
    return parser.accept(visitor);
  }
}
