import { <PERSON>Field, FilterCondition, ViewFilter } from '@bika/types/database/bo';
import { FilterConjunction } from '@bika/types/shared';
import { ComparisonNode, ExpressionNode, LogicNode, SelectorNode, ValueNode } from './ast';
import { SqlAstNodeVisitor, transformToFieldFilterOperator } from './types';
import { ValueAdapter } from './value-adapter';
import { UserSO } from '../../../../user/server';
import { DatabaseSO } from '../../database-so';

export class CustomAstNodeVisitor extends SqlAstNodeVisitor<ViewFilter> {
  private readonly conditions: FilterCondition[] = [];

  private rootConjunction: FilterConjunction | null = null;

  private isRootLevel = true;

  private readonly user: UserSO;

  private readonly database: DatabaseSO;

  private readonly timeZone?: string;

  constructor(user: UserSO, database: DatabaseSO, options?: { timeZone?: string }) {
    super();
    this.user = user;
    this.database = database;
    this.timeZone = options?.timeZone || user.timeZone;
  }

  visit(node: ExpressionNode): ViewFilter {
    // 实现具体的访问逻辑
    switch (node.type) {
      case 'LOGIC': {
        this.visitLogical(node);
        break;
      }
      case 'COMPARISON': {
        this.visitComparison(node);
        break;
      }
      default:
        throw new Error(`unknown expression node type`);
    }

    // 处理完成后重置根级别标志
    this.isRootLevel = false;

    // 返回最终的 Filter 对象
    return {
      // 默认逻辑连接符为 AND
      conjunction: this.rootConjunction ?? 'And',
      conditions: [],
      conds: this.conditions,
    };
  }

  /**
   * 提取逻辑连接符
   */
  private extractLogicConjunction(operator: string): FilterConjunction {
    switch (operator) {
      case 'and':
      case ';':
        return 'And';
      case 'or':
      case ',':
        return 'Or';
      default:
        throw new Error(`Unknown operator: ${operator}`);
    }
  }

  private visitLogical(node: LogicNode) {
    // console.log(`处理逻辑节点: ${node.type}`);
    // And 或 Or 逻辑节点, 解析来所有的operator必须都一致, 不可以and和or混用
    const { left, operator, right } = node;
    // 当前逻辑连接符
    const currentConjunction = this.extractLogicConjunction(operator);

    // 在根级别设置整体的逻辑连接
    if (this.isRootLevel) {
      if (this.rootConjunction && this.rootConjunction !== currentConjunction) {
        // 不可以混用 AND 和 OR
        throw new Error('Mixed AND and OR operators are not allowed in the same expression');
      }
      this.rootConjunction = currentConjunction;
    }
    // 继续递归访问左右子节点
    this.visit(left);
    this.visit(right);
  }

  private visitComparison(node: ComparisonNode) {
    // left是字段名, right是值, operator是操作符
    const { left, operator, right } = node;
    // console.log(
    //   '处理比较节点:',
    //   `字段: ${left.selector}`,
    //   `操作符: ${operator}`,
    //   `值类型: ${typeof right.value}`,
    //   `值: ${right.value}`,
    // );
    // 转换成字段支持的操作符
    const matchedOperator = transformToFieldFilterOperator(operator);
    if (!matchedOperator) {
      // 不支持的操作符?
      throw new Error(`Unsupported operator: ${operator}`);
    }
    // 转换成表字段
    const field = this.visitSelectNode(left);
    // 转换成条件子句
    const value = this.visitValueNode(right);
    const condition = new ValueAdapter(this.user, field, matchedOperator, {
      timeZone: this.timeZone,
    }).transformToFilterCondition(value);
    this.conditions.push(condition);
  }

  private visitSelectNode(node: SelectorNode): DatabaseField {
    // console.log(`处理选择器节点: ${node.selector}`);
    // 检查字段是否存在
    const fieldKey = node.selector;
    const field = this.database.findFieldByFieldKey(fieldKey);
    if (!field) {
      // 是否可以不抛异常忽略?
      throw new Error(`Field with key ${fieldKey} not found in the database`);
    }
    return field.toBO();
  }

  private visitValueNode(node: ValueNode): string | string[] {
    // console.log(`处理值节点: ${node.value}`);
    // 根据操作符和值匹配输入数据类型是否匹配
    return node.value;
  }
}
