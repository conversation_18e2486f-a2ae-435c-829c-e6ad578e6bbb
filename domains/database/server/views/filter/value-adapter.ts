import { safeStringToNumber } from '@bika/domains/shared/server';
import { UserSO } from '@bika/domains/user/server';
import {
  BooleanFilterConditionClause,
  DatabaseField,
  DatabaseMultiSelectField,
  DatabaseSingleSelectField,
  DateTimeFilterConditionClause,
  FieldFilterOperator,
  FilterCondition,
  FixedDurations,
  NumberFilterConditionClause,
  PresentFilterConditionClause,
  SingleStringArrayFilterConditionClause,
  StringArrayFilterConditionClause,
  StringFilterConditionClause,
} from '@bika/types/database/bo';
import { DateRangeSO, DateTimeSO, iStringParse } from '@bika/types/system';

export class ValueAdapter {
  private readonly user: UserSO;

  private readonly field: DatabaseField;

  private readonly operator: FieldFilterOperator;

  private timeZone?: string;

  constructor(user: UserSO, field: DatabaseField, operator: FieldFilterOperator, options?: { timeZone?: string }) {
    this.user = user;
    this.field = field;
    this.operator = operator;
    this.timeZone = options?.timeZone || user.timeZone;
  }

  /**
   * 大部分都具备的条件: 为空 | 不为空
   * 只有复选框没有
   */
  buildPresentFilterConditionClause(): PresentFilterConditionClause {
    return { operator: this.operator === 'Is' ? 'IsEmpty' : 'IsNotEmpty' };
  }

  transformToFilterCondition(value: string | string[]): FilterCondition {
    const base = {
      fieldId: this.field.id,
      fieldTemplateId: this.field.templateId,
    };
    switch (this.field.type) {
      case 'SINGLE_TEXT':
      case 'LONG_TEXT':
      case 'URL':
      case 'EMAIL':
      case 'PHONE':
      case 'FORMULA': {
        if (
          typeof value === 'string' &&
          this.isNullString(value) &&
          (this.operator === 'Is' || this.operator === 'IsNot')
        ) {
          // 为空的情况, 并且是 == 或 !=, ==NULL代表isEmpty
          return {
            ...base,
            fieldType: this.field.type,
            clause: this.buildPresentFilterConditionClause(),
          };
        }
        return {
          ...base,
          fieldType: this.field.type,
          clause: this.buildStringClause(this.ensureSingleString(value)),
        };
      }
      case 'NUMBER':
      case 'PERCENT':
      case 'CURRENCY':
      case 'RATING':
      case 'AUTO_NUMBER': {
        if (
          typeof value === 'string' &&
          this.isNullString(value) &&
          (this.operator === 'Is' || this.operator === 'IsNot')
        ) {
          // 为空的情况, 并且是 == 或 !=, ==NULL代表isEmpty
          return {
            ...base,
            fieldType: this.field.type,
            clause: this.buildPresentFilterConditionClause(),
          };
        }
        return {
          ...base,
          fieldType: this.field.type,
          clause: this.buildNumberClause(this.ensureSingleString(value)),
        };
      }
      case 'CHECKBOX': {
        return {
          ...base,
          fieldType: this.field.type,
          clause: this.buildBooleanClause(this.ensureSingleString(value)),
        };
      }
      case 'SINGLE_SELECT': {
        if (
          typeof value === 'string' &&
          this.isNullString(value) &&
          (this.operator === 'Is' || this.operator === 'IsNot')
        ) {
          // 为空的情况, 并且是 == 或 !=, ==NULL代表isEmpty
          return {
            ...base,
            fieldType: this.field.type,
            clause: this.buildPresentFilterConditionClause(),
          };
        }
        return {
          ...base,
          fieldType: this.field.type,
          clause: this.buildSingleSelectFilterConditionClause(this.field, value),
        };
      }
      case 'MULTI_SELECT': {
        if (
          typeof value === 'string' &&
          this.isNullString(value) &&
          (this.operator === 'Is' || this.operator === 'IsNot')
        ) {
          // 为空的情况, 并且是 == 或 !=, ==NULL代表isEmpty
          return {
            ...base,
            fieldType: this.field.type,
            clause: this.buildPresentFilterConditionClause(),
          };
        }
        return {
          ...base,
          fieldType: this.field.type,
          clause: this.buildMultiSelectFilterConditionClause(this.field, this.ensureStringArray(value)),
        };
      }
      case 'MEMBER': {
        if (
          typeof value === 'string' &&
          this.isNullString(value) &&
          (this.operator === 'Is' || this.operator === 'IsNot')
        ) {
          // 为空的情况, 并且是 == 或 !=, ==NULL代表isEmpty
          return {
            ...base,
            fieldType: this.field.type,
            clause: this.buildPresentFilterConditionClause(),
          };
        }
        return {
          ...base,
          fieldType: this.field.type,
          clause: this.buildMemberFilterConditionClause(this.ensureStringArray(value)),
        };
      }
      case 'CREATED_BY':
      case 'MODIFIED_BY': {
        if (
          typeof value === 'string' &&
          this.isNullString(value) &&
          (this.operator === 'Is' || this.operator === 'IsNot')
        ) {
          // 为空的情况, 并且是 == 或 !=, ==NULL代表isEmpty
          return {
            ...base,
            fieldType: this.field.type,
            clause: this.buildPresentFilterConditionClause(),
          };
        }
        return {
          ...base,
          fieldType: this.field.type,
          clause: this.buildCreatedByOrModifiedByFilterConditionClause(value),
        };
      }
      case 'LINK': {
        if (
          typeof value === 'string' &&
          this.isNullString(value) &&
          (this.operator === 'Is' || this.operator === 'IsNot')
        ) {
          // 为空的情况, 并且是 == 或 !=, ==NULL代表isEmpty
          return {
            ...base,
            fieldType: this.field.type,
            clause: this.buildPresentFilterConditionClause(),
          };
        }
        return {
          ...base,
          fieldType: this.field.type,
          clause: this.buildLinkFilterConditionClause(this.ensureStringArray(value)),
        };
      }
      case 'DATETIME':
      case 'CREATED_TIME':
      case 'MODIFIED_TIME': {
        if (
          typeof value === 'string' &&
          this.isNullString(value) &&
          (this.operator === 'Is' || this.operator === 'IsNot')
        ) {
          // 为空的情况, 并且是 == 或 !=, ==NULL代表isEmpty
          return {
            ...base,
            fieldType: this.field.type,
            clause: this.buildPresentFilterConditionClause(),
          };
        }
        return {
          ...base,
          fieldType: this.field.type,
          clause: this.buildDateTimeFilterConditionClause(this.ensureSingleString(value)),
        };
      }
      case 'LOOKUP':
      case 'DATERANGE':
      case 'ATTACHMENT':
      case 'WORK_DOC': {
        // 这些只支持isEmpty和isNotEmpty操作符
        if (
          typeof value === 'string' &&
          this.isNullString(value) &&
          (this.operator === 'Is' || this.operator === 'IsNot')
        ) {
          // 为空的情况, 并且是 == 或 !=, ==NULL代表isEmpty
          return {
            ...base,
            fieldType: this.field.type,
            clause: this.buildPresentFilterConditionClause(),
          };
        }
        throw new Error(`Unsupported operator ${this.operator} for field type ${this.field.type}`);
      }
      default: {
        // 其他都是未开发的字段类型
        throw new Error(`Unsupported field type: ${this.field.type}`);
      }
    }
  }

  ensureSingleString(value: string | string[]): string {
    if (Array.isArray(value)) {
      throw new Error(`Expected single value for field ${this.field.id}, got array: ${JSON.stringify(value)}`);
    }
    return value;
  }

  ensureStringArray(value: string | string[]): string[] {
    if (typeof value === 'string') {
      throw new Error(`Expected array for field ${this.field.id}, got string: ${value}`);
    }
    return value;
  }

  isNullString(value: string): boolean {
    // 检查是否是null字符串
    return value === 'null' || value === 'NULL' || value === '';
  }

  private buildStringClause(value: string): StringFilterConditionClause {
    switch (this.operator) {
      case 'Is':
      case 'IsNot':
      case 'Contains':
      case 'DoesNotContain':
        return {
          operator: this.operator,
          value: value.trim(),
        };
      default:
        throw new Error(`Unsupported operator ${this.operator} for string field`);
    }
  }

  private buildNumberClause(value: string): NumberFilterConditionClause {
    switch (this.operator) {
      case 'Is':
      case 'IsNot':
      case 'IsLess':
      case 'IsLessEqual':
      case 'IsGreater':
      case 'IsGreaterEqual': {
        const numeric = safeStringToNumber(value);
        if (numeric === null) {
          throw new Error(`Invalid number value: ${value} for field ${this.field.id}`);
        }
        return {
          operator: this.operator,
          value: numeric,
        };
      }
      default:
        throw new Error(`Unsupported operator ${this.operator} for number field`);
    }
  }

  private buildBooleanClause(value: string): BooleanFilterConditionClause {
    const convertToBoolean = (input: string): boolean => {
      if (typeof input === 'boolean') {
        return input === true;
      }
      if (typeof input === 'string') {
        return input === 'true' || input === '1';
      }
      return false;
    };
    switch (this.operator) {
      case 'Is': {
        // 字符串兼容: 'true', 'false', '1', '0', 'null'
        const boolValue = convertToBoolean(value);
        return {
          operator: this.operator,
          value: boolValue,
        };
      }

      default:
        throw new Error(`Unsupported operator ${this.operator} for boolean field`);
    }
  }

  private buildSingleSelectFilterConditionClause(
    singleSelectField: DatabaseSingleSelectField,
    value: string | string[],
  ): SingleStringArrayFilterConditionClause {
    switch (this.operator) {
      case 'Is':
      case 'IsNot': {
        const optionKey = this.ensureSingleString(value);
        const option = singleSelectField.property.options.find((opt) => opt.id === optionKey || opt.name === optionKey);
        return {
          operator: this.operator,
          value: option && option.id ? option.id : optionKey,
        };
      }
      case 'Contains':
      case 'DoesNotContain': {
        const optionKeys = this.ensureStringArray(value);
        const options = singleSelectField.property.options.filter((opt) => {
          if (opt.id && optionKeys.includes(opt.id)) {
            return true;
          }
          if (opt.name && optionKeys.includes(iStringParse(opt.name))) {
            return true;
          }
          return false;
        });
        return {
          operator: this.operator,
          value: options.map((opt) => opt.id!),
        };
      }
      default:
        throw new Error(`Unsupported operator ${this.operator} for single string array field`);
    }
  }

  private buildMultiSelectFilterConditionClause(
    multiSelect: DatabaseMultiSelectField,
    value: string[],
  ): StringArrayFilterConditionClause {
    switch (this.operator) {
      case 'Is':
      case 'IsNot':
      case 'Contains':
      case 'DoesNotContain': {
        const options = multiSelect.property.options.filter((opt) => {
          if (opt.id && value.includes(opt.id)) {
            return true;
          }
          if (opt.name && value.includes(iStringParse(opt.name))) {
            return true;
          }
          return false;
        });
        return {
          operator: this.operator,
          value: options.map((opt) => opt.id!),
        };
      }
      default:
        throw new Error(`Unsupported operator ${this.operator} for string array field`);
    }
  }

  private buildCreatedByOrModifiedByFilterConditionClause(
    value: string | string[],
  ): SingleStringArrayFilterConditionClause {
    switch (this.operator) {
      case 'Is':
      case 'IsNot': {
        return {
          operator: this.operator,
          value: this.ensureSingleString(value),
        };
      }
      case 'Contains':
      case 'DoesNotContain': {
        return {
          operator: this.operator,
          value: this.ensureStringArray(value),
        };
      }
      default:
        throw new Error(`Unsupported operator ${this.operator} for single string array field`);
    }
  }

  private buildMemberFilterConditionClause(value: string[]): StringArrayFilterConditionClause {
    switch (this.operator) {
      case 'Is':
      case 'IsNot':
      case 'Contains':
      case 'DoesNotContain': {
        return {
          operator: this.operator,
          value,
        };
      }
      default:
        throw new Error(`Unsupported operator ${this.operator} for string array field`);
    }
  }

  private buildLinkFilterConditionClause(value: string[]): StringArrayFilterConditionClause {
    switch (this.operator) {
      case 'Is':
      case 'IsNot':
      case 'Contains':
      case 'DoesNotContain': {
        return {
          operator: this.operator,
          value,
        };
      }
      default:
        throw new Error(`Unsupported operator ${this.operator} for string array field`);
    }
  }

  private buildDateTimeFilterConditionClause(value: string): DateTimeFilterConditionClause {
    // 情况比较特殊, 用户输入的字符串可能为已格式化的日期时间字符串,
    // 比如 '2023-10-02', 假设用户设置在东八区, 根据时区得到ISO格式的时间字符串为 '2023-10-01T16:00:00.000Z'
    // 比如不是日期时间格式的字符串, 可能是Today, Yesterday,
    switch (this.operator) {
      case 'Is':
      case 'IsNot':
      case 'IsLess':
      case 'IsLessEqual':
      case 'IsGreater':
      case 'IsGreaterEqual': {
        const isoString = DateTimeSO.safeParseToUtcIsoString(value, this.timeZone);
        if (isoString !== null) {
          // 有效的ISO字符串
          return {
            operator: this.operator,
            value: ['ExactDate', isoString],
          };
        }
        const dateRangeString = DateRangeSO.safeParseDateRangeString(value, this.user.timeZone);
        if (dateRangeString !== null) {
          // 有效的日期范围字符串
          return {
            operator: this.operator,
            value: ['DateRange', dateRangeString.toCellDate()],
          };
        }
        // 都不是, 检查是否是固定期间变量
        const fixedDuration = FixedDurations.find((duration) => duration.toLowerCase() === value.toLowerCase());
        if (fixedDuration) {
          return {
            operator: this.operator,
            value: [fixedDuration],
          };
        }
        // 无法解析的输入值
        throw new Error(`Invalid date time value: ${value} for field ${this.field.id}`);
      }
      default:
        throw new Error(`Unsupported operator ${this.operator} for date time field`);
    }
  }
}
