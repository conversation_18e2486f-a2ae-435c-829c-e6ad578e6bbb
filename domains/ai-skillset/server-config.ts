import { SkillsetSelectBO } from '@bika/types/skill/bo';

// Featured 的Skillsets，用于打开界面的时候，默认向服务端请求的 skillsets
export const FeaturedSkillsetSelects: SkillsetSelectBO[] = [
  // 自动化 actions 怼在一起
  // {
  //   kind: 'automation',
  //   key: 'automation-actions',
  // },d
  {
    kind: 'preset',
    key: 'bika-search',
  },
  {
    kind: 'preset',
    key: 'bika-research',
  },
  {
    kind: 'preset',
    key: 'bika-office',
  },
  {
    kind: 'preset',
    key: 'bika-database',
  },
  // {
  //   kind: 'preset',
  //   key: 'bika-document',
  // },
  {
    kind: 'preset',
    key: 'bika-automation',
  },
  // {
  //   kind: 'preset',
  //   key: 'node-resource',
  // },
  {
    kind: 'toolsdk',
    key: 'twitter',
  },
  // 和 bika-automation 的 run_automation skill 重复
  // {
  //   kind: 'custom',
  //   key: 'call-automation',
  //   nodeId: undefined,
  // },
  {
    kind: 'custom',
    key: 'mcp',
    serverConfig: {
      mcpServers: {},
    },
  },
];
