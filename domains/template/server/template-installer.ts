/* eslint-disable max-classes-per-file */
import assert from 'assert';
import _ from 'lodash';
import { generateNanoID } from 'sharelib/nano-id';
import { AutomationSO } from '@bika/domains/automation/server/automation-so';
import { AutomationCreateOperations } from '@bika/domains/automation/server/types';
import { DashboardSO } from '@bika/domains/dashboard/server/dashboard-so';
import type { DashboardModel } from '@bika/domains/dashboard/server/types';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import type { DatabaseModel } from '@bika/domains/database/server/types';
import { FormModel, FormSO } from '@bika/domains/form/server/form-so';
import { MirrorSO } from '@bika/domains/mirror/server/mirror-so';
import { MissionSO } from '@bika/domains/mission/server/mission-so';
import { FolderSO, RootFolderSO } from '@bika/domains/node/server/folder-so';
import { NodeResourceAdapter } from '@bika/domains/node/server/node-resource-adapter';
import { Logger } from '@bika/domains/shared/server';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { SpaceTemplateInstallation } from '@bika/domains/space/server/space-template-installation';
import type { TemplateApplyModel } from '@bika/domains/space/server/types';
import { StoreTemplateSO } from '@bika/domains/store/server/store-template-so';
import { RoleSO } from '@bika/domains/unit/server/role-so';
import { UnitFactory } from '@bika/domains/unit/server/unit-factory';
import { UserSO } from '@bika/domains/user/server/user-so';
import { PrismaPromise, db } from '@bika/server-orm';
import { AiNodeBO, AINodeBOSchema, AiPageNodeBO, AiPageNodeBOSchema } from '@bika/types/ai/bo';
import { Action, Automation, AutomationSchema } from '@bika/types/automation/bo';
import { Dashboard, DashboardSchema } from '@bika/types/dashboard/bo';
import { Database, DatabaseSchema, DatabaseSingleSelectField } from '@bika/types/database/bo';
import {
  CONST_PREFIX_FIELD,
  CONST_PREFIX_FORM,
  CONST_PREFIX_MIRROR,
  CONST_PREFIX_DAT,
  CONST_PREFIX_ACTION,
  CONST_PREFIX_AUT,
  CONST_PREFIX_TRIGGER,
  CONST_PREFIX_VIEW,
  CONST_PREFIX_RECORD,
  CONST_PREFIX_OPTION,
  CONST_PREFIX_AI_NODE,
  CONST_PREFIX_AI_PAGE,
} from '@bika/types/database/vo';
import { FormBO, FormBOSchema } from '@bika/types/form/bo';
import { Mission, MissionSchema } from '@bika/types/mission/bo';
import { Folder, FolderSchema, type NodeResource, MirrorBO, MirrorBOSchema } from '@bika/types/node/bo';
import { CustomTemplate } from '@bika/types/template/bo';
import { Unit, UnitRoleSchema } from '@bika/types/unit/bo';
import { TemplateIdPathStore } from './template-id-path-store';
import { TemplateRepoSO } from './template-repo-so';
import type {
  InitNodeOperation,
  MongoModels,
  NodeOperations,
  PrismaOperations,
  RecordModels,
  TransactionOperations,
  UnitOperations,
} from './types';
import { AINodeSO } from '../../node-resources/ai-agent/ai-node-so';
import { AIPageSO } from '../../node-resources/ai-page/ai-page-so';
import { MemberSO } from '../../unit/server/member-so';

const { isEmpty } = _;

interface UserContext {
  user: UserSO;
  space: SpaceSO;
  parent?: FolderSO;
  // used for private resource
  member?: MemberSO;
}

/**
 * 模板安装器，用于安装和初始化模板
 */
export class TemplateInstaller {
  // 用户上下文
  private readonly _context: UserContext;

  // 所有需要安装的模板实际数据(依赖)，之所以放在这里，因为过程中会对Template进行修改(mutate)，如id赋值
  private _templatesData: CustomTemplate[] = [];

  /**
   * 这里存储，templateId对应的生成出来的instance id，便于后续关联真正的id
   *
   * key format example:
   *  templateId:ROLE:roleTemplateId
   *  templateId:databaseTemplateId、templateId:databaseTemplateId:viewTemplateId
   */
  private _templateIdPathStore = new TemplateIdPathStore();

  /**
   * constructor.
   * @param context user context
   */
  private constructor(context: UserContext) {
    this._context = context;
  }

  /**
   * 初始化模板安装器
   * @param context user context
   * @param template template object
   */
  static async init(context: UserContext, templateRepo: TemplateRepoSO): Promise<TemplateInstaller> {
    // 初始化模板上下文状态机
    const instance = new TemplateInstaller(context);
    await instance.initTemplateState(templateRepo);
    return instance;
  }

  /**
   * 安装和初始化模板，获取数据库操作
   * @param folder parent目录
   * @param preNodeId 上一个节点
   * @param nextNodeId 下一个节点
   */
  private async initTemplate(params: {
    folder: FolderSO;
    preNodeId?: string;
    nextNodeId?: string;
    template: CustomTemplate;
  }): Promise<{ templateNodeId: string; operations: TransactionOperations; callbackFns: (() => Promise<void>)[] }> {
    const prismaOperations: PrismaOperations = [];
    const mongoModels: MongoModels = {};
    const { id: userId } = this._context.user;
    const { folder, preNodeId, nextNodeId, template } = params;
    const { templateId, resources, presetUnits } = template;
    // create template node operation
    const { id: templateNodeId, operation: createTemplateNodeOperation } = await folder.createTemplateNodeOperation(
      userId,
      { preNodeId, nextNodeId, template, unitId: this._context.member?.id },
    );
    // 预置roles, units，之后再node resources
    prismaOperations.push(...(await this.initPresetUnits(presetUnits)));
    // add operations: create template node
    prismaOperations.push(createTemplateNodeOperation);
    // get init template nodes operations
    const convertToInstanceId = await this.buildConvertInstanceIdFunc(templateId);
    const initNodesOperations = await this.initNodes(templateId, templateNodeId, resources, convertToInstanceId);
    // 所有模板安装需要的DB操作全部塞进去
    prismaOperations.push(...initNodesOperations.prisma);
    // console.log(`template id: ${template.templateId},mongo model: ${JSON.stringify(initNodesOperations.models)}`);
    mongoModels.records = [...(mongoModels.records || []), ...(initNodesOperations.models.records || [])];
    // 添加空间站和模板的关系
    prismaOperations.push(this.saveApplyRelation(templateNodeId, template));
    return {
      templateNodeId,
      operations: { prisma: prismaOperations, models: mongoModels },
      callbackFns: initNodesOperations.callbackFns,
    };
  }

  private async hasNotInstallTemplate(rootFolder: RootFolderSO, templateId: string): Promise<boolean> {
    const foundTemplateNode = await rootFolder.findChildNodeByTemplateId(templateId);
    // 找到了，不需要重复安装
    return !foundTemplateNode;
  }

  private async buildConvertInstanceIdFunc(templateId: string) {
    const templateSource = await StoreTemplateSO.getTemplateSource(templateId);

    return (templateIdSuffix: string): string => {
      // 指定跨模板引用的情况。如：foreignDatabaseTemplateId = 'other_template:databaseTemplateId'
      if (templateIdSuffix.includes(':')) {
        const id = this._templateIdPathStore.getMayBeNull(templateIdSuffix);
        if (id) {
          return id;
        }
      }
      // 先查找当前模板下的实例id
      const instanceId = this._templateIdPathStore.getMayBeNull(`${templateId}:${templateIdSuffix}`);
      if (instanceId) {
        return instanceId;
      }
      // 可能是依赖的模板，需要递归查找
      for (const template of this._templatesData) {
        const id = this._templateIdPathStore.getMayBeNull(`${template.templateId}:${templateIdSuffix}`);
        if (id) {
          return id;
        }
      }
      // 兼容非官方模板的角色匹配问题
      if (templateSource !== 'OFFICIAL' && templateIdSuffix.startsWith('ROLE')) {
        return '';
      }
      throw new Error(`Can not find instance id for ${templateIdSuffix}`);
    };
  }

  /**
   * 初始化节点(nodes)，通常会在模板节点下按顺序创建.
   * @param parentId 上级节点
   * @param resources 节点列表
   */
  private async initNodes(
    templateId: string,
    parentId: string,
    resources: NodeResource[],
    convertToInstanceId: (templateIdSuffix: string) => string,
  ): Promise<InitNodeOperation & { callbackFns: (() => Promise<void>)[] }> {
    // 所有的prisma操作，构建链表
    // 然后最终交给prisma在事务方法里顺序执行，保证数据完整性，一旦代码出错，数据回滚，将导致整个模板安装失败
    const nodeOperations: NodeOperations = [];
    const mongoModels: MongoModels = {};
    const callbackFns: (() => Promise<void>)[] = [];
    // 遍历模板下面的节点，按顺序创建
    let preNodeId: string | undefined;
    for (const resource of resources) {
      const { resourceType: nodeType } = resource;
      switch (nodeType) {
        case 'FOLDER':
          {
            // 文件夹类型节点
            const folderResource = FolderSchema.parse(resource);
            const {
              id: folderId,
              operations: createFolderNodeOperation,
              callbackFns: fns,
            } = await this.initFolderNode({
              templateId,
              parentId,
              preNodeId,
              folderResource: folderResource as Folder,
              convertToInstanceId,
            });
            nodeOperations.push(...createFolderNodeOperation.prisma);
            mongoModels.records = [...(mongoModels.records || []), ...(createFolderNodeOperation.models.records || [])];
            callbackFns.push(...fns);
            preNodeId = folderId;
          }
          break;
        case 'DATABASE':
          {
            // 数据库类型节点
            const databaseResource = DatabaseSchema.parse(resource);
            const {
              id: databaseId,
              operation: createDatabaseOperation,
              recordModels,
            } = this.initDatabaseNode({
              parentId,
              preNodeId,
              databaseResource,
              convertToInstanceId,
            });
            nodeOperations.push(createDatabaseOperation);
            mongoModels.records = [...(mongoModels.records || []), ...recordModels];
            preNodeId = databaseId;
            // this.databaseTemplateMap[resource.templateId!] = databaseId;
          }
          break;
        case 'AUTOMATION':
          {
            // 自动化类型节点
            const automationResource = AutomationSchema.parse(resource);
            const {
              automationId,
              operations: createAutomationOperation,
              callbackFns: fns,
            } = await this.initAutomationNode({
              parentId,
              preNodeId,
              automationResource,
              convertToInstanceId,
            });
            nodeOperations.push(...createAutomationOperation);
            preNodeId = automationId;
            if (fns) {
              callbackFns.push(...fns);
            }
          }
          break;
        case 'DASHBOARD':
          {
            // 仪表盘节点
            const dashboardResource = DashboardSchema.parse(resource);
            const { id: dashboardId, operation: createDashboardOperation } = this.initDashboardNode({
              parentId,
              preNodeId,
              dashboardResource,
              convertToInstanceId,
            });
            nodeOperations.push(createDashboardOperation);
            preNodeId = dashboardId;
          }
          break;
        case 'FORM':
          {
            const formResource = FormBOSchema.parse(resource);
            const { id: formId, operation: createFormOperation } = this.initFormNode({
              templateId,
              parentId,
              preNodeId,
              formResource,
            });
            nodeOperations.push(createFormOperation);
            preNodeId = formId;
          }
          break;
        case 'MIRROR':
          {
            const mirrorResource = MirrorBOSchema.parse(resource);
            const { id: mirrorId, operation: createMirrorOperation } = this.initMirrorNode({
              parentId,
              preNodeId,
              mirrorResource,
              convertToInstanceId,
            });
            nodeOperations.push(createMirrorOperation);
            preNodeId = mirrorId;
          }
          break;
        case 'AI': {
          const aiResource = AINodeBOSchema.parse(resource);
          const { id: aiNodeId, operations: createAiNodeOperation } = await this.initAiNode({
            parentId,
            preNodeId,
            aiResource,
            convertToInstanceId,
          });
          nodeOperations.push(...createAiNodeOperation);
          preNodeId = aiNodeId;
          break;
        }
        case 'PAGE': {
          const aiPageResource = AiPageNodeBOSchema.parse(resource);
          const { id: aiPageNodeId, operation: createAiPageOperation } = this.initAiPageNode({
            parentId,
            preNodeId,
            aiPageResource,
            convertToInstanceId,
          });
          nodeOperations.push(createAiPageOperation);
          preNodeId = aiPageNodeId;
          break;
        }
        default:
          Logger.warn(`not implement node type: ${nodeType}`);
          break;
      }
    }
    return { prisma: nodeOperations, models: mongoModels, callbackFns };
  }

  private async initFolderNode(params: {
    templateId: string;
    parentId: string;
    preNodeId?: string;
    folderResource: Folder;
    convertToInstanceId: (templateIdSuffix: string) => string;
  }): Promise<{ id: string; operations: InitNodeOperation; callbackFns: (() => Promise<void>)[] }> {
    const { user, space } = this._context;
    const { templateId, parentId, preNodeId, folderResource: folderTemplate, convertToInstanceId } = params;
    const prismaOperations: NodeOperations = [];
    const mongoModels: MongoModels = {};
    const callbackFns: (() => Promise<void>)[] = [];
    // 构造文件夹类型节点插入数据
    const { id: folderNodeId, operation: createFolderOperation } = FolderSO.createFolderNodeOperationWithTemplate(
      user.id,
      {
        spaceId: space.id,
        parentId,
        preNodeId,
        unitId: this._context.member?.id,
      },
      folderTemplate,
    );
    prismaOperations.push(createFolderOperation);

    if (!isEmpty(folderTemplate.children)) {
      const childrenOperations = await this.initNodes(
        templateId,
        folderNodeId,
        folderTemplate.children! as NodeResource[],
        convertToInstanceId,
      );
      prismaOperations.push(...childrenOperations.prisma);
      mongoModels.records = [...(mongoModels.records || []), ...(childrenOperations.models.records || [])];
      callbackFns.push(...childrenOperations.callbackFns);
    }
    return { id: folderNodeId, operations: { prisma: prismaOperations, models: mongoModels }, callbackFns };
  }

  /**
   * 初始化数据库节点
   * @param params parentId 上级节点 preNodeId 上一个节点 databaseTemplate 数据库模板
   */
  private initDatabaseNode(params: {
    parentId: string;
    preNodeId?: string;
    databaseResource: Database;
    convertToInstanceId: (templateIdSuffix: string) => string;
  }): {
    id: string;
    operation: PrismaPromise<DatabaseModel>;
    recordModels: RecordModels;
  } {
    const { user, space } = this._context;
    const { parentId, preNodeId, databaseResource: databaseTemplate, convertToInstanceId } = params;

    const relation = () => {
      const resourceSO = NodeResourceAdapter.new(databaseTemplate.resourceType);
      resourceSO.relationInstanceId(databaseTemplate, { convertToInstanceId });
    };
    relation();
    // 构造数据库类型节点插入数据
    const {
      id: databaseNodeId,
      operation: createOperation,
      recordModels,
    } = DatabaseSO.createDatabaseOperationWithTemplate(
      user.id,
      {
        spaceId: space.id,
        parentId,
        preNodeId,
        unitId: this._context.member?.id,
      },
      databaseTemplate,
    );
    // 设置当前模板下面的上一个节点
    return { id: databaseNodeId, operation: createOperation, recordModels };
  }

  /**
   * 初始化自动化类型节点
   * @param params parentId 上级节点 preNodeId 上一个节点 automationTemplate 自动化模板
   */
  private async initAutomationNode(params: {
    parentId: string;
    preNodeId?: string;
    automationResource: Automation;
    convertToInstanceId: (templateIdSuffix: string) => string;
  }): Promise<AutomationCreateOperations> {
    const { user, space } = this._context;
    const { parentId, preNodeId, automationResource: automationTemplate, convertToInstanceId } = params;

    const relation = () => {
      const resourceSO = NodeResourceAdapter.new(automationTemplate.resourceType);
      resourceSO.relationInstanceId(automationTemplate, { convertToInstanceId });
    };
    relation();

    return AutomationSO.createAutomationOperationWithTemplate(user, {
      spaceId: space.id,
      automationTemplate,
      parentId,
      preNodeId,
      isTemplateOperation: true,
      unitId: this._context.member?.id,
    });
  }

  /**
   * 初始化仪表盘类型节点
   * @param params parentId 上级节点 preNodeId 上一个节点 dashboard 仪表盘模板
   */
  private initDashboardNode(params: {
    parentId: string;
    preNodeId?: string;
    dashboardResource: Dashboard;
    convertToInstanceId: (templateIdSuffix: string) => string;
  }): {
    id: string;
    operation: PrismaPromise<DashboardModel>;
  } {
    const { user, space } = this._context;
    const { parentId, preNodeId, dashboardResource: dashboardTemplate, convertToInstanceId } = params;

    const relation = () => {
      const resourceSO = NodeResourceAdapter.new(dashboardTemplate.resourceType);
      resourceSO.relationInstanceId(dashboardTemplate, { convertToInstanceId });
    };
    relation();

    const { id: dashboardNodeId, operation: createDashboardOperation } =
      DashboardSO.createDashboardOperationWithTemplate(
        user.id,
        {
          spaceId: space.id,
          parentId,
          preNodeId,
          unitId: this._context.member?.id,
        },
        dashboardTemplate,
      );
    return { id: dashboardNodeId, operation: createDashboardOperation };
  }

  private initFormNode(params: { templateId: string; parentId: string; preNodeId?: string; formResource: FormBO }): {
    id: string;
    operation: PrismaPromise<FormModel>;
  } {
    const { user, space } = this._context;
    const { templateId, parentId, preNodeId, formResource } = params;
    assert(formResource.formType === 'DATABASE' || formResource.formType === undefined);
    if (formResource.databaseTemplateId) {
      const databasePath = `${templateId}:${formResource.databaseTemplateId}`;
      formResource.databaseId = this._templateIdPathStore.get(`${databasePath}`);
      if (formResource.metadata?.type === 'VIEW' && formResource.metadata.viewTemplateId) {
        const path = `${templateId}:${formResource.databaseTemplateId}:${formResource.metadata.viewTemplateId}`;
        formResource.metadata.viewId = this._templateIdPathStore.get(path);
      }
      if (formResource.metadata?.type === 'FIELD') {
        formResource.metadata.fields = formResource.metadata.fields.map((field) => {
          if (!field.fieldTemplateId) {
            return field;
          }
          const path = `${templateId}:${formResource.databaseTemplateId}:${field.fieldTemplateId}`;
          const fieldId = this._templateIdPathStore.get(path);
          return { ...field, fieldId };
        });
      }
    }
    return FormSO.createWithNodeInput(user.id, formResource, {
      spaceId: space.id,
      parentId,
      preNodeId,
      unitId: this._context.member?.id,
    });
  }

  private initMirrorNode(param: {
    parentId: string;
    preNodeId?: string;
    mirrorResource: MirrorBO;
    convertToInstanceId: (templateIdSuffix: string) => string;
  }) {
    const { user, space } = this._context;
    const { parentId, preNodeId, mirrorResource, convertToInstanceId } = param;
    // convert template id to instance id
    const relation = () => {
      const resourceSO = NodeResourceAdapter.new(mirrorResource.resourceType);
      resourceSO.relationInstanceId(mirrorResource, { convertToInstanceId });
    };
    relation();

    const { id: mirrorNodeId, operation: createOperation } = MirrorSO.createWithNodeInput(user.id, mirrorResource, {
      spaceId: space.id,
      parentId,
      preNodeId,
      unitId: this._context.member?.id,
    });
    return { id: mirrorNodeId, operation: createOperation };
  }

  private async initAiNode(params: {
    parentId: string;
    preNodeId?: string;
    aiResource: AiNodeBO;
    convertToInstanceId: (templateIdSuffix: string) => string;
  }) {
    const { user, space } = this._context;
    const { parentId, preNodeId, aiResource, convertToInstanceId } = params;
    // convert template id to instance id
    const relation = () => {
      const resourceSO = NodeResourceAdapter.new(aiResource.resourceType);
      resourceSO.relationInstanceId(aiResource, { convertToInstanceId });
    };
    relation();
    const rootTeam = await space.getRootTeam();

    const { id: aiNodeId, operations: createOperation } = AINodeSO.createWithNodeInput(user, aiResource, {
      spaceId: space.id,
      parentId,
      preNodeId,
      unitId: this._context.member?.id,
      teamId: rootTeam.id,
    });
    return { id: aiNodeId, operations: createOperation };
  }

  private initAiPageNode(params: {
    parentId: string;
    preNodeId?: string;
    aiPageResource: AiPageNodeBO;
    convertToInstanceId: (templateIdSuffix: string) => string;
  }) {
    const { user, space } = this._context;
    const { parentId, preNodeId, aiPageResource, convertToInstanceId } = params;
    // convert template id to instance id
    const relation = () => {
      const resourceSO = NodeResourceAdapter.new(aiPageResource.resourceType);
      resourceSO.relationInstanceId(aiPageResource, { convertToInstanceId });
    };
    relation();

    const { id: aiPageNodeId, operation: createOperation } = AIPageSO.createWithNodeInput(user.id, aiPageResource, {
      spaceId: space.id,
      parentId,
      preNodeId,
      unitId: this._context.member?.id,
    });
    return { id: aiPageNodeId, operation: createOperation };
  }

  /**
   * 保存模板和空间站的关系
   * @private
   */
  private saveApplyRelation(templateNodeId: string, template: CustomTemplate): PrismaPromise<TemplateApplyModel> {
    const { user, space } = this._context;
    return SpaceTemplateInstallation.create(user.id, space.id, templateNodeId, template);
  }

  /**
   * 预配置roles、teams等，事先根据模板特性创建好
   */
  private async initPresetUnits(unitBOs?: Unit[]): Promise<UnitOperations> {
    if (!unitBOs || isEmpty(unitBOs)) {
      return [];
    }
    const { user, space } = this._context;

    const createOperations: UnitOperations = [];

    for (const unitBO of unitBOs) {
      if (unitBO.type === 'ROLE') {
        const roleBO = UnitRoleSchema.parse(unitBO);
        if (!roleBO.templateId) {
          // 没有唯一标识,直接新增
          createOperations.push(RoleSO.createRoleOperationWithBO(user.id, space.id, roleBO));
        } else {
          const existRole = await RoleSO.findByTemplateId(space.id, roleBO.templateId);
          // 同样模板ID的只能有一个
          if (!existRole) {
            createOperations.push(RoleSO.createRoleOperationWithBO(user.id, space.id, roleBO));
          }
        }
      } else {
        throw new Error(`not implement unit type: ${unitBO.type}`);
      }
    }
    return createOperations;
  }

  private async asyncInitMissions(templateNodeId: string, missions: Mission[]) {
    const { user, space } = this._context;
    for (const mission of missions) {
      await MissionSO.createMission({
        user,
        spaceId: space.id,
        templateNodeId,
        missionTemplate: MissionSchema.parse(mission),
      });
    }
  }

  private async initTemplateState(templateRepo: TemplateRepoSO, version: string = '*') {
    const template = templateRepo.getReleaseTemplate(version);
    if (template.dependencies) {
      for (const depTemplateId in template.dependencies) {
        if (depTemplateId in template.dependencies) {
          const depVersion = template.dependencies[depTemplateId];
          const dependTemplateRepo = await TemplateRepoSO.init(depTemplateId);
          await this.initTemplateState(dependTemplateRepo, depVersion);
        }
      }
    }

    if (template.presetUnits) {
      for (const presetUnit of template.presetUnits) {
        if (presetUnit.type === 'ROLE') {
          if (presetUnit.templateId) {
            const existRole = await RoleSO.findByTemplateId(this._context.space.id, presetUnit.templateId);
            const unitId = existRole?.id || UnitFactory.generateUnitId(presetUnit.type);
            presetUnit.id = unitId;
            this._templateIdPathStore.set(`${template.templateId}:${presetUnit.type}:${presetUnit.templateId}`, unitId);
          }
        } else {
          throw new Error(`not implement unit type: ${presetUnit.type}`);
        }
      }
    }

    const initAutomationNodeState = (automation: Automation) => {
      if (automation.triggers) {
        for (const trigger of automation.triggers) {
          trigger.id = generateNanoID(CONST_PREFIX_TRIGGER);
          this._templateIdPathStore.set(
            `${template.templateId}:${automation.templateId}:${trigger.templateId}`,
            trigger.id,
          );
          if (trigger.templateId) {
            if (trigger.templateId.startsWith(CONST_PREFIX_TRIGGER)) {
              this._templateIdPathStore.set(trigger.templateId, trigger.id);
            }
          }
        }
      }
      if (automation.actions) {
        const setActionIdStore = (data: Action) => {
          const actionId = generateNanoID(CONST_PREFIX_ACTION);
          this._templateIdPathStore.set(`${template.templateId}:${automation.templateId}:${data.templateId}`, actionId);
          if (data.templateId) {
            if (data.templateId.startsWith(CONST_PREFIX_ACTION)) {
              this._templateIdPathStore.set(data.templateId, actionId);
            }
          }
          // eslint-disable-next-line no-param-reassign
          data.id = actionId;
        };
        for (const action of automation.actions) {
          setActionIdStore(action);
          // nested actions
          if ('actions' in action && action.actions) {
            for (const subAction of action.actions) {
              setActionIdStore(subAction);
            }
          }
        }
      }
    };

    const initDatabaseNodeState = (database: Database) => {
      if (database.views) {
        for (const view of database.views) {
          view.id = generateNanoID(CONST_PREFIX_VIEW);
          this._templateIdPathStore.set(`${template.templateId}:${database.templateId}:${view.templateId}`, view.id);
          if (view.templateId && view.templateId.startsWith(CONST_PREFIX_VIEW)) {
            this._templateIdPathStore.set(view.templateId, view.id);
          }
        }
      }
      if (database.fields) {
        for (const field of database.fields) {
          field.id = generateNanoID(CONST_PREFIX_FIELD);
          this._templateIdPathStore.set(`${template.templateId}:${database.templateId}:${field.templateId}`, field.id);
          if (field.templateId && field.templateId.startsWith(CONST_PREFIX_FIELD)) {
            this._templateIdPathStore.set(field.templateId, field.id);
          }

          // Generate option id
          if (field.type === 'SINGLE_SELECT' || field.type === 'MULTI_SELECT') {
            const selectField = field as DatabaseSingleSelectField;
            for (const option of selectField.property.options) {
              if (option.templateId && !option.id) {
                option.id = generateNanoID(CONST_PREFIX_OPTION);
                this._templateIdPathStore.set(
                  `${template.templateId}:${database.templateId}:${field.templateId}:${option.templateId}`,
                  option.id,
                );
              }
            }
          }
        }
      }
      if (database.records) {
        for (const record of database.records) {
          record.id = generateNanoID(CONST_PREFIX_RECORD);
          if (record.templateId) {
            this._templateIdPathStore.set(
              `${template.templateId}:${database.templateId}:${record.templateId}`,
              record.id,
            );
            if (record.templateId.startsWith(CONST_PREFIX_RECORD)) {
              this._templateIdPathStore.set(record.templateId, record.id);
            }
          }
        }
      }
    };

    const loopFolder = (folder: Folder) => {
      const children = folder.children;
      if (!children) {
        return;
      }
      for (const child of children) {
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        initNodeState(child as NodeResource);
      }
    };

    const initNodeState = (resource: NodeResource) => {
      switch (resource.resourceType) {
        case 'AUTOMATION':
          {
            const automation = resource as Automation;
            if (automation.templateId) {
              const automationTemplateId = automation.templateId;
              automation.id = generateNanoID(CONST_PREFIX_AUT);
              this._templateIdPathStore.set(`${template.templateId}:${automationTemplateId}`, automation.id);
              initAutomationNodeState(automation);
            }
          }
          break;
        case 'DATABASE':
          {
            const database = resource as Database;
            if (database.templateId) {
              const databaseTemplateId = database.templateId;
              database.id = generateNanoID(CONST_PREFIX_DAT);
              this._templateIdPathStore.set(`${template.templateId}:${databaseTemplateId}`, database.id);
              if (database.templateId && database.templateId.startsWith(CONST_PREFIX_DAT)) {
                this._templateIdPathStore.set(database.templateId, database.id);
              }
              initDatabaseNodeState(database);
            }
          }
          break;
        case 'FOLDER':
          loopFolder(resource as Folder);
          break;
        case 'FORM':
          {
            const form = resource as FormBO;
            if (form.templateId) {
              const formTemplateId = form.templateId;
              form.id = generateNanoID(CONST_PREFIX_FORM);
              this._templateIdPathStore.set(`${template.templateId}:${formTemplateId}`, form.id);
            }
          }
          break;
        case 'MIRROR':
          {
            const mirror = resource as MirrorBO;
            if (mirror.templateId) {
              const mirrorTemplateId = mirror.templateId;
              mirror.id = generateNanoID(CONST_PREFIX_MIRROR);
              this._templateIdPathStore.set(`${template.templateId}:${mirrorTemplateId}`, mirror.id);
            }
          }
          break;

        case 'AI': {
          const ai = resource as AiNodeBO;
          if (ai.templateId) {
            const aiTemplateId = ai.templateId;
            ai.id = generateNanoID(CONST_PREFIX_AI_NODE);
            this._templateIdPathStore.set(`${template.templateId}:${aiTemplateId}`, ai.id);
          }
          break;
        }
        case 'PAGE': {
          const page = resource as AiPageNodeBO;
          if (page.templateId) {
            const pageTemplateId = page.templateId;
            page.id = generateNanoID(CONST_PREFIX_AI_PAGE);
            this._templateIdPathStore.set(`${template.templateId}:${pageTemplateId}`, page.id);
          }
          break;
        }
        default:
          break;
      }
    };

    for (const resource of template.resources) {
      if (resource.templateId) {
        initNodeState(resource);
      }
    }

    this._templatesData.push(template);
  }

  /**
   * 执行模板安装
   */
  async execute(): Promise<{ templateNodeId: string }> {
    // get space so
    const spaceSO = this._context.space;
    // get root node so
    const rootFolder = await spaceSO.getRootFolder();
    const folder = this._context.parent || rootFolder;
    // 找到根目录下的第一个节点
    const nextNode = await folder.firstChildId(this._context.member?.id);
    let preNodeId: string | undefined;
    const installedTemplate: string[] = [];
    const templateNodeIdMap: Record<string, string> = {};
    // transaction operations
    const prismaOperations: PrismaOperations = [];
    // mongo models
    const mongoModels: MongoModels = {};
    // callback functions
    const callbackFns: (() => Promise<void>)[] = [];
    for (let index = 0; index < this._templatesData.length; index += 1) {
      const template = this._templatesData[index];
      if (!installedTemplate.includes(template.templateId)) {
        const isNotInstall = await this.hasNotInstallTemplate(rootFolder, template.templateId);
        if (isNotInstall || !template.installOnce) {
          const {
            templateNodeId,
            operations: transactionOperation,
            callbackFns: fns,
          } = await this.initTemplate({
            folder,
            preNodeId,
            nextNodeId: index === this._templatesData.length - 1 ? nextNode : undefined,
            template,
          });
          templateNodeIdMap[template.templateId] = templateNodeId;
          preNodeId = templateNodeId;
          prismaOperations.push(...transactionOperation.prisma);
          mongoModels.records = [...(mongoModels.records || []), ...(transactionOperation.models.records || [])];
          installedTemplate.push(template.templateId);
          callbackFns.push(...fns);
        }
      }
    }

    if (!preNodeId) {
      throw new Error('template has not installed successfully');
    }

    // console.log(`mongo model: ${JSON.stringify(mongoModels)}`);

    if (mongoModels.records && mongoModels.records.length > 0) {
      const recordDAO = db.mongo.databaseRecord(spaceSO.id);
      await recordDAO.init();
      await db.mongo.transaction(async (session) => {
        await recordDAO.insertMany(mongoModels.records, { session });

        await db.prisma.$transaction(prismaOperations);
      });
      // await db.mongo.databaseRecord(spaceId).insertMany(mongoModels.records);
    } else {
      await db.prisma.$transaction(prismaOperations);
    }

    for (const template of this._templatesData) {
      if (!isEmpty(template.initMissions)) {
        // 异步初始化Mission
        if (template.templateId in templateNodeIdMap) {
          const templateNodeId = templateNodeIdMap[template.templateId];
          await this.asyncInitMissions(templateNodeId, template.initMissions!);
        }
      }
    }

    // 异步执行回调函数
    if (callbackFns) {
      Promise.allSettled(callbackFns.map((fn) => fn()));
    }

    return { templateNodeId: preNodeId };
  }
}
