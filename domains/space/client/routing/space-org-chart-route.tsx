'use client';

import dynamic from 'next/dynamic';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useApiCaller } from '@bika/api-caller/context';
import type { SkillsetVO } from '@bika/types/skill/vo';
import { useSpace<PERSON>ontext<PERSON><PERSON><PERSON>, useSpaceRouter } from '@bika/types/space/context';
import type { EditorScreenProps } from '@bika/types/template/type';
import { useGlobalContext } from '@bika/types/website/context';
import { Empty } from '@bika/ui/components/empty/index';
import type { OrgChartUnit } from '@bika/ui/editor/flow-editor/nodes/unit';
import { Box } from '@bika/ui/layouts';
import { LoadingMask } from '@bika/ui/loading-mask';
import { Skeleton } from '@bika/ui/skeleton';

const FlowEditor = dynamic(
  () => import('@bika/ui/editor/flow-editor/flow-editor').then((module) => module.FlowEditor),
  {
    loading: () => <Skeleton pos="NODE_PAGE" />,
    ssr: false,
  },
);

const PAGE_SIZE = 100;
const PAGE_NO = 1;

const setAllDisabledRecursive = (unit: OrgChartUnit, disabled: boolean): OrgChartUnit => {
  let item: OrgChartUnit;
  if (unit.type === 'Team' && unit.children && unit.children.length > 0) {
    item = { ...unit, disabled, children: unit.children.map((child) => setAllDisabledRecursive(child, disabled)) };
  } else {
    item = { ...unit, disabled };
  }
  return item;
};

const setAllNodesDisabledRecursive = (units: OrgChartUnit[], disabled: boolean): OrgChartUnit[] =>
  units.map((unit) => {
    let item: OrgChartUnit;
    if (unit.type === 'Team' && unit.children && unit.children.length > 0) {
      item = {
        ...unit,
        disabled,
        children: setAllNodesDisabledRecursive(unit.children, disabled),
      };
    } else {
      item = { ...unit, disabled };
    }
    return item;
  });

export function SpaceOrgChartRoute({ spaceId }: { spaceId: string }) {
  const { useParams, push } = useSpaceRouter();
  const { teamId } = useParams<{ teamId: string }>();
  const ctx = useGlobalContext();
  const spaceContext = useSpaceContextForce();
  const { trpcQuery, trpc } = useApiCaller();
  const [unitList, setUnitList] = useState<OrgChartUnit[]>([]);
  const [currentTeam, setCurrentTeam] = useState<OrgChartUnit>();
  const [viewMode, setViewMode] = useState<'all' | 'member' | 'ai'>('all');

  const isRootTeam = teamId === spaceContext.rootTeam.id;

  const { data, isLoading } = trpcQuery.team.subList.useQuery({
    spaceId,
    teamId,
    pageSize: PAGE_SIZE,
    pageNo: PAGE_NO,
  });
  const { data: teamInfo, isLoading: teamInfoLoading } = trpcQuery.team.info.useQuery(
    {
      spaceId,
      id: teamId,
    },
    { enabled: !isRootTeam },
  );

  const agentNodes = data?.data
    .filter((item) => item.type === 'Member' && item.relationType === 'AI')
    .map((item) => item.id);
  const { data: talks } = trpcQuery.talk.searchTalks.useQuery(
    {
      mode: 'org-chart',
      unitIds: agentNodes || [],
    },
    { enabled: !!agentNodes && agentNodes.length > 0 },
  );

  const skillsetsMap = useMemo(() => {
    const map = new Map<string, SkillsetVO[]>();
    if (talks && talks.length > 0) {
      talks.forEach((talk) => {
        if (talk.type === 'unit' && talk.skillsets) {
          map.set(talk.unitId, talk.skillsets);
        }
      });
    }
    return map;
  }, [talks]);

  const toggleUnitExpansion = useCallback(
    (units: OrgChartUnit[], targetId: string): OrgChartUnit[] =>
      units.map((unit) => {
        if (unit.id === targetId) {
          const newIsExpanded = !unit.isExpanded;
          const newUnit: OrgChartUnit = {
            ...unit,
            isExpanded: newIsExpanded,
          };
          if (unit.children) {
            newUnit.children = unit.children.map((child) => setAllDisabledRecursive(child, !newIsExpanded));
          }
          return newUnit;
        }

        if (unit.children && unit.children.length > 0) {
          return {
            ...unit,
            children: toggleUnitExpansion(unit.children, targetId),
          };
        }
        return unit;
      }),
    [],
  );

  const handleOnClick = useCallback(
    (unit: EditorScreenProps | OrgChartUnit) => {
      if ('type' in unit && unit.type === 'Member') {
        push(`/space/${spaceId}/member/${unit.id}`);
      }
      if ('type' in unit && unit.type === 'Team') {
        push(`/space/${spaceId}/team/${unit.id}`);
      }
    },
    [spaceId],
  );

  function toggleAll(_curTeam: OrgChartUnit) {
    setCurrentTeam((prev) => {
      if (!prev) return prev;
      return {
        ...prev,
        isExpanded: !prev.isExpanded,
      };
    });
    setUnitList((prev) => {
      const hasEnabledNodes = prev.some((unit) => !unit.disabled);
      return setAllNodesDisabledRecursive(prev, hasEnabledNodes);
    });
  }

  useEffect(() => {
    const loadUnitsWithSubteams = async () => {
      if (data?.data) {
        const units = await Promise.all(
          data.data.map(async (item) => {
            if (item.type === 'Team') {
              const { data: subTeam } = await trpc.team.subList.query({
                spaceId,
                teamId: item.id,
                pageSize: PAGE_SIZE,
                pageNo: PAGE_NO,
              });
              return {
                ...item,
                isExpanded: false,
                children: subTeam.map((team) => ({
                  ...team,
                  isExpanded: false,
                  disabled: true,
                  parentId: item.id,
                  // Add skillsets for AI agent members
                  ...(team.type === 'Member' &&
                    team.relationType === 'AI' &&
                    skillsetsMap.has(team.id) && {
                      skillsets: skillsetsMap.get(team.id),
                    }),
                })),
              };
            }
            return {
              ...item,
              isExpanded: false,
              parentId: teamId,
              // Add skillsets for AI agent members
              ...(item.type === 'Member' &&
                item.relationType === 'AI' &&
                skillsetsMap.has(item.id) && {
                  skillsets: skillsetsMap.get(item.id),
                }),
            };
          }),
        );
        setUnitList(units);
      }

      if (isRootTeam) {
        setCurrentTeam({
          id: spaceContext.rootTeam.id,
          name: spaceContext.rootTeam.name,
          isGuest: spaceContext.rootTeam.isGuest,
          avatar: spaceContext.data.logo,
          type: 'Team',
          isExpanded: true,
          children: data?.data || [],
          getMoreUnits: toggleAll,
        });
        return;
      }
      if (teamInfo) {
        setCurrentTeam({
          ...teamInfo,
          isExpanded: !!teamInfo.children?.length,
          children: data?.data,
          getMoreUnits: toggleAll,
        });
      }
    };

    loadUnitsWithSubteams();
  }, [teamInfo, data, teamId, spaceId, skillsetsMap, isRootTeam]);

  useEffect(() => {
    const updateUnitsBasedOnViewMode = (units: OrgChartUnit[]): OrgChartUnit[] =>
      units.map((unit) => {
        const isAI = unit.type === 'Member' && unit.relationType === 'AI';
        const isTeam = unit.type === 'Team';

        const disabled = (viewMode === 'ai' && !isAI && !isTeam) || (viewMode === 'member' && isAI && !isTeam) || false;
        if (unit.type === 'Team') {
          return {
            ...unit,
            disabled,
            children: unit.children ? updateUnitsBasedOnViewMode(unit.children) : undefined,
          };
        }
        return {
          ...unit,
          disabled,
        };
      });

    setUnitList((prevUnits) => updateUnitsBasedOnViewMode(prevUnits));
  }, [viewMode]);

  const findAndSetTarget = useCallback(
    (list: OrgChartUnit[], targetName: string, subList: OrgChartUnit[]): OrgChartUnit[] =>
      list.map((unit) => {
        if (unit.type === 'Team') {
          if (unit.name === targetName) {
            return {
              ...unit,
              children: subList,
            };
          }

          if (unit?.children && unit.children.length > 0) {
            const children = findAndSetTarget(unit.children, targetName, subList);
            return {
              ...unit,
              children,
            };
          }
        }
        return unit;
      }),
    [],
  );

  // 限制并发请求数量，防止URL过长
  async function concurrentMap(
    units: OrgChartUnit[],
    getSubTeams: (item: OrgChartUnit) => Promise<OrgChartUnit>,
    limit = 10,
  ): Promise<OrgChartUnit[]> {
    const result: OrgChartUnit[] = [];
    let idx = 0;

    const processBatch = async () => {
      const batch = units.slice(idx, idx + limit);
      const promises = batch.map((unit, i) =>
        getSubTeams(unit).then((subTeams) => {
          result[idx + i] = subTeams;
        }),
      );

      await Promise.all(promises);
      idx += limit;

      if (idx < units.length) {
        await processBatch();
      }
    };

    await processBatch();
    return result;
  }

  const getMoreUnits = useCallback(
    async (unit: OrgChartUnit) => {
      if (unit.type !== 'Team') return;
      if (unit.children) {
        const hasRequested = unit.children.some((child) => child.type === 'Team' && child.children);
        if (!hasRequested) {
          const units = await concurrentMap(
            unit.children,
            async (item) => {
              if (item.type === 'Team') {
                const { data: subTeam } = await trpc.team.subList.query({
                  spaceId,
                  teamId: item.id,
                  pageSize: PAGE_SIZE,
                  pageNo: PAGE_NO,
                });
                return {
                  ...item,
                  isExpanded: false,
                  children: subTeam.map((team) => ({
                    ...team,
                    isExpanded: false,
                    disabled: true,
                    parentId: item.id,
                    // Add skillsets for AI agent members
                    ...(team.type === 'Member' &&
                      team.relationType === 'AI' &&
                      skillsetsMap.has(team.id) && {
                        skillsets: skillsetsMap.get(team.id),
                      }),
                  })),
                };
              }
              return {
                ...item,
                isExpanded: false,
                parentId: teamId,
              };
            },
            10,
          );

          setUnitList((currentUnitList) => {
            const newUnits = findAndSetTarget(currentUnitList, unit.name, units);
            const updatedUnits = toggleUnitExpansion(newUnits, unit.id);
            return updatedUnits;
          });
        } else {
          setUnitList((currentUnitList) => toggleUnitExpansion(currentUnitList, unit.id));
        }
      }
    },
    [spaceId, teamId, skillsetsMap],
  );

  if (isLoading || (!isRootTeam && teamInfoLoading))
    return (
      <div className="w-full h-full relative">
        <LoadingMask />
      </div>
    );

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
      }}
    >
      {!teamInfo && !data ? (
        <Empty text="team not found" />
      ) : (
        <FlowEditor
          readonly
          showControl
          showTeamControl
          openNewWindowButtonAndUrl={undefined}
          fitView={true}
          theme={ctx.theme.themeMode?.toLowerCase()}
          data={{
            type: 'units',
            units: unitList,
            currentTeam,
          }}
          viewMode={viewMode}
          setViewMode={setViewMode}
          onClick={handleOnClick}
          getMoreUnits={getMoreUnits}
          backgroundStyle={{
            backgroundImage: 'url(/assets/home/<USER>',
            backgroundSize: 'cover',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
            opacity: 0.2,
          }}
        />
      )}
    </Box>
  );
}
