import { describe, test, expect } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { DatabaseSO } from '@bika/domains/database/server/database-so';

/**
 * 数据表附件引用测试
 */
describe('database attachment test', () => {
  /**
   * 创建记录, 并且添加附件列, 给附件列添加附件, 检查附件使用量
   */
  test('create record with attachment field test', async () => {
    // 初始化
    const { user, member, space, rootFolder } = await MockContext.initUserContext();
    // 现在的空间附件使用用量
    const entitlement = await space.getEntitlement();
    const usage = entitlement.getUsage();
    const beforeStorageUsedValue = await usage.getUsedUsageValue('STORAGES');
    expect(beforeStorageUsedValue).toBe(0);
    // 初始化一张表格
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
    });
    expect(databaseNode).not.toBeNull();
    // 上传附件
    const filePath = `${__dirname}/files/voice.m4a`;
    const attachment = await MockContext.createMockAttachment(user, filePath);
    const attach = attachment.toVO();
    const attachmentBO = [
      {
        id: attach.id,
        mimeType: attach.mimeType,
        bucket: attach.bucket,
        name: 'voice',
        size: attach.size,
        path: attach.path,
      },
    ];
    // 添加两个附件列, 并添加该附件内容
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    const photoField = await database.createField(user, {
      type: 'ATTACHMENT',
      name: 'photo',
    });
    const imageField = await database.createField(user, {
      type: 'ATTACHMENT',
      name: 'image',
    });

    // 给附件列上传文件
    await database.createRecord(user, member, {
      [photoField.id]: attachmentBO,
      [imageField.id]: attachmentBO,
    });

    // 检查现在的空间站的附件使用
    const afterUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterUsedUsageValue).toBe(attachment.size * 2);
  });

  /**
   * 删除有附件列的记录, 检查附件使用量
   */
  test('delete record with attachment field test', async () => {
    // 初始化
    const { user, member, space, rootFolder } = await MockContext.initUserContext();
    // 现在的空间附件使用用量
    const entitlement = await space.getEntitlement();
    const usage = entitlement.getUsage();
    const beforeStorageUsedValue = await usage.getUsedUsageValue('STORAGES');
    expect(beforeStorageUsedValue).toBe(0);
    // 初始化一张表格
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
    });
    expect(databaseNode).not.toBeNull();
    // 上传附件
    const filePath = `${__dirname}/files/voice.m4a`;
    const attachment = await MockContext.createMockAttachment(user, filePath);
    // 添加两个附件列, 并添加该附件内容
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    const photoField = await database.createField(user, {
      type: 'ATTACHMENT',
      name: 'photo',
    });
    const imageField = await database.createField(user, {
      type: 'ATTACHMENT',
      name: 'image',
    });
    const attach = attachment.toVO();
    const attachmentBO = [
      {
        id: attach.id,
        mimeType: attach.mimeType,
        bucket: attach.bucket,
        name: 'test',
        size: attach.size,
        path: attach.path,
      },
    ];
    // 给附件列上传文件
    const records = await database.createRecords(user, member, [
      {
        [photoField.id]: attachmentBO,
      },
      {
        [imageField.id]: attachmentBO,
      },
    ]);
    expect(records.length).toBe(2);
    // 检查现在的空间站的附件使用
    const afterUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterUsedUsageValue).toBe(attachment.size * 2);

    // 删除其中一个
    await database.deleteRecords(user, [records[0].id]);

    // 检查现在的空间站的附件使用
    const afterDeleteUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterDeleteUsedUsageValue).toBe(attachment.size);
  });

  /**
   * 更新有附件列的记录, 给附件列添加附件, 检查附件使用量
   */
  test('update record with attachment field test', async () => {
    // 初始化
    const { user, member, space, rootFolder } = await MockContext.initUserContext();
    // 现在的空间附件使用用量
    const entitlement = await space.getEntitlement();
    const usage = entitlement.getUsage();
    const beforeStorageUsedValue = await usage.getUsedUsageValue('STORAGES');
    expect(beforeStorageUsedValue).toBe(0);
    // 初始化一张表格
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
    });
    expect(databaseNode).not.toBeNull();
    // 上传附件
    const filePath = `${__dirname}/files/voice.m4a`;
    const attachment = await MockContext.createMockAttachment(user, filePath);
    // 添加两个附件列, 并添加该附件内容
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    const photoField = await database.createField(user, {
      type: 'ATTACHMENT',
      name: 'photo',
    });
    const imageField = await database.createField(user, {
      type: 'ATTACHMENT',
      name: 'image',
    });
    const attach = attachment.toVO();
    const attachmentBO = [
      {
        id: attach.id,
        mimeType: attach.mimeType,
        bucket: attach.bucket,
        name: 'test',
        size: attach.size,
        path: attach.path,
      },
    ];
    // 给附件列上传文件
    const record = await database.createRecord(user, member, {
      [photoField.id]: attachmentBO,
      [imageField.id]: attachmentBO,
    });
    // 检查现在的空间站的附件使用
    const afterUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterUsedUsageValue).toBe(attachment.size * 2);

    // 上传新的文件
    const newFilePath = `${__dirname}/files/test.m4a`;
    const newAttachment = await MockContext.createMockAttachment(user, newFilePath);
    const newAttach = newAttachment.toVO();
    const newAttachmentBO = {
      id: newAttach.id,
      mimeType: newAttach.mimeType,
      bucket: newAttach.bucket,
      name: 'test',
      size: newAttach.size,
      path: newAttach.path,
    };
    // 更新附件列, 添加新的附件
    await database.updateRecords(user, [
      {
        recordId: record.id,
        cells: {
          [photoField.id]: [...attachmentBO, newAttachmentBO],
        },
      },
    ]);

    // 检查现在的空间站的附件使用
    const afterUpdateUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterUpdateUsedUsageValue).toBe(attachment.size * 2 + newAttachment.size);
  });

  /**
   * 更改附件列为非附件列
   */
  test('update attchment field to un-attchment field test', async () => {
    // 初始化
    const { user, member, space, rootFolder } = await MockContext.initUserContext();
    // 现在的空间附件使用用量
    const entitlement = await space.getEntitlement();
    const usage = entitlement.getUsage();
    const beforeStorageUsedValue = await usage.getUsedUsageValue('STORAGES');
    expect(beforeStorageUsedValue).toBe(0);
    // 初始化一张表格
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
    });
    expect(databaseNode).not.toBeNull();
    // 上传附件
    const filePath = `${__dirname}/files/voice.m4a`;
    const attachment = await MockContext.createMockAttachment(user, filePath);
    // 添加两个附件列, 并添加该附件内容
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    const photoField = await database.createField(user, {
      type: 'ATTACHMENT',
      name: 'photo',
    });
    const imageField = await database.createField(user, {
      type: 'ATTACHMENT',
      name: 'image',
    });
    const attach = attachment.toVO();
    const attachmentBO = [
      {
        id: attach.id,
        mimeType: attach.mimeType,
        bucket: attach.bucket,
        name: 'test',
        size: attach.size,
        path: attach.path,
      },
    ];
    // 给附件列上传文件
    await database.createRecord(user, member, {
      [photoField.id]: attachmentBO,
      [imageField.id]: attachmentBO,
    });
    // 检查现在的空间站的附件使用
    const afterUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterUsedUsageValue).toBe(attachment.size * 2);

    // 更新附件列为非附件列
    await photoField.update(user, {
      type: 'SINGLE_TEXT',
      name: 'text',
    });

    // 这一列的数据应该没了, 检查现在的空间站的附件使用
    const afterUpdateUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterUpdateUsedUsageValue).toBe(attachment.size);
  });

  /**
   * 删除附件列
   */
  test('delete attachment field test', async () => {
    // 初始化
    const { user, member, space, rootFolder } = await MockContext.initUserContext();
    // 现在的空间附件使用用量
    const entitlement = await space.getEntitlement();
    const usage = entitlement.getUsage();
    const beforeStorageUsedValue = await usage.getUsedUsageValue('STORAGES');
    expect(beforeStorageUsedValue).toBe(0);
    // 初始化一张表格
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
    });
    expect(databaseNode).not.toBeNull();
    // 上传附件
    const filePath = `${__dirname}/files/voice.m4a`;
    const attachment = await MockContext.createMockAttachment(user, filePath);
    // 添加两个附件列, 并添加该附件内容
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    const photoField = await database.createField(user, {
      type: 'ATTACHMENT',
      name: 'photo',
    });
    const imageField = await database.createField(user, {
      type: 'ATTACHMENT',
      name: 'image',
    });
    const attach = attachment.toVO();
    const attachmentBO = [
      {
        id: attach.id,
        mimeType: attach.mimeType,
        bucket: attach.bucket,
        name: 'test',
        size: attach.size,
        path: attach.path,
      },
    ];
    // 给附件列上传文件
    await database.createRecord(user, member, {
      [photoField.id]: attachmentBO,
      [imageField.id]: attachmentBO,
    });
    // 检查现在的空间站的附件使用
    const afterUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterUsedUsageValue).toBe(attachment.size * 2);

    // 删除附件列
    await photoField.delete(user);

    // 检查现在的空间站的附件使用
    const afterDeleteUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterDeleteUsedUsageValue).toBe(attachment.size);
  });

  /**
   * 删除包含附件列的表格(同时删除icon和附件列引用)
   */
  test('delete database with attachment field test', async () => {
    // 初始化
    const { user, member, space, rootFolder } = await MockContext.initUserContext();
    // 现在的空间附件使用用量
    const entitlement = await space.getEntitlement();
    const usage = entitlement.getUsage();
    const beforeStorageUsedValue = await usage.getUsedUsageValue('STORAGES');
    expect(beforeStorageUsedValue).toBe(0);
    // 初始化一张表格
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
    });
    expect(databaseNode).not.toBeNull();
    // 上传一个icon
    const iconPath = `${__dirname}/files/node-icon.png`;
    const icon = await MockContext.createMockAttachment(user, iconPath);
    // 设置icon
    await databaseNode.update(user, {
      resourceType: 'DATABASE',
      icon: {
        type: 'ATTACHMENT',
        attachmentId: icon.id,
        relativePath: icon.path,
      },
    });
    // 检查现在的空间站的附件使用
    const afterUpdatedUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterUpdatedUsedUsageValue).toBe(icon.size);
    // 上传附件
    const filePath = `${__dirname}/files/voice.m4a`;
    const attachment = await MockContext.createMockAttachment(user, filePath);
    // 添加两个附件列, 并添加该附件内容
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    const photoField = await database.createField(user, {
      type: 'ATTACHMENT',
      name: 'photo',
    });
    const imageField = await database.createField(user, {
      type: 'ATTACHMENT',
      name: 'image',
    });
    const attach = attachment.toVO();
    const attachmentBO = [
      {
        id: attach.id,
        mimeType: attach.mimeType,
        bucket: attach.bucket,
        name: 'test',
        size: attach.size,
        path: attach.path,
      },
    ];
    // 给附件列上传文件
    await database.createRecord(user, member, {
      [photoField.id]: attachmentBO,
      [imageField.id]: attachmentBO,
    });
    // 检查现在的空间站的附件使用
    const afterUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterUsedUsageValue).toBe(icon.size + attachment.size * 2);

    // 删除表格
    await database.toNodeSO().delete(user);

    // 检查现在的空间站的附件使用
    const afterDeleteUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterDeleteUsedUsageValue).toBe(0);
  });
});

/**
 * 表单封面图引用测试
 */
describe('form brand logo ref test', () => {
  /**
   * 非图片LOGO更改为图片LOGO
   */
  test('un-attachment brand logo change to attachment brand logo test', async () => {
    // 初始化
    const { user, space, rootFolder } = await MockContext.initUserContext();
    // 现在的空间附件使用用量
    const entitlement = await space.getEntitlement();
    const usage = entitlement.getUsage();
    // 初始化一张表格
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
    });
    expect(databaseNode).not.toBeNull();
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    // 初始化一张表单
    const formNode = await rootFolder.createChildSimple(user, {
      resourceType: 'FORM',
      name: 'new form',
      formType: 'DATABASE',
      databaseId: database.id,
      viewId: (await database.firstView()).id,
    });
    // 此时没有设置LOGO, 检查容量
    const beforeStorageUsedValue = await usage.getUsedUsageValue('STORAGES');
    expect(beforeStorageUsedValue).toBe(0);

    // 上传一个PNG
    const logoPath = `${__dirname}/files/form-logo.png`;
    const logo = await MockContext.createMockAttachment(user, logoPath);
    // 更改封面图
    await formNode.update(user, {
      resourceType: 'FORM',
      brandLogo: {
        type: 'ATTACHMENT',
        attachmentId: logo.id,
        relativePath: logo.path,
      },
    });
    // 检查现在的空间站的附件使用
    const afterUpdatedUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterUpdatedUsedUsageValue).toBe(logo.size);
    // 删除表单, 检查容量
    await formNode.delete(user);
    const afterDeleteUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterDeleteUsedUsageValue).toBe(0);
  });
  /**
   * 图片LOGO更改为非图片LOGO
   */
  test('attachment brand logo change to un-attachment brand logo test', async () => {
    // 初始化
    const { user, space, rootFolder } = await MockContext.initUserContext();
    // 现在的空间附件使用用量
    const entitlement = await space.getEntitlement();
    const usage = entitlement.getUsage();
    // 初始化一张表格
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
    });
    expect(databaseNode).not.toBeNull();
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    // 初始化一张表单
    const formNode = await rootFolder.createChildSimple(user, {
      resourceType: 'FORM',
      name: 'new form',
      formType: 'DATABASE',
      databaseId: database.id,
      viewId: (await database.firstView()).id,
    });
    // 此时没有设置LOGO, 检查容量
    const beforeStorageUsedValue = await usage.getUsedUsageValue('STORAGES');
    expect(beforeStorageUsedValue).toBe(0);

    // 上传一个PNG
    const logoPath = `${__dirname}/files/form-logo.png`;
    const logo = await MockContext.createMockAttachment(user, logoPath);
    // 更改封面图
    await formNode.update(user, {
      resourceType: 'FORM',
      brandLogo: {
        type: 'ATTACHMENT',
        attachmentId: logo.id,
        relativePath: logo.path,
      },
    });
    // 检查现在的空间站的附件使用
    const afterUpdatedUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterUpdatedUsedUsageValue).toBe(logo.size);
    // 更改为非图片LOGO
    await formNode.update(user, {
      resourceType: 'FORM',
      brandLogo: {
        type: 'COLOR',
        color: 'BLUE',
      },
    });
    // 检查现在的空间站的附件使用
    const afterDeleteUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterDeleteUsedUsageValue).toBe(0);
  });
});

/**
 * 文件类型节点附件测试
 */
describe('file node attachment test', () => {
  /**
   * 附件类型的文件节点
   */
  test('file node with attachment flow test', async () => {
    // 初始化
    const { user, space, rootFolder } = await MockContext.initUserContext();
    // 现在的空间附件使用用量
    const entitlement = await space.getEntitlement();
    const usage = entitlement.getUsage();
    const beforeStorageUsedValue = await usage.getUsedUsageValue('STORAGES');
    expect(beforeStorageUsedValue).toBe(0);
    // 上传附件
    const filePath = `${__dirname}/files/voice.m4a`;
    const attachment = await MockContext.createMockAttachment(user, filePath);
    const downloadUrl = await attachment.getPresignedGetUrl();
    // 创建附件类型的文件节点
    const fileNode = await rootFolder.createChildSimple(user, {
      name: 'file',
      resourceType: 'FILE',
      file: {
        fileType: 'ATTACHMENT',
        attachmentId: attachment.id,
        attachment: attachment.toVO(),
        previewUrl: attachment.previewUrl ?? undefined,
        downloadUrl,
      },
    });
    // 检查现在的空间站的附件使用
    const afterUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterUsedUsageValue).toBe(attachment.size);
    // 修改成其他附件
    const newFilePath = `${__dirname}/files/test.m4a`;
    const newAttachment = await MockContext.createMockAttachment(user, newFilePath);
    const newDownloadUrl = await newAttachment.getPresignedGetUrl();
    await fileNode.update(user, {
      resourceType: 'FILE',
      file: {
        fileType: 'ATTACHMENT',
        attachmentId: newAttachment.id,
        attachment: newAttachment.toVO(),
        previewUrl: newAttachment.previewUrl ?? undefined,
        downloadUrl: newDownloadUrl,
      },
    });
    // 检查现在的空间站的附件使用
    const afterUpdateUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterUpdateUsedUsageValue).toBe(newAttachment.size);
    // 删除文件节点
    await fileNode.delete(user);
    // 检查现在的空间站的附件使用
    const afterDeleteUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterDeleteUsedUsageValue).toBe(0);
  });
});

/**
 * 空间站图片LOGO引用测试
 */
describe('space attachment logo ref test', () => {
  /**
   * 非图片LOGO更改为图片LOGO
   */
  test('un-attachment logo change to attachment logo test', async () => {
    // 初始化
    const { user, space } = await MockContext.initUserContext();
    // 现在的空间附件使用用量
    const entitlement = await space.getEntitlement();
    const usage = entitlement.getUsage();
    const beforeStorageUsedValue = await usage.getUsedUsageValue('STORAGES');
    expect(beforeStorageUsedValue).toBe(0);
    // 上传一个icon
    const iconPath = `${__dirname}/files/node-icon.png`;
    const icon = await MockContext.createMockAttachment(user, iconPath);
    // 设置icon
    await space.update(user, {
      logo: {
        type: 'ATTACHMENT',
        attachmentId: icon.id,
        relativePath: icon.path,
      },
    });
    // 检查现在的空间站的附件使用
    const afterUpdatedUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterUpdatedUsedUsageValue).toBe(icon.size);
  });
  /**
   * 图片LOGO更改为非图片LOGO
   */
  test('attachment logo change to un-attachment logo test', async () => {
    // 初始化
    const { user, space } = await MockContext.initUserContext();
    // 现在的空间附件使用用量
    const entitlement = await space.getEntitlement();
    const usage = entitlement.getUsage();
    const beforeStorageUsedValue = await usage.getUsedUsageValue('STORAGES');
    expect(beforeStorageUsedValue).toBe(0);
    // 上传一个icon
    const iconPath = `${__dirname}/files/node-icon.png`;
    const icon = await MockContext.createMockAttachment(user, iconPath);
    // 设置空间站LOGO
    const updatedSpace = await space.update(user, {
      logo: {
        type: 'ATTACHMENT',
        attachmentId: icon.id,
        relativePath: icon.path,
      },
    });
    // 检查现在的空间站的附件使用
    const afterUpdatedUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterUpdatedUsedUsageValue).toBe(icon.size);

    // 更改为非图片LOGO
    await updatedSpace.update(user, {
      logo: {
        type: 'COLOR',
        color: 'BLUE',
      },
    });

    // 检查现在的空间站的附件使用
    const afterDeleteUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(afterDeleteUsedUsageValue).toBe(0);
  });
});
