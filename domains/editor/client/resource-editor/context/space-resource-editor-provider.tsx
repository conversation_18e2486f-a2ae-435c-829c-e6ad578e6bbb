import React, { useMemo } from 'react';
import { useApiCaller } from '@bika/api-caller/context';
import { getActionTypesConfig } from '@bika/contents/config/client/automation/actions';
import { getTriggerTypesConfig } from '@bika/contents/config/client/automation/triggers';
import type { LocaleContextProps } from '@bika/contents/i18n/context';
import type { Action, Trigger } from '@bika/types/automation/bo';
import type { DatabaseField, View } from '@bika/types/database/bo';
import { ResourceEditorContext } from '@bika/types/editor/context';
import type { SpaceIntegrationType } from '@bika/types/integration/bo';
import { useNodeResourceApiContext } from '@bika/types/node/context';
import type { SpaceUIModal } from '@bika/types/space/bo';
import { useSpaceContextForce } from '@bika/types/space/context';
import {
  type EditorScreenProps,
  EditorScreenPropsSchema,
  type EditorScreenAutomationTestPreview,
} from '@bika/types/template/type';
import type { GlobalModalConfig } from '@bika/types/website/bo';
import { useGlobalContext } from '@bika/types/website/context';
import { Modal } from '@bika/ui/modal';
import { useSnackBar } from '@bika/ui/snackbar';
import { useStackNavigatorContext } from '@bika/ui/stack-navigator';

type Props = {
  children: React.ReactNode;
  screen: EditorScreenProps;
  locale: LocaleContextProps;
};

export function SpaceResourceEditorProvider(props: Props) {
  const nodeResourceApi = useNodeResourceApiContext();
  const { locale } = props;

  const actionTypesConfig = useMemo(() => getActionTypesConfig(locale), [locale]);
  const triggerTypesConfig = React.useMemo(() => getTriggerTypesConfig(locale), [locale]);

  const globalContext = useGlobalContext();
  const { t, i } = locale;
  const stackContext = useStackNavigatorContext();
  const stackRouter = stackContext.router;
  const { toast } = useSnackBar();

  const pushScreen = (screen: EditorScreenProps) => {
    stackRouter.push(screen.screenType, screen);
  };
  const { params: stackParams } = stackContext;
  const getScreen = () => EditorScreenPropsSchema.parse(stackParams);

  const spaceContext = useSpaceContextForce();
  const { trpcQuery, trpc } = useApiCaller();
  const utils = trpcQuery.useUtils();

  return (
    <ResourceEditorContext.Provider
      value={{
        pushScreen,
        getScreen,
        // locale: props.locale,
        back: () => {
          if (stackRouter.isTop()) {
            spaceContext.showUIDrawer(null);
          } else {
            stackRouter.pop();
          }
        },
        api: nodeResourceApi,
        uiHandler: {
          onClickPreview: ({ actionId, triggerId }: { actionId?: string; triggerId?: string }) => {
            const screen: EditorScreenAutomationTestPreview = {
              screenType: 'AUTOMATION_TEST_PREVIEW',
            };
            if (actionId) {
              screen.actionId = actionId;
            }
            if (triggerId) {
              screen.triggerId = triggerId;
            }
            pushScreen(screen);
          },
          onClickTrigger: (trigger: Trigger, automationId: string) => {
            console.log('click trigger', trigger);
            const triggerId = trigger.id || trigger.templateId;
            pushScreen({
              screenType: 'AUTOMATION_TRIGGER',
              triggerId: triggerId!,
              automationId,
            });
          },
          onClickNewTrigger: (automationId: string) => {
            pushScreen({
              screenType: 'AUTOMATION_TRIGGER',
              triggerId: 'new',
              automationId,
            });
          },
          onClickDeleteTrigger: (trigger: Trigger) => {
            const triggerId = trigger.id || trigger.templateId;
            Modal.show({
              type: 'error',
              title: t.delete.delete,
              content: t('delete.confirm_to_delete_content', { name: triggerTypesConfig[trigger.triggerType].label }),
              okText: t.ok,
              cancelText: t.cancel,
              onOk: async () => {
                await trpc.automation.deleteTrigger.mutate({
                  triggerId: triggerId!,
                });
                await utils.node.detail.invalidate();
                await utils.node.boInfo.invalidate();
                toast(t('delete.delete_success'), { variant: 'success' });
              },
            });
          },
          onClickAction: (action: Action, automationId: string, actionsLength: number, parentActionId?: string) => {
            const actionId = action.id || action.templateId;
            pushScreen({
              screenType: 'AUTOMATION_ACTION',
              actionId: actionId!,
              parentActionId,
              actionsLength: String(actionsLength),
              automationId,
            });
          },
          onClickNewAction: (automationId: string, parentActionId?: string) => {
            pushScreen({
              screenType: 'AUTOMATION_ACTION',
              actionId: 'new',
              parentActionId,
              automationId,
            });
          },
          onClickDeleteAction: (action: Action) => {
            const actionId = action.id || action.templateId;
            Modal.show({
              type: 'error',
              title: t.delete.delete,
              content: t('delete.confirm_to_delete_content', { name: actionTypesConfig[action.actionType].label }),
              okText: t.ok,
              cancelText: t.cancel,
              onOk: async () => {
                await trpc.automation.deleteAction.mutate({
                  actionId: actionId!,
                });
                await utils.node.detail.invalidate();
                await utils.node.boInfo.invalidate();
                toast(t('delete.delete_success'), { variant: 'success' });
              },
            });
          },
          onClickNewView: (databaseId: string) => {
            pushScreen({
              screenType: 'DATABASE_VIEW',
              viewId: 'new',
              databaseId,
            });
          },
          onClickNewField: (databaseId: string, filterFieldType?: string) => {
            pushScreen({
              screenType: 'DATABASE_FIELD',
              // nodeId
              databaseId,
              fieldId: 'new',
              filterFieldType,
            });
          },
          onClickField: (field: DatabaseField, databaseId: string) => {
            const fieldId = field.id || field.templateId;
            pushScreen({
              screenType: 'DATABASE_FIELD',
              databaseId,
              fieldId: fieldId!,
            });
          },
          onClickDeleteField: (item: DatabaseField, databaseId: string) => {
            const fieldId = item.id || item.templateId;

            Modal.show({
              type: 'error',
              title: t.resource.delete_field,
              content: t('resource.delete_field_description', { name: i(item.name) }),
              okText: t.buttons.delete,
              cancelText: t.buttons.cancel,
              onOk: async () => {
                await trpc.database.deleteField.mutate({
                  databaseId,
                  fieldId: fieldId!,
                });
                await utils.node.detail.invalidate();
                await utils.node.boInfo.invalidate();
                toast(t('resource.remove_field_success', { name: i(item.name) }), { variant: 'success' });
              },
            });
          },
          onClickView: (view: View, databaseId: string) => {
            const viewId = view.id || view.templateId;
            pushScreen({
              screenType: 'DATABASE_VIEW',
              viewId: viewId!,
              databaseId,
            });
          },
          onClickDeleteView: (item: View, databaseId: string) => {
            const viewId = item.id || item.templateId;

            Modal.show({
              type: 'error',
              title: t.resource.delete_view,
              content: t('resource.delete_view_description', { name: i(item.name) }),
              okText: t.buttons.delete,
              cancelText: t.buttons.cancel,
              onOk: async () => {
                await trpc.database.deleteView.mutate({ databaseId, viewId: viewId! });
                await utils.node.detail.invalidate();
                await utils.node.boInfo.invalidate();
                toast(t('resource.delete_view_success', { name: i(item.name) }), { variant: 'success' });
              },
            });
          },
          onSelectNewIntegration: (integrationType: SpaceIntegrationType) => {
            spaceContext.showUIModal({
              type: 'integration-detail',
              integration: {
                type: integrationType,
              },
            });
          },

          showUIModal: (modal: GlobalModalConfig | SpaceUIModal | null, context: 'SPACE' | 'GLOBAL') => {
            if (context === 'GLOBAL') {
              globalContext.showUIModal(modal as GlobalModalConfig | null);
            } else if (context === 'SPACE') {
              spaceContext.showUIModal(modal as SpaceUIModal | null);
            } else {
              throw new Error('Invalid context');
            }
          },
        },
      }}
    >
      {props.children}
    </ResourceEditorContext.Provider>
  );
}
